version: '3'

services:
  my-rabble:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=mongodb+srv://rabblewerks:<EMAIL>/rabble-connect-transition--dev
      - API_PREFIX=/api/v1
      - JWT_SECRET=G+KbPeShVmYq3t6w9z$C&E)H@McQfTjW
      - NODE_ENV=development
      - AWS_ACCESS_KEY=********************
      - AWS_SECRET=0hKEh9qs2o+lJ2Up27a9LsRtkmZI3EzNOb4wxfGp
      - S3_BUCKET=rabblehealth-v2
      - S3_REGION=us-east-2
      - EMAIL=<EMAIL>
      - POST_REPORTING_EMAIL=<EMAIL>
      - MIXPANEL_TOKEN=05e7ba37828e82ce320242acbf5a0902
      - MIXPANEL_TOKEN_V2=a8c8cadc9f1d25834b5d0b44c58fcbbd
      - COUNTRY_CODE=+1
