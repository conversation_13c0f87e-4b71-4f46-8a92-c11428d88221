import { MID } from "../../backend/src/@types/common";

export enum BlogType {
  INFO = "info",
  PODCAST = "podcast",
  VIDEOS = "videos",
  BLOGS = "blogs",
}

export enum BlogStatus {
  DRAFT = "draft",
  PUBLISHED = "published",
}

export type Tag = {
  _id?: MID;
  tag: string;
  scope?: string[];
};

export type Blog = {
  _id?: MID;
  title: string;
  author: string;
  contentHtml: string;
  editorData?: Object;

  description?: string;
  timeToReadInMins?: number;
  tags?: Tag[];
  banner?: string;
  status: BlogStatus;
  publishDate: Date;
  // diseaseTags is deprecated, use tags instead
  diseaseTags?: string[];
};

export type BlogDashboard = {
  bannerImage: string;
  title: string;
};

export enum BlogContentType {
  MARKDOWN = "markdown",
  IMAGE = "image",
}

export type BlogLayout = {
  _id?: MID;
  sectionName: string;
  image: string;
  blogs: MID[]; // Array of blog IDs
  isDeleted?: boolean;
  sequence?: number; // For ordering sections
};

export type BlogLike = {
  _id?: MID;
  blog: MID;
  user: MID;
};
