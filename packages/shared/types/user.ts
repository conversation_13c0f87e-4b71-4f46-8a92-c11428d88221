import { MID } from "../../backend/src/@types/common";
import {
  CaregiverApprovalStatus,
  CaregiverRelationTypes,
  Disease as DiseaseEnum,
} from "../enums/user";

/** User Account Types */
export interface Contact {
  countryCode: string;
  phone: string;
}

export type Role = "SUPER_ADMIN" | "ADMIN" | "MODERATOR" | "USER";

export interface UserAccount {
  _id: MID;
  username?: string;
  email?: string;
  month?: string;
  year?: string;
  age?: string;
  deviceId?: string;
  contact?: Contact;
  role: Role;
  profilePicture?: string;
  isCaregiver?: boolean;
  firstname?: string;
  lastname?: string;
  marketingConsent?: boolean;
  tncConsent?: boolean;
  privacyConsent?: boolean;
  activity?: string;
  createdAt?: Date;
  lastLoggedIn?: Date;
  lastInactiveMailSent?: Date;
  isOnboardingCompleted?: boolean;
  lastMangeImpressionMade?: [
    {
      topic: MID;
      date: Date;
      isComplete?: boolean;
      enableNotifications?: boolean;
    }
  ];
  managedBy?: {
    user: string | UserAccount;
    accountType: "caregiver";
    relationship: CaregiverRelationTypes;
    approvalStatus: CaregiverApprovalStatus;
  }[];
}

/** User Profile Types */
type Gender = "MALE" | "FEMALE" | "NON_BINARY" | "PREFER_NOT_TO_SAY";
export type Disease =
  | "cancer"
  | "boneHealth"
  | "cardiovascular"
  | "inflammation"
  | "nephrology";

export type ApplierKey =
  | "Bone Cancer"
  | "Chronic kidney disease (CKD)"
  | "Ductal Carcinoma"
  | "Lobular Carcinoma"
  | "Inflammatory Breast Cancer"
  | "unknown";

export type DiagnoseStageKey = "Ductal Carcinoma";

type Race =
  | "AMERICAN_INDIAN"
  | "ASIAN"
  | "BLACK"
  | "HAWAIIAN"
  | "ISLANDER"
  | "WHITE"
  | "PREFER_NO_SAY";

export const GENDER = {
  MALE: "male",
  FEMALE: "female",
  NON_BINARY: "non-binary",
  PREFER_NOT_TO_SAY: "prefer not to say",
} as const;
export const RACE = {
  AMERICAN_INDIAN: "American Indian or Alaska Native",
  ASIAN: "Asian",
  BLACK: "Black or African American",
  HAWAIIAN: "Native Hawaiian or Other Pacific Islander",
  WHITE: "White",
  PREFER_NO_SAY: "prefer not to say",
} as const;

export interface UserProfilePersonalDetails {
  gender: Gender;
  dob: Date;
  race: Race;
}

export interface ProfileAddress {
  city: string;
  state: string;
  zip: string;
}

export interface UserProfile {
  personal: UserProfilePersonalDetails;
  address: ProfileAddress;
  user: MID;
}

/** TODO:  Health Profile */
export interface UserHealthProfile {
  user: MID;
  disease: DiseaseEnum;
  diagnosisDate?: string;
  diagnosis?: string;
  diagnosisSubType?: string;
  diagnosisStage?: string;
  cancerJourneyStage?: string; // cancer role
  /** Except for cancer = breast cancer */
  personalizedHelp?: "Yes" | "No";
  /** Only for cancer = breast cancer */
  patientBiopsyReports?: string;
  additionalApplier?: string[];
}

export type JwtUser = UserAccount & { token: string };
