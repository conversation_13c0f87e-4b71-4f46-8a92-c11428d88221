import { MID } from "../../backend/src/@types/common";

export interface SymtomTracker {
  _id?: MID;
  symptom: string;
  symptomId?: MID;
  timestamp?: Date;
  user?: MID;
  trigger?: string;
  outcomes?: string[];
}

export interface Sypmtom {
  _id: MID;
  name: string;
  disease: string;
  user: MID | undefined;
}

export interface DailyTracker {
  _id?: MID;
  days: number;
  user: MID;
  updatedAt: Date;
}

export interface Actions {
  actionType: string;
  metadata: Record<string, any>;
  isDeleted?: boolean;
}

export interface Option {
  _id?: MID;
  value: string;
  color: string;
  isDeleted: boolean;
  externalId?: string;
  endOfQuestions?: boolean;
  carePartnerValue?: string;
  prompt?: string;
  description?: string;
  answersFromProfile?: boolean;
}

export interface Question {
  _id?: MID;
  question: string;
  subText?: string;
  type: string;
  isDeleted: boolean;
  isOptional: boolean;
  options?: Option[];
  actions?: Actions[];
  optionsDisplayMode?: string;
  triggerWithOptionId?: MID;
  externalId?: string;
  isSchedule?: boolean;
  duration?: number;
  delay?: number;
  carePartnerText?: String;
  getNextCompletedQuestion?: string;
  recurringQuestion?: boolean;
  recurringDay?: number;
  isActivityQuestion?: boolean;
}

export interface UserQuestionAnswers {
  question: MID;
  selectedOption?: [MID];
  answerText?: string;
  rawOptionData?: Option;
  aqi?: Number;
}

export interface TopicCategory {
  _id?: MID;
  name: string;
  logo: string;
}

export interface Topic {
  _id?: MID;
  name: string;
  nickname: string;
  category?: MID[];
  tags: MID[];
  questions: Question[];
  learningQuestions?: Question[];
  dailyTrackerQuestions: Question[];
  isDeleted?: boolean;
  externalId?: string;
  logo?: string;
  status?: TopicStatus;
}

export interface UserTopics {
  _id?: MID;
  user: MID;
  topic: MID;
  isCarePartner?: boolean;
  isHealthCare?: boolean;
  questionAnswers: UserQuestionAnswers[];
  isDeleted?: boolean;
  createdAt?: Date;
  userExperince?: string;
}

export interface UserDailyTracker {
  user: MID;
  topic: MID;
  answers: UserQuestionAnswers[];
  date: Date;
  completed: boolean;
  isDeleted?: boolean;
  createdAt?: Date;
}

//TODO: Move enums in enums folder
export enum Triggers {
  Laughing,
  Stress,
  "Polutioin, air quality, smoke",
  Exercise,
  "Weather/climate",
  Infection,
  Vacuuming,
  "Allergens – mold, pets, dust mites, pollen, etc.",
}

export enum Outcomes {
  "Missed School or work",
  "Doctor Visit - InPatient",
  "Doctor Visit - OutPatient",
  "Change in treatment",
}

export enum ActionTypes {
  Call = "Call",
  NextQuestion = "NextQuestion",
  NextBehaviourQuestion = "NextBehaviourQuestion",
}

export enum EventTypes {
  OptionSelect = "OptionSelect",
  OnInactiveSchedule = "OnInactiveSchedule",
}

export enum QuestionTypes {
  Select = "select",
  ColorQuestion = "Color question",
  YesNo = "Yes/No",
  "HorizontalSelect" = "HorizontalSelect",
  Emergency = "Emergency",
  Completed = "Completed",
  MultiSelect = "MultiSelect",
  Slider = "Slider",
}

export enum UserExperience {
  BEGINNER = "beginner",
  ADVANCED = "advanced",
}

export enum TopicStatus {
  Draft = "Draft",
  Published = "Published",
}

export enum TopicStatusFilter {
  All = "All",
  Draft = "Draft",
  Published = "Published",
}
