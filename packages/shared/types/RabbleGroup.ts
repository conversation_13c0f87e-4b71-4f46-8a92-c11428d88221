import { MID } from '../../backend/src/@types/common';

export type RabbleGroupType = 'public' | 'private';
export type RabbleGroup = {
  groupName: string;
  groupDescription: string;
  groupGuidelines?: string;
  privacy: RabbleGroupType;
  createdBy: MID;
  image?: string;
  deleted?: boolean;
  diseaseTags?: string[];
  tags?: string[];
  appearanceInDirectory?: boolean;
};

export type RabbleGroupUserRole = 'owner' | 'admin' | 'user';
export type RabbleGroupUser = {
  user: MID;
  rabbleGroup: MID;
  role: RabbleGroupUserRole;
};

export type RabbleInviteStatus = 'pending' | 'accepted' | 'rejected';
export type RabbleInvite = {
  rabbleGroup: MID;
  requestStatus: RabbleInviteStatus;
  requestedBy: MID;
  invitedBy: MID;
  email: string;
  phone: string;
};

export type RabbleSignupInvite = {
  email: string;
  phone: string;
  status: RabbleInviteStatus;
  invitedBy: MID;
};
