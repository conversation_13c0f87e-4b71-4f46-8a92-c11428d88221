import { MID } from '../../backend/src/@types/common';

/**
 * Connect
 */
export type PostTypes = 'CONNECT' | 'PRIVATE' | 'PUBLIC';
export type Post = {
  _id: MID;
  user: MID;
  title: string;
  image?: string;
  disabled?: boolean;
  rabbleGroup?: MID;
  postType: PostTypes;
  visibleTo?: [MID];
  isEdited?: boolean;
  isArchived?: boolean;
};

export type Like = {
  _id: MID;
  user: MID;
  post: MID;
  reactionType?: string;
};

export type Comment = {
  _id: MID;
  comment: string;
  user: MID;
  post: MID;
  repliedTo?: MID;
  isEdited?:boolean;
};

export type CommentLike = {
  _id: MID;
  user: MID;
  comment: MID;
  reactionType?: string;
};

/**
 * Report a post
 */
export type ReportPostTypes = 'post' | 'comment';

export enum ReportPostStatus {
  PENDING = 'pending',
  APPROVE = 'approved',
  REJECT = 'rejected',
}
export interface ReportPost {
  _id: MID;
  reportedBy: MID;
  post?: MID;
  comment?: MID;
  type: ReportPostTypes;
  status: ReportPostStatus;
}
