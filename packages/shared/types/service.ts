import { MID } from "../../backend/src/@types/common";

export enum ServiceStatusEnum {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export type Service = {
  // serviceId: string;
  category: MID[];
  // subCategory?: string[];
  states: string[];
  // Tags - references to BlogTagModel
  tags?: MID[];
  logo: string;
  organization: string;
  title: string;
  description: string;
  phone?: string;
  email?: string;
  website?: string;
  sequence: number;
  status: ServiceStatusEnum;
};

export type ServiceCategory = {
  title: string;
  logo: string;
  description?: string;
};
