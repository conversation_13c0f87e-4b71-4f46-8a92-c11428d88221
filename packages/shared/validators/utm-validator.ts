import { z } from "zod";

export const createUtmValidator = z.object({
  name: z.string().min(1, "name is required"),
  utm_campaign: z.string().optional(),
  utm_content: z.string().optional(),
  utm_medium: z.string().optional(),
  utm_source: z.string().optional(),
  utm_term: z.string().optional(),
});

export const updateUtmValidator = z.object({
  utmId: z.string().min(1, "utmId is required"),
  name: z.string().min(1, "name is required"),
  utm_campaign: z.string().optional(),
  utm_content: z.string().optional(),
  utm_medium: z.string().optional(),
  utm_source: z.string().optional(),
  utm_term: z.string().optional(),
});

export const deleteUtmValidator = z.object({
  utmId: z.string().min(1, "utmId is required"),
});

/** Analytics */
export const createUtmAnalyticsValidator = z.object({
  utm_campaign: z.string().optional(),
  utm_content: z.string().optional(),
  utm_medium: z.string().optional(),
  utm_source: z.string().optional(),
  utm_term: z.string().optional(),
  utm_id: z.string().min(1, "utmId is required"),
});
