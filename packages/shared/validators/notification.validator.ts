import { z } from "zod";
import { NotificationTypes } from "../enums/notification";
import { zString } from ".";

export const createNotificationValidator = z
  .object({
    notificationType: z.nativeEnum(NotificationTypes),
    content: z.string().min(1, "content is required"),
    read: z.boolean().optional(),
    userId: z.string().min(1, "userId is required"),
    metadata: z.object({}).optional(),
    requestedUserId: z.string().optional(),
  });


  export const removeNotificationValidator = z.object({
    notificationId: z.string().optional(),
    filters: z.record(z.unknown()).default({}),
  }).refine((val) => val.notificationId || Object.keys(val.filters).length > 0, {
    message: "Either notificationId or filters must be present"
  });