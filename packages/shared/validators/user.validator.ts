import { z } from "zod";
import { GENDER, RACE } from "../../shared/types/user";
import { zString } from ".";
import {
  CaregiverApprovalStatus,
  CaregiverRelationTypes,
  Disease,
} from "../../shared/enums/user";

const additionalApplierKeys = [
  "Bone Cancer",
  "Chronic kidney disease (CKD)",
  "Ductal Carcinoma",
  "Lobular Carcinoma",
  "Inflammatory Breast Cancer",
  "unknown",
];

const diagnoseStageKeys = ["Ductal Carcinoma"];

const minAge = 12;
const minDate = new Date();
minDate.setFullYear(minDate.getFullYear() - minAge);

export const requestLoginOtpValidator = z
  .object({
    email: z.string().email().optional(),
    phone: z.string().optional(),
    deviceId: z.string().optional(),
    isLogin: z.boolean().optional(),
  })
  .refine(
    (data) =>
      data.email !== undefined ||
      data.phone !== undefined ||
      data.deviceId !== undefined,
    {
      message: "At least one of email or phone must be provided",
    }
  );

export const loginValidator = z
  .object({
    email: z.string().email().optional(),
    phone: z.string().optional(),
    deviceId: z.string().optional(),
    otp: z.string(),
  })
  .refine(
    (data) =>
      data.email !== undefined ||
      data.phone !== undefined ||
      data.deviceId !== undefined,
    {
      message: "At least one of email or phone must be provided",
    }
  );

export const generateLoginOtpAndCreateUserValidator = z
  .object({
    email: z.string().email().optional(),
    phone: z.string().optional(),
    deviceId: z.string().optional(),
    isLogin: z.boolean().optional(),
  })
  .superRefine(({ email, phone, deviceId }, ctx) => {
    if (!email && !phone)
      ctx.addIssue({
        path: ["email", "phone"],
        code: z.ZodIssueCode.custom,
        message: "email or phone is required",
      });
  });

export const userFilterValidator = z.object({
  username: z.string().min(3).max(1024),
  includePatientsOnly: z.boolean().default(false).optional(),
});

export const userOrPhoneFilterValidator = z
  .object({
    email: z.string().email().optional(),
    phone: z.string().optional(),
  })
  .superRefine(({ email, phone }, ctx) => {
    if (!email && !phone)
      ctx.addIssue({
        path: ["email", "phone"],
        code: z.ZodIssueCode.custom,
        message: "email or phone is required",
      });
  });

export const requestPatientToBeCareGiverValidator = z.object({
  username: z.string().min(3).max(1024),
  relationship: z.nativeEnum(CaregiverRelationTypes),
});

export const updateCaregiverApprovalStatusValidator = z.object({
  caregiverId: zString,
  status: z.nativeEnum(CaregiverApprovalStatus),
});

export const updateUserEventsValidator = z.object({
  visitedAgainAfterSignedIn: z.boolean().optional(),
});

export const updateUserOrPatientPartialValidator = z.object({
  username: z.string().optional(),
  profilePicture: z.string().optional(),
  firstname: z.string().optional(),
  lastname: z.string().optional(),
  email: z.string().email().optional(),
  tncConsent: z.literal(true).optional(),
  marketingConsent: z.boolean().optional(),
  privacyConsent: z.literal(true).optional(),
  isCaregiver: z.boolean().optional(),
  contact: z.object({ phone: z.string().optional() }).optional(),
  userOrPatientId: zString,
  month: z.string().optional(),
  year: z.string().optional(),
  age: z.string().optional(),
  isOnboardingCompleted: z.boolean().optional(),
  activity: z.string().optional(),
});

export const updateUserOrPatientEmailValidator = z.object({
  userOrPatientId: zString,
  email: z.string().email(),
});

export const updateUserOrPatientPhoneValidator = z.object({
  userOrPatientId: zString,
  contact: z.object({ phone: z.string() }),
});

export const createUpdateUserOrPatientProfileValidator = z.object({
  personal: z
    .object({
      race: z
        .enum([
          RACE.AMERICAN_INDIAN,
          RACE.ASIAN,
          RACE.BLACK,
          RACE.HAWAIIAN,
          RACE.PREFER_NO_SAY,
          RACE.WHITE,
        ])
        .optional(),
      dob: z
        .date()
        .max(minDate, "You must be at least 12 years old")
        .optional(),
      gender: z
        .enum([
          GENDER.FEMALE,
          GENDER.MALE,
          GENDER.NON_BINARY,
          GENDER.PREFER_NOT_TO_SAY,
        ])
        .optional(),
    })
    .optional(),
  address: z
    .object({
      city: zString.optional(),
      state: zString.optional(),
      zip: zString.length(5, "should be 5 characters").optional(),
    })
    .optional(),
  userOrPatientId: zString,
});

export const requestUpdateUserOrPatientEmailPhoneOtpValidator = z
  .object({
    email: z.string().optional(),
    phone: z.string().optional(),
    updateType: z.enum(["phone", "email"]),
    userOrPatientId: zString,
  })
  .superRefine(({ email, phone }, ctx) => {
    if (!(email || phone)) {
      ctx.addIssue({
        path: ["email", "phone"],
        code: "custom",
        message: "Email or phone is required",
      });
    }
  });

export const validateUserOrPatientUpdateEmailPhoneOtpValidator = z.object({
  otp: z.string().length(4, "required"),
  userOrPatientId: zString,
});

export const createCaregiverValidator = z
  .object({
    username: zString.optional(),
    firstname: zString.optional(),
    lastname: zString.optional(),
    email: z.string().email().or(z.literal("")).optional(),
    contact: z.object({ phone: z.string().optional() }).optional(),
    tncConsent: z.literal(true),
    marketingConsent: z.boolean().optional(),
  })
  .merge(createUpdateUserOrPatientProfileValidator)
  .superRefine(({ contact, email }, ctx) => {
    if (!email && !contact?.phone) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["email"],
        message: "email or contact is required",
      });
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["contact", "phone"],
        message: "email or contact is required",
      });
    }
  });

export const revokeCareGiverAccessValidator = z.object({
  caregiverId: zString,
});

export const deleteUserDataValidator = z.object({ userId: zString });

export const bulkSmsDataValidator = z.object({
  message: zString,
  recipients: z.array(z.object({
    phoneNumbers: z.string().optional(),
    email: z.string().optional(),
  })),
  groupId: z.string().optional(),
});

export const updateUserFullProfileValidator = z.object({
  userOrPatientId: zString,

  userProfile: z.object({
    username: z.string().optional(),
    firstname: z.string().optional(),
    lastname: z.string().optional(),
    email: z.string().email().optional(),
    contact: z.object({ phone: z.string().optional() }).optional(),
  }),
  // User Profile
  personalProfile: z.object({
    personal: z.object({
      race: z.enum([
        RACE.AMERICAN_INDIAN,
        RACE.ASIAN,
        RACE.BLACK,
        RACE.HAWAIIAN,
        RACE.PREFER_NO_SAY,
        RACE.WHITE,
      ]),
      dob: z.date().max(minDate, "You must be at least 12 years old"),
      gender: z.enum([
        GENDER.FEMALE,
        GENDER.MALE,
        GENDER.NON_BINARY,
        GENDER.PREFER_NOT_TO_SAY,
      ]),
    }),
    address: z.object({
      city: zString,
      state: zString,
      zip: zString.length(5, "should be 5 characters"),
    }),
  }),
});
