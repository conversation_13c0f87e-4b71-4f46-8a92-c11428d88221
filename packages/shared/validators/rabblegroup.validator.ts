import * as z from 'zod';
import { zString } from '.';

export const RABBLEGROUP_TYPES = {
  public: 'public',
  private: 'private',
} as const;

export const RABBLEGROUP_USER_ROLES = {
  owner: 'owner',
  admin: 'admin',
  user: 'user',
} as const;

export type RabbleInviteStatus = 'pending' | 'accepted' | 'rejected';

export const RABBLEINVITE_STATUS = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
} as const;

// Group
export const createRabbleGroupValidator = z.object({
  groupName: zString,
  groupDescription: zString,
  groupGuidelines: z.string().optional(),
  privacy: z.enum([RABBLEGROUP_TYPES.private, RABBLEGROUP_TYPES.public]),
  image: z.string().optional(),
  diseaseTags: z.array(zString).optional(),
  tags: z.array(zString).optional(),
});

export const updateRabbleGroupPartialsValidator = z.object({
  id: zString,
  groupName: z.string().optional(),
  groupDescription: z.string().optional(),
  diseaseTags: z.array(zString).optional(),
  tags: z.array(zString).optional(),
  privacy: z
    .enum([RABBLEGROUP_TYPES.private, RABBLEGROUP_TYPES.public])
    .optional(),
  image: z.string().optional(),
  appearanceInDirectory: z.boolean().optional(),
});

export const searchRabbleGroupValidator = z.object({
  name: z.string(),
  diseaseTags: z.array(zString).optional(),
});

export const getDiscoverGroupsValidator = z
  .object({ diseaseTags: z.array(zString).optional() })
  .optional();

// export const getUserCreatedGroupsValidator = z.object({
//   user: zString,
// });

export const deleteRabbleGroupValidator = z.object({
  groupId: zString,
});

// User
export const promoteDemoteRabbleUserValidator = z.object({
  role: z.enum([RABBLEGROUP_USER_ROLES.admin, RABBLEGROUP_USER_ROLES.user]),
  rabbleGroupId: zString,
  targetUserId: zString,
});

// if user creates the group himself pass role as owner, or by default user role is set in schema (No need to pass user)
export const addUserToRabbleGroupValidator = z.object({
  role: z
    .enum([RABBLEGROUP_USER_ROLES.owner, RABBLEGROUP_USER_ROLES.user])
    .optional(),
  user: zString,
  rabbleGroup: zString,
});

export const getUsersInGroupValidator = z.object({
  group: zString.or(z.array(zString)),
});

export const getGroupInfoValidator = z.object({
  group: zString,
});

export const leaveGroupValidator = z.object({
  group: zString,
});

export const kickUserValidator = z.object({ userId: zString, group: zString });

// Invites

export const requestInviteValidator = z.object({
  rabbleGroup: zString,
});

export const handleRabbleInviteValidator = z.object({
  rabbleGroup: zString,
  requestedUser: zString,
  requestStatus: z.enum([
    RABBLEINVITE_STATUS.rejected,
    RABBLEINVITE_STATUS.accepted,
  ]),
  requestId: zString,
});

export const getRabbleInvitesValidator = z.object({
  rabbleGroupId: zString,
});

export const getRabbleInvitesWithEmailValidator = z.object({
  email: z.string().optional(),
  phone: z.string().optional(),
});