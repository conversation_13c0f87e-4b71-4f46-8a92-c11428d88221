import XLSX from 'xlsx';

interface Params {
  buffer: Buffer;
  sheetIndex?: number;
  transform?: (worksheet: XLSX.WorkSheet) => XLSX.WorkSheet;
}
export default function excelBufferToJson({ buffer, sheetIndex = 0, transform }: Params) {
  const workbook = XLSX.read(buffer, { type: 'buffer', cellDates: true });

  const sheetNames = workbook.SheetNames;
  let worksheet = workbook.Sheets[sheetNames[sheetIndex]];

  if (transform) worksheet = transform(worksheet);

  const data = XLSX.utils.sheet_to_json(worksheet, {
    dateNF: 'yyyy-mm-dd',
    rawNumbers: false,
  });

  return data;
}
