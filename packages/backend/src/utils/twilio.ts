import twilio from 'twilio';

const accountSid = '**********************************';
const authToken = '52b30dc9dd97b02a2250d024e9d5fb86';
export const twilio_number = '+***********';

export const twilioClient = twilio(accountSid, authToken);

// eslint-disable-next-line consistent-return
export const sendMessage = async ({ message, to }: { message: string; to: string }) => {
  try {
    await twilioClient.messages.create({
      body: message,
      to,
      from: twilio_number,
    });
  } catch (error) {
    // You can implement your fallback code here
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    return { error: true, message: error.message };
  }
};

export const sendBulkMessages = async ({ message, recipients }: { message: string; recipients: string[] }) => {
  try {
    const results = await Promise.all(
      recipients.map(async (to) => {
        try {
          const response = await twilioClient.messages.create({
            body: message,
            to,
            from: twilio_number,
          });
          return { success: true, to, sid: response.sid };
        } catch (error) {
            // @ts-ignore
          return { error: true, to, message: error.message };
        }
      })
    );
    return results;
  } catch (error) {
      // @ts-ignore
    return { error: true, message: error.message };
  }
};
