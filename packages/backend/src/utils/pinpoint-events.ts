import _ from 'lodash';
import { z } from 'zod';

import { PinpointClient, PutEventsCommand, PutEventsCommandInput } from '@aws-sdk/client-pinpoint';
import { updateUserOrPatientPartialValidator } from '../../../shared/validators/user.validator';
import { UserAccount } from '../../../shared/types/user';

const log = console;

const client = new PinpointClient({
  region: process.env.S3_REGION,
  credentials: { accessKeyId: process.env?.AWS_ACCESS_KEY || '', secretAccessKey: process.env?.AWS_SECRET || '' },
}); // Specify your AWS region
const { PINPOINT_APP_ID } = process.env;

type Events = {
  profileCompleted?: boolean;
  visitedAgainAfterSignedIn?: boolean;
};

// eslint-disable-next-line consistent-return
export async function createUserRegistrationEvent(user: z.infer<typeof updateUserOrPatientPartialValidator> | UserAccount, events?: Events) {
  if (PINPOINT_APP_ID) {
    const { firstname = '', lastname = '', email = '', contact, marketingConsent = false, username = '', isCaregiver } = user;
    const channel = contact?.phone ? 'SMS' : 'EMAIL';
    const phone =
      (contact?.phone &&
        (contact.phone.startsWith('+') ? contact.phone : [_.get(contact, 'countryCode', ''), _.get(contact, 'phone', '')].join(''))) ||
      '';
    const emailOrPhone = (phone || email) as string;
    const { profileCompleted = false, visitedAgainAfterSignedIn = false } = events || {};
    const key = emailOrPhone;
    const params: PutEventsCommandInput = {
      ApplicationId: PINPOINT_APP_ID,
      EventsRequest: {
        BatchItem: {
          [key]: {
            Endpoint: {
              Address: emailOrPhone,
              ChannelType: channel,
              Attributes: {
                isEmail: [contact?.phone ? 'false' : 'true'],
                isCaregiver: [isCaregiver ? 'true' : 'false'],
                profileCompleted: [profileCompleted ? 'true' : 'false'],
                visitedAgainAfterSignedIn: [visitedAgainAfterSignedIn ? 'true' : 'false'],
                firstname: [firstname],
                lastname: [lastname],
                phone: [phone],
                email: [email],
                marketingConsent: [_.toString(marketingConsent)],
              },
            },
            Events: {
              USER_REGISTRATION: {
                EventType: 'USER_REGISTRATION',
                Timestamp: new Date().toISOString(),
                Attributes: {
                  isEmail: contact?.phone ? 'false' : 'true',
                  marketingConsent: _.toString(marketingConsent),
                },
              },
            },
          },
        },
      },
    };

    try {
      const putEventsCommand = new PutEventsCommand(params);
      const data = await client.send(putEventsCommand);
      log.info('Successfully created the event', data);
      return data;
    } catch (err) {
      log.error('Error creating events:', err);
      throw err;
    }
  } else {
    // eslint-disable-next-line no-console
    console.warn('Skipping due to no pinpoint app id provided');
  }
}
