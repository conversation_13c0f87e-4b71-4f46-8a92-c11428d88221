import { customAlphabet, nanoid } from 'nanoid/async';
import mimeTypes from 'mime-types';
import { FileArray, UploadedFile } from 'express-fileupload';
import createHttpError from 'http-errors';
import mongoose from 'mongoose';
import nodemailer from 'nodemailer';
import Mail from 'nodemailer/lib/mailer';
import { Request } from 'express';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { z } from 'zod';
import s3 from '../startup/s3-auth';
import { generatedSignedUrlValidator } from '../../../shared/validators/lib.validators';

export const genUserId = customAlphabet('1234567890', 6);
export const genOtp = customAlphabet('1234567890', 4);
export const guestSerial = customAlphabet('1234567890', 5);
export const randomFileSuffix = customAlphabet('1234567890', 9);

/** S3 */
interface UploadFileToS3Params {
  bucket?: string;
  key: string;
  body: Buffer;
}

interface RemoveFileS3Params {
  bucket?: string;
  key: string;
}

export const uploadFileBufferToS3 = ({ bucket, key, body }: UploadFileToS3Params) =>
  s3.putObject({
    Bucket: bucket || process.env.S3_BUCKET,
    Key: key,
    Body: body,
  });

export const removeFileFromS3 = ({ bucket, key }: RemoveFileS3Params) =>
  s3.deleteObject({
    Bucket: bucket || process.env.S3_BUCKET,
    Key: key,
  });

export const createFilePath = async (prefix: string, file: UploadedFile) => {
  const ext = mimeTypes.extension(file.mimetype).toString();
  const rand = await nanoid();
  const filename = `${prefix}-${rand}.${ext}`;
  return filename;
};

const imageFileTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

interface ValidateImageOptions {
  optional?: boolean;
}

export type Files = FileArray | null | undefined;
export const validateImage = (files: Files, options?: ValidateImageOptions) => {
  if (!files?.image && !options?.optional) throw createHttpError.BadRequest('image is missing');

  const image = files?.image as UploadedFile | undefined;
  if (image === undefined) return null;
  if (!imageFileTypes.includes(image.mimetype)) throw createHttpError.BadRequest('invalid file format');

  return image;
};

export const uploadFileTOS3 = async ({ image, s3FilePath }: { image: UploadedFile | null; s3FilePath: string }) => {
  let filePath: string | undefined;

  if (image) {
    filePath = await createFilePath(s3FilePath, image);
    uploadFileBufferToS3({ key: filePath, body: image.data });
    return filePath;
  }

  return undefined;
};

export const base64ImageToS3 = async (base64: string, path: string, prefix: string) => {
  const rand = await nanoid();
  const filename = `${prefix}-${rand}.png`;
  const s3Path = `${path}/${filename}`;
  await uploadFileBufferToS3({ key: s3Path, body: Buffer.from(base64, 'base64') });

  return s3Path;
};

export const generatedSignedUrl = async ({ path, ext, prefix }: z.infer<typeof generatedSignedUrlValidator>) => {
  const rand = await nanoid();
  const filename = `${prefix}-${rand}.${ext}`;
  const s3Path = `${path}/${filename}`;

  const command = new PutObjectCommand({ Bucket: process.env.S3_BUCKET, Key: s3Path });
  const s3BaseUrl = `https://${process.env.S3_BUCKET}.s3.${process.env.S3_REGION}.amazonaws.com`;

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  return { signedUrl: await getSignedUrl(s3, command, { expiresIn: 3600 }), filename, s3Path, s3BaseUrl, s3FileUrl: `${s3BaseUrl}/${s3Path}` };
};

/** Mail */
const transporter = nodemailer.createTransport({
  host: 'email-smtp.us-east-2.amazonaws.com',
  port: 587,
  secure: false, // Use TLS
  auth: {
    user: 'AKIAYQHMIC7RF2XGFQZK',
    pass: 'BJdgCpLwBM0psVYH7ATm+TI3x2mwtnHz8KPiLBiqFqmm',
  },
});

export async function sendEmail(mailOptions: Mail.Options) {
  return transporter.sendMail({ from: process.env.EMAIL, ...mailOptions });
}

export async function sendBulkEmail(recipients: string[], subject: string, body: string) {
  await recipients.reduce(async (promise: Promise<any>, recipient: string) => {
    await promise;
    return transporter.sendMail({
      from: process.env.EMAIL,
      to: recipient, 
      subject: subject,
      text: body,
    })
  }, Promise.resolve());
}

/** Common */
export async function generateOtp(otpLength = 4) {
  const chars = '0123456789';
  let otp = '';
  for (let i = 0; i < otpLength; i += 1) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    otp += chars[randomIndex];
  }

  return otp;
}

export function pagination(cursor: number, totalItems: number, limit: number) {
  // Calculate total number of pages
  const totalPages = Math.ceil(totalItems / limit);

  // Ensure currentPage is within valid range
  // eslint-disable-next-line no-param-reassign
  cursor = Math.max(0, Math.min(cursor, totalPages));

  // Calculate next page and previous page
  const nextCursor = cursor < totalPages - 1 ? cursor + 1 : undefined;
  const prevCursor = cursor > 0 ? cursor - 1 : undefined;

  // Check if it's the last page
  const isLastPage = cursor === totalPages - 1;

  // Return pagination details as an object
  return {
    currentPage: cursor,
    nextCursor,
    prevCursor,
    isLastPage,
    totalPages,
    totalItems,
  };
}

export function getHostInfo(req: Request) {
  const { protocol, hostname } = req;
  return { protocol, hostname };
}

export function toObjectId(id: string | mongoose.Types.ObjectId) {
  if (id instanceof mongoose.Types.ObjectId) return id;
  const isValidObjectId = mongoose.Types.ObjectId.isValid(id);
  if (!isValidObjectId) throw createHttpError.BadRequest('Invalid object Id');

  return new mongoose.Types.ObjectId(id);
}

interface ImageKitParams {
  imagePath: string;
  /** transform is an array with strings
   * 1. width: w-100
   * 2. blur: bl-2
   */
  transform: string[];
}

export const imageKit = ({ imagePath, transform }: ImageKitParams) => {
  const mediaUrl = `https://ik.imagekit.io/rabblehealth${transform.length ? `/tr:${transform.join(',')}` : ''}/${imagePath}`;
  return mediaUrl;
};
