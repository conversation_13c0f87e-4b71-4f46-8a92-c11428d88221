import bcrypt from 'bcryptjs';

export async function hashPassword(password: string): Promise<string> {
  return new Promise((resolve, reject) => {
    bcrypt.genSalt(10, (err, salt) => {
      if (err) reject(err);
      else {
        bcrypt.hash(password, salt, (error, hash) => {
          if (error) reject(error);
          else resolve(hash);
        });
      }
    });
  });
}

export async function comparePasswordHash(password: string, passwordInHash: string): Promise<boolean> {
  return new Promise((resolve, reject) => {
    bcrypt.compare(password, passwordInHash, (err, res) => {
      if (err) return reject(err);
      return resolve(res);
    });
  });
}
