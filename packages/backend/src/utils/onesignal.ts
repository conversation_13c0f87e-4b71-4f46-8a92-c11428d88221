/* eslint-disable no-console */
import axios from 'axios';

export type CreateOneSignalNotification = {
  heading: string;
  message?: string;
  imageUrl?: string;
  userIds?: string[];
  metadata?: Record<string, any>;
  launchUrl?: string;
};
export const createOnesignalNotification = async ({ heading, message, imageUrl, userIds, metadata, launchUrl }: CreateOneSignalNotification) => {
  const finalHeading = heading || 'Title not set';
  const finalMessage = message || 'Message not set';
  try {
    const response = await axios.request({
      method: 'POST',
      url: 'https://api.onesignal.com/notifications',
      headers: {
        accept: 'application/json',
        Authorization: `Basic ${process.env.ONESIGNAL_REST_API_KEY}`,
        'content-type': 'application/json',
      },
      data: {
        app_id: process.env.ONESIGNAL_APPID,
        contents: { en: finalMessage ?? '' },
        headings: { en: finalHeading }, // Add title
        big_picture: imageUrl, // Add image
        include_external_user_ids: userIds,
        url: launchUrl,
        ...metadata,
      },
    });

    return response;
  } catch (ex) {
    if (ex instanceof Error) {
      console.log('ONESIGNAL_ERROR', ex.message);
    }
  }
};
