import axios from "axios";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>r<PERSON><PERSON>unt,
  User<PERSON>ealthPro<PERSON>le,
  <PERSON>r<PERSON><PERSON><PERSON><PERSON>,
  UserProfilePersonalDetails,
} from "../../../shared/types/user";
import { NotificationTypes } from "../../../shared/enums/notification";
import { createAnalytics } from "../controller/analytics.controller";

// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
export const MIXPANEL_TOKEN = process.env.MIXPANEL_TOKEN!;
export const MIXPANEL_TOKEN_V2 = process.env.MIXPANEL_TOKEN_V2!;

// Define types for event-specific properties
interface CreateUserProfile {
  $token: string;
  $distinct_id: string | number;
  $set: Partial<UserAccount> &
    Partial<UserHealthProfile> &
    Partial<UserProfilePersonalDetails> &
    Partial<ProfileAddress>;
}

type UpdateProfile = Omit<Partial<UserAccount>, "firstname" | "lastname"> & {
  first_name: string;
  last_name: string;
};

interface PostCreatedEvent {
  caption?: string;
  username: string;
  createdAt: string;
  postId: string;
  rabbleGroup?: string;
  rabbleGroupId?: string;
}

interface LikePostEvent {
  username: string;
  createdAt: string;
  postId: string;
}
interface UnLikePostEvent {
  username: string;
  createdAt: string;
  postId: string;
}

interface LikeCommentEvent {
  createdAt: string;
  commentId: string;
}
interface UnLikeCommentEvent {
  createdAt: string;
  commentId: string;
}

interface CommentPostEvent {
  username: string;
  createdAt: string;
  postId: string;
  comment: string;
}

interface ReportPostEvent {
  createdAt: string;
  postId: string;
  post?: string;
  imgUrl?: string;
  postAuthorUsername: string;
  reporterUsername: string;
}

interface ReportCommentEvent {
  createdAt: string;
  postId: string;
  postAuthor: string;
  post?: string;
  imgUrl?: string;
  comment: string;
  commentAuthor: string;
}

interface DeletePostEvent {
  createdAt: string;
  postId: string;
  post?: string;
  imgUrl?: string;
}
interface DeleteCommentEvent {
  createdAt: string;
  postId: string;
  post?: string;
  comment: string;
}

interface NotificationCreated {
  notificationType: NotificationTypes;
  content?: string;
}

type RabbleGroupCreated = Record<string, string>;
type RabbleGroupDeleted = Record<string, string>;
type RabbleGroupUpdated = Record<string, string>;
type ManageDailyTracker = Record<string, string>;
type ManageSubscriptionQuestions = Record<string, string>;
type ManageSubscriptionTopics = Record<string, string>;
type ManageUnSubscriptionTopics = Record<string, string>;
type Action = Record<string, string>;

export interface MixpanelEventTypes {
  CREATE_POST: PostCreatedEvent;
  LIKE_POST: LikePostEvent;
  UNLIKE_POST: UnLikePostEvent;
  LIKE_COMMENT: LikeCommentEvent;
  UNLIKE_COMMENT: UnLikeCommentEvent;
  COMMENT_POST: CommentPostEvent;
  REPORT_POST: ReportPostEvent;
  REPORT_COMMENT: ReportCommentEvent;
  DELETE_POST: DeletePostEvent;
  DELETE_COMMENT: DeleteCommentEvent;
  UPDATE_PROFILE: UpdateProfile;
  HEALTH_PROFILE: UserHealthProfile;
  NOTIFICATION_CREATED: NotificationCreated;
  RABBLEGROUP_CREATED: RabbleGroupCreated;
  RABBLEGROUP_DELETED: RabbleGroupDeleted;
  RABBLEGROUP_UPDATED: RabbleGroupUpdated;
  ManageUserDailyTracker: ManageDailyTracker;
  ManageSubscriptionQuestions: ManageSubscriptionQuestions;
  ACTION: Action;
  ManageSubscriptionTopics: ManageSubscriptionTopics;
  ManageUnSubscriptionTopics: ManageUnSubscriptionTopics;
}

class MixpanelTracker {
  private token: string;

  private baseUrl = "https://api.mixpanel.com/track";

  private engageUrl = "https://api.mixpanel.com/engage#profile-set";

  constructor(token: string) {
    this.token = token;
  }

  async trackEvent<T extends keyof MixpanelEventTypes>(
    event: T | string,
    properties: MixpanelEventTypes[T],
    distinct_id?: string | number,
    version?: "v1" | "v2"
  ): Promise<any> {
    const data = {
      event,
      properties: {
        token: this.token,
        ...(distinct_id && { distinct_id }),
        ...properties,
      },
    };
    const dataV2 = {
      event,
      properties: {
        token: MIXPANEL_TOKEN_V2,
        ...(distinct_id && { distinct_id }),
        ...properties,
      },
    };

    // Save analytics data to database
    try {
      // Create a unique key based on event name, user ID, and timestamp
      const timestamp = new Date().toISOString();
      const analyticsKey = `${event}_${
        distinct_id || "anonymous"
      }_${timestamp}`;

      // Save analytics data with version information
      await createAnalytics({
        key: analyticsKey,
        value: {
          event,
          ...properties,
          distinct_id: distinct_id || "anonymous",
          timestamp,
          version: version || "v1",
        },
      });
    } catch (error) {
      console.error("Failed to save analytics to database:", error);
      // Don't throw the error to avoid disrupting the main flow
    }

    if (version === "v2") {
      return Promise.all([
        axios.post(this.baseUrl, [dataV2], {
          headers: { accept: "text/plain", "Content-Type": "application/json" },
          params: { verbose: "2" },
        }),
        axios.post(this.baseUrl, [data], {
          headers: { accept: "text/plain", "Content-Type": "application/json" },
          params: { verbose: "2" },
        }),
      ]);
    }

    return axios.post(this.baseUrl, [data], {
      headers: { accept: "text/plain", "Content-Type": "application/json" },
      params: { verbose: "2" },
    });
  }

  createUserProfile(payload: CreateUserProfile) {
    return axios.post(this.engageUrl, [payload], {
      params: { verbose: "2" },
      headers: { "Content-Type": "application/json" },
    });
  }
}

export default new MixpanelTracker(MIXPANEL_TOKEN);
