/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { z } from 'zod';
import _ from 'lodash';
import { TRPCError } from '@trpc/server';
import { updateUserOrPatientPartialValidator } from '../../../shared/validators/user.validator';
import { UserAccount } from '../../../shared/types/user';
import UserAccountModel from '../models/UserAccount';

export async function checkDuplicateUserByEmailContactUsername(payload: z.infer<typeof updateUserOrPatientPartialValidator>, user?: UserAccount) {
    const { username, email, contact } = payload;
    const duplicateUser = await UserAccountModel.findOne({
        $and: _.compact([
          {
            $or: [
              {},
              ...(username ? [{ username: username.toLowerCase() }] : []),
              ...(email ? [{ email }] : []),
              ...(contact?.phone ? [{ 'contact.phone': contact?.phone }] : []),
            ],
          },
          user ? { _id: { $ne: user._id } } : undefined,
        ]),
      });
    
      // check if email is empty in both cases
      if (duplicateUser) {
        if (duplicateUser.username && duplicateUser.username === username?.toLowerCase())
          throw new TRPCError({ code: 'CONFLICT', message: 'username already in use' });
        if (duplicateUser.email && duplicateUser.email === email) throw new TRPCError({ code: 'CONFLICT', message: 'email already in use' });
        if (duplicateUser.contact?.phone && duplicateUser.contact?.phone === contact?.phone)
          throw new TRPCError({ code: 'CONFLICT', message: 'contact no already in use' });
      }
}


export async function checkDuplicateUserByQuery(query: Record<any, any>, user: UserAccount, message: string = 'email already in use') {
    const duplicateUser = await UserAccountModel.findOne({
        ...query,
        _id: { $ne: user._id },
      });
    
      if (duplicateUser) throw new TRPCError({ code: 'CONFLICT', message });
}

export async function checkDuplicateUserByPhoneAndEmail(email: string | undefined, phone: string | undefined, userId: string){
    const duplicateUser = await UserAccountModel.findOne({
        $and: [{ $or: [...(email ? [{ email }] : []), ...(phone ? [{ 'contact.phone': phone }] : [])] }, { _id: { $ne: userId } }],
      });
    
      if (duplicateUser) {
        if (email && duplicateUser.email === email) throw new TRPCError({ code: 'CONFLICT', message: 'email already in use' });
        if (phone && duplicateUser.contact?.phone === phone) throw new TRPCError({ code: 'CONFLICT', message: 'contact no already in use' });
      }
}
