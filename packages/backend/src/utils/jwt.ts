import jwt from 'jsonwebtoken';
import { UserAccount } from '../../../shared/types/user';

export function creatJwtToken(payload: unknown) {
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  if (payload) return jwt.sign(payload, process.env.JWT_SECRET!);
  return null;
}

export function decodeJwtToken(token: string) {
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  return jwt.verify(token, process.env.JWT_SECRET!) as unknown as UserAccount;
}
