import { TRPCError } from '@trpc/server';
import t from '../startup/trpc';
import { decodeJwtToken } from '../utils/jwt';
import { Role } from '../../../shared/types/user';

export const authProcedure = (roles?: Role | Role[]) => {
  const _roles = typeof roles === 'string' ? [roles] : roles;

  const authMiddleWare = t.middleware((opts) => {
    try {
      const { req } = opts.ctx;
      const authToken = req.headers['x-auth-token'];
      if (!authToken) throw new TRPCError({ code: 'FORBIDDEN' });

      const decoded = decodeJwtToken(authToken as string);

      if (!_roles?.length) return opts.next({ ctx: { ...opts.ctx, user: decoded } });

      if (_roles.includes(decoded.role)) return opts.next({ ctx: { ...opts.ctx, user: decoded } });
      throw new TRPCError({ code: 'FORBIDDEN', message: 'forbidden' });
    } catch (ex) {
      if (ex instanceof Error) throw new TRPCError({ code: 'FORBIDDEN', message: ex.message });
      return opts.next();
    }
  });

  return t.procedure.use(authMiddleWare);
};
