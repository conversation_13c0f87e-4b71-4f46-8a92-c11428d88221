/**
 * Migration script to convert service topics to tags (BlogTagModel references)
 *
 * This script:
 * 1. Finds all services with the old topics format (array of strings)
 * 2. For each service, it:
 *    a<PERSON> to find matching tags in the BlogTagModel
 *    b. Updates the service with the new tags format (array of ObjectIds)
 *
 * Run this script with: npx ts-node src/scripts/migrate-service-topics.ts
 */

import mongoose from "mongoose";
import { config } from "dotenv";
import { ServiceModel } from "../models/Service";
import { BlogTagModel } from "../models/Blog";

// Load environment variables
config();

async function migrateServiceTopics() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || "");
    console.log("Connected to MongoDB");

    // Get all services
    const services = await ServiceModel.find({}).lean();
    console.log(`Found ${services.length} services to process`);

    // Get all tags
    const allTags = await BlogTagModel.find({}).lean();
    console.log(`Found ${allTags.length} tags in the system`);

    // Create a map of tag names to tag IDs for quick lookup
    const tagNameToIdMap = new Map();
    allTags.forEach((tag) => {
      tagNameToIdMap.set(tag.tag.toLowerCase(), tag._id);
    });

    // Process each service
    let updatedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const service of services) {
      try {
        // Use any to access the old topics field
        const serviceAny = service as any;

        // Skip if no topics
        if (!serviceAny.topics || serviceAny.topics.length === 0) {
          skippedCount++;
          continue;
        }

        // Check if topics are already ObjectIds
        if (typeof serviceAny.topics[0] !== "string") {
          skippedCount++;
          continue;
        }

        // Find matching tag IDs
        const tagIds = [];
        for (const topic of serviceAny.topics) {
          // Convert to string to handle both string and ObjectId types
          const topicName = String(topic);

          // Skip if the topic is already an ObjectId (string format)
          if (/^[0-9a-fA-F]{24}$/.test(topicName)) {
            // This is already an ObjectId string, just use it directly
            tagIds.push(topicName);
            continue;
          }

          // Look for a matching tag by name
          const tagId = tagNameToIdMap.get(topicName.toLowerCase());
          if (tagId) {
            tagIds.push(tagId);
          } else {
            console.log(
              `Warning: No matching tag found for topic "${topicName}"`
            );
            // Create a new tag if needed
            try {
              // Check if a tag with this name already exists (case insensitive)
              const existingTag = await BlogTagModel.findOne({
                tag: { $regex: new RegExp(`^${topicName}$`, "i") },
              }).lean();

              if (existingTag) {
                tagIds.push(existingTag._id);
                console.log(
                  `Found existing tag "${existingTag.tag}" with ID ${existingTag._id}`
                );
                // Add to map for future lookups
                tagNameToIdMap.set(topicName.toLowerCase(), existingTag._id);
              } else {
                // Create a new tag
                const newTag = await new BlogTagModel({
                  tag: topicName,
                }).save();
                tagIds.push(newTag._id);
                console.log(
                  `Created new tag "${topicName}" with ID ${newTag._id}`
                );
                // Add to map for future lookups
                tagNameToIdMap.set(topicName.toLowerCase(), newTag._id);
              }
            } catch (err) {
              console.error(`Error creating tag for "${topicName}":`, err);
            }
          }
        }

        // Update the service
        await ServiceModel.updateOne(
          { _id: service._id },
          {
            $set: {
              tags: tagIds,
            },
          }
        );

        updatedCount++;
        console.log(
          `Updated service ${service._id}: ${tagIds.length} tags migrated`
        );
      } catch (err) {
        console.error(`Error processing service ${service._id}:`, err);
        errorCount++;
      }
    }

    console.log("\nMigration Summary:");
    console.log(`Total services: ${services.length}`);
    console.log(`Updated: ${updatedCount}`);
    console.log(`Skipped: ${skippedCount}`);
    console.log(`Errors: ${errorCount}`);
  } catch (err) {
    console.error("Migration failed:", err);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

// Run the migration
migrateServiceTopics();
