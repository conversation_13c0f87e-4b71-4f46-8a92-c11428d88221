import { endOfToday, isToday, startOfDay, startOfToday, subDays } from 'date-fns';
import UserAccountModel from '../models/UserAccount';
import { Topic } from '../../../shared/types/manage';
import { createNotification } from '../controller/notification.controller';
import { createNotificationValidator } from '../../../shared/validators/notification.validator';
import { z } from 'zod';
import { NotificationTypes } from '../../../shared/enums/notification';
import cron from 'node-cron';

type CreateNotification = z.infer<typeof createNotificationValidator>;

async function sendNotifications() {
  const daysAgo = 1;
  const targetDate = startOfDay(subDays(new Date(), daysAgo));

  const inActiveUsers = await UserAccountModel.find({
    $or: [
      { 'lastMangeImpressionMade.date': { $lte: targetDate } },
      {
        'lastMangeImpressionMade.date': { $gte: startOfToday(), $lte: endOfToday() },
        'lastMangeImpressionMade.isComplete': { $ne: true },
      },
    ],
  }).populate<{ lastMangeImpressionMade: { topic?: Topic; date: Date; isComplete: boolean; enableNotifications?: boolean }[] }>(
    'lastMangeImpressionMade.topic',
  );

  // notifications to send for users who did not attempt manage today
  const attemptNotifications: Map<string, CreateNotification[]> = new Map();

  // notifications to send for users who attempted but did not complete today
  const pendingNotifications: Map<string, CreateNotification[]> = new Map();

  inActiveUsers.forEach((user) => {
    if (!user.lastMangeImpressionMade?.length) return;
    const { lastMangeImpressionMade } = user;

    lastMangeImpressionMade.forEach(({ date, topic, enableNotifications, isComplete }) => {
      if (!topic) return;
      if (!enableNotifications) return;
      if (isToday(date) && isComplete) return;

      // attempt notifications
      if (!isToday(date)) {
        const attemptNotificationsByTopic = attemptNotifications.get(String(topic._id));

        const notificationPayload: CreateNotification = {
          content: 'How do you feel today? Click here to track ' + topic.name,
          metadata: { topicId: String(topic) },
          userId: String(user._id),
          notificationType: NotificationTypes.MANAGE_DAILY_QUESTIONS,
        };

        if (!attemptNotificationsByTopic) attemptNotifications.set(String(topic._id), [notificationPayload]);
        else attemptNotificationsByTopic.push(notificationPayload);
      }
      // pending notifications
      // else {
      //   const pendingNotificationsByTopic = attemptNotifications.get(String(topic._id));
      //   const notificationPayload: CreateNotification = {
      //     content: 'Pending Manage actions' + topic.name,
      //     userId: String(user._id),
      //     notificationType: NotificationTypes.MANAGE_DAILY_QUESTIONS,
      //     metadata: { topicId: String(topic._id) },
      //   };
      //
      //   if (!pendingNotificationsByTopic) pendingNotifications.set(String(topic), [notificationPayload]);
      //   else pendingNotificationsByTopic.push(notificationPayload);
      // }
    });
  });

  // send notifications
  const attemptNotificationsToSend: ReturnType<typeof createNotification>[] = [];
  const pendingNotificationsToSend: ReturnType<typeof createNotification>[] = [];

  attemptNotifications.forEach((value, key) => {
    value.forEach((notification) => {
      attemptNotificationsToSend.push(createNotification(notification));
    });
  });

  pendingNotifications.forEach((value, key) => {
    value.forEach((notification) => {
      pendingNotificationsToSend.push(createNotification(notification));
    });
  });

  const ATTEMPT_NOTIFICATIONS_SCHEDULE = '0 9 * * *';
  const PENDING_NOTIFICATIONS_SCHEDULE = '0 13 * * *';

  cron.schedule(ATTEMPT_NOTIFICATIONS_SCHEDULE, () => Promise.all(attemptNotificationsToSend), {
    scheduled: true,
    timezone: 'UTC',
  });

  cron.schedule(PENDING_NOTIFICATIONS_SCHEDULE, () => Promise.all(pendingNotificationsToSend), {
    scheduled: true,
    timezone: 'UTC',
  });
}

// sendNotifications();

const NOTIFY_CRON_SCHEDULE = process.env.NOTIFY_CRON_SCHEDULE;
if (NOTIFY_CRON_SCHEDULE) {
  sendNotifications();
}
