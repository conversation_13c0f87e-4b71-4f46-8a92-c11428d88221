import _ from 'lodash';
import { PinpointClient, SendMessagesCommand, SendMessagesCommandInput } from "@aws-sdk/client-pinpoint";
import cron from "node-cron";
import UserAccountModel from "../models/UserAccount";
import moment from "moment";
import { UserAccount } from "../../../shared/types/user";

const emailAddress =  process.env?.PINPOINT_EMAIL_ADDRESS || '<EMAIL>';
const orginatorPhone = process.env?.PINPOINT_ORIGINATING_NUMBER || '+***********';

const log = console;

const NOTIFY_CRON_SCHEDULE = process.env.NOTIFY_CRON_SCHEDULE;

// Configure AWS Pinpoint client
const client = new PinpointClient({ region: process.env.S3_REGION, credentials: { accessKeyId: process.env?.AWS_ACCESS_KEY || '', secretAccessKey: process.env?.AWS_SECRET || '' } }); // Specify your AWS region
const { PINPOINT_APP_ID } = process.env;

// Define the email sending function
const sendEmailOrSMS = async (user: UserAccount) => {
  const { email, contact, firstname = 'User' } = user;
  const phone = contact?.phone && (contact.phone.startsWith('+') ? contact.phone : [_.get(contact, 'countryCode', ''), _.get(contact, 'phone', '')].join('')) || '';
  if(email || phone) {
    const substitutions = {
      // Add any template variables here if needed
      'Attributes.firstname': [firstname],
    }

    const params: SendMessagesCommandInput = {
      ApplicationId: PINPOINT_APP_ID, // replace with your Pinpoint application ID
      MessageRequest: {
        Addresses: phone ? {
          [phone]: { // replace with the recipient's email address
            ChannelType: 'SMS'
          }
        } : {
          [email || '']: { // replace with the recipient's email address
            ChannelType: 'EMAIL'
          }
        },
        MessageConfiguration: phone ? {
          SMSMessage: { // SMSMessage
            MessageType: "PROMOTIONAL",
            OriginationNumber: orginatorPhone,
            Substitutions: substitutions,
          }
        } : {
          EmailMessage: {
            FromAddress: emailAddress, // replace with the sender's email address
            Substitutions: substitutions,
          }
        },
        TemplateConfiguration: { // TemplateConfiguration
          EmailTemplate: { // Template
            Name: "UserNotSignedInOrUsedAppFor30days",
            Version: "1",
          },
          SMSTemplate: { // Template
            Name: "UserNotSignedInOrUsedAppFor30days",
            Version: "1",   
          },
        }
      }
    };
  
    try {
      const command = new SendMessagesCommand(params);
      const response = await client.send(command);
      console.log('Email sent successfully:', response?.MessageResponse?.Result);
    } catch (error) {
      console.error('Failed to send email:', error);
    }
  }
};

const daysAgo = 30;

async function notifyInactiveUsers() {
    log.info('@InactiveNotifyCron: Starting the notify cron');
    const targetDate = moment().subtract(daysAgo, 'day').startOf('day');
    const users = await UserAccountModel.find({
      lastLoggedIn: { $lte: targetDate.toDate()  },
      $or: [
        { lastInactiveMailSent: { $lte: targetDate.toDate() } },
        { lastInactiveMailSent: { $exists: false } }
      ]
    });

    if(!users.length){
      log.info('@InactiveNotifyCron: No inactive users');
      return;
    }
    
    log.info(`@InactiveNotifyCron: Notifying users with ids - ${_.map(users, '_id')}`)
  
    await users.reduce(async (prev, user) => {
      await prev;
      await sendEmailOrSMS(user);
      log.info(`@InactiveNotifyCron: Notified user - ${_.get(user, '_id')}`)
      user.lastInactiveMailSent = moment().startOf('day').toDate();
      await user.save();
    }, Promise.resolve());
    console.log('Mail sent to all users...');
}

if(NOTIFY_CRON_SCHEDULE) {
  log.info(`Cron: ${NOTIFY_CRON_SCHEDULE}`)
 // Schedule the email sending task to run daily
  cron.schedule(NOTIFY_CRON_SCHEDULE, notifyInactiveUsers, {
    scheduled: true,
    timezone: "UTC"
  });  
}
