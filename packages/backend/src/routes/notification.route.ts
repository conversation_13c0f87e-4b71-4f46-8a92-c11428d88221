import t from '../startup/trpc';
import { authProcedure } from '../middleware/auth';
import { getNotifications, getNotificationsCount, removeNotification, updateReadNotification } from '../controller/notification.controller';
import { removeNotificationValidator } from '../../../shared/validators/notification.validator';

const notificationsRouter = t.router({
  getNotifications: authProcedure().query(({ ctx }) => getNotifications(ctx.user._id)),

  getNotificationCount: authProcedure().query(({ ctx }) => getNotificationsCount(ctx.user._id)),

  removeNotification: authProcedure()
    .input(removeNotificationValidator)
    .mutation(({ input }) => {
      const filters = input.notificationId ? { _id: input.notificationId } : input.filters;
      return removeNotification(filters);
    }),

  markAsReadNotification: authProcedure().mutation(({ ctx }) => {
    const userId = ctx.user._id;
    return updateReadNotification(userId);
  }),
});

export default notificationsRouter;
