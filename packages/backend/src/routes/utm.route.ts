import t from "../startup/trpc";
import {
  createUtmAnalyticsValidator,
  createUtmValidator,
  updateUtmValidator,
} from "../../../shared/validators/utm-validator";
import {
  createUtmAnalytics,
  createUtm,
  deleteUtm,
  getUtmAnalytics,
  getUtm,
  updateUtm,
  getUtmById,
} from "../controller/utm.controller";
import { z } from "zod";

export const utmRouter = t.router({
  createUtmAnalytics: t.procedure
    .input(createUtmAnalyticsValidator)
    .mutation(({ input }) => createUtmAnalytics(input)),

  getUtmAnalytics: t.procedure
    .input(z.string())
    .query(({ input }) => getUtmAnalytics(input)),

  createUtm: t.procedure
    .input(createUtmValidator)
    .mutation(({ input }) => createUtm(input)),

  updateUtm: t.procedure
    .input(updateUtmValidator)
    .mutation(({ input }) => updateUtm(input)),

  deleteUtm: t.procedure
    .input(z.string())
    .mutation(({ input }) => deleteUtm(input)),

  getUtm: t.procedure.query(() => getUtm()),

  getUtmById: t.procedure.input(z.string()).query(({ input }) => getUtmById(input)),
});
