import { TRPCError } from '@trpc/server';
import t from '../startup/trpc';
import { authProcedure } from '../middleware/auth';
import UserAccountModel, { UserProfileModel } from '../models/UserAccount';
import {
  createCaregiverValidator,
  createUpdateUserOrPatientProfileValidator,
  requestUpdateUserOrPatientEmailPhoneOtpValidator,
  updateUserOrPatientEmailValidator,
  updateUserOrPatientPhoneValidator,
  updateUserOrPatientPartialValidator,
  userFilterValidator,
  validateUserOrPatientUpdateEmailPhoneOtpValidator,
  updateCaregiverApprovalStatusValidator,
  requestPatientToBeCareGiverValidator,
  updateUserFullProfileValidator,
  revokeCareGiverAccessValidator,
  updateUserEventsValidator,
  userOrPhoneFilterValidator,
  deleteUserDataValidator,
  bulkSmsDataValidator,
} from '../../../shared/validators/user.validator';
import {
  createCareGiver,
  createUpdateUserOrPatientProfile,
  getCareGiverPatients,
  requestUpdateEmailPhoneOtp as requestUpdateUserOrPatientEmailPhoneOtp,
  updateUserOrPatientEmail,
  updateUserOrPatientPhone,
  updateUserOrPatientPartial,
  userFilter,
  validateUpdateEmailPhoneOtp,
  requestPatientToBeCareGiver,
  updateCaregiverApprovalStatus,
  updateUserFullProfile,
  revokeCareGiverAccess,
  updateUserEvents,
  userEmailOrPhoneFilter,
  deleteUserData,
  sendConnectOnboardingInvitations,
  sendInvitationRabble,
} from '../controller/user.controller';
import { zString } from '../../../shared/validators';
import { UserAccount } from '../../../shared/types/user';
import mongoose from 'mongoose';
import { z } from 'zod';
import { toObjectId } from '../utils';

const userRoute = t.router({
  requestUpdateEmailPhoneOtp: authProcedure()
    .input(requestUpdateUserOrPatientEmailPhoneOtpValidator)
    .mutation(({ ctx, input }) => requestUpdateUserOrPatientEmailPhoneOtp(input, ctx.user._id as string)),

  validateUpdateEmailPhoneOtp: authProcedure()
    .input(validateUserOrPatientUpdateEmailPhoneOtpValidator)
    .mutation(({ input, ctx }) => validateUpdateEmailPhoneOtp(input, ctx.user._id as string)),

  me: authProcedure()
    .meta({ description: 'Get logged in user information' })
    .input(zString.optional())
    .query(async ({ ctx, input }) => {
      const user = await UserAccountModel.findById(input || ctx.user._id).populate<{ 'managedBy.user': UserAccount }>('managedBy.user');
      if (!user) throw new TRPCError({ code: 'NOT_FOUND', message: 'user not found in database' });
      user.lastLoggedIn = new Date();
      user.save();
      return user?.toJSON();
    }),

  updateUserPartial: authProcedure()
    .input(updateUserOrPatientPartialValidator)
    .mutation(({ input, ctx }) => updateUserOrPatientPartial(input, ctx.user._id as string)),

  updateUserEvents: authProcedure()
    .input(updateUserEventsValidator)
    .mutation(({ input, ctx }) => updateUserEvents(input, ctx.user._id as string)),

  meOrPatientProfile: authProcedure()
    .input(zString.optional())
    .query(({ ctx, input }) => UserProfileModel.findOne({ user: input || ctx.user._id }).lean()),

  userFilter: t.procedure.input(userFilterValidator).query(({ input }) => userFilter(input)),

  userEmailOrPhoneFilter: t.procedure.input(userOrPhoneFilterValidator).query(({ input }) => userEmailOrPhoneFilter(input)),

  getAllActiveUsers: t.procedure.query(async () => {
    const users = await UserAccountModel.find({}).select('_id email contact.phone').lean();
    return users;
  }),

  createUpdateUserOrPatientProfile: authProcedure()
    .input(createUpdateUserOrPatientProfileValidator)
    .mutation(({ ctx, input }) => createUpdateUserOrPatientProfile(input, ctx.user._id.toString())),

  updateUserOrPatientEmail: authProcedure()
    .input(updateUserOrPatientEmailValidator)
    .mutation(({ ctx, input }) => updateUserOrPatientEmail(input, ctx.user._id)),

  updateUserOrPatientPhone: authProcedure()
    .input(updateUserOrPatientPhoneValidator)
    .mutation(({ ctx, input }) => updateUserOrPatientPhone(input, ctx.user._id)),

  requestPatientToBeCareGiver: authProcedure()
    .input(requestPatientToBeCareGiverValidator)
    .mutation(({ input, ctx }) => requestPatientToBeCareGiver(input, ctx.user._id)),

  updateCaregiverApprovalStatus: authProcedure()
    .input(updateCaregiverApprovalStatusValidator)
    .mutation(({ input, ctx }) => updateCaregiverApprovalStatus(ctx.user._id, input.caregiverId, input.status)),

  createCareGiver: authProcedure()
    .input(createCaregiverValidator)
    .mutation(({ input, ctx }) => createCareGiver(input, ctx.user._id)),

  getCareGiverPatients: authProcedure().query(({ ctx }) => getCareGiverPatients(ctx.user._id as string)),

  updateUserFullProfile: authProcedure()
    .input(updateUserFullProfileValidator)
    .mutation(({ input, ctx }) => updateUserFullProfile(input, ctx.user._id)),

  revokeCareGiverAccess: authProcedure()
    .input(revokeCareGiverAccessValidator)
    .mutation(({ input, ctx }) => revokeCareGiverAccess(input.caregiverId, ctx.user._id)),

  deleteUserData: authProcedure()
    .input(deleteUserDataValidator)
    .mutation(({ input, ctx }) => deleteUserData(input.userId)),

  sendBulkSms: authProcedure()
    .input(bulkSmsDataValidator)
    .mutation(({ input, ctx }) => sendConnectOnboardingInvitations(input, ctx.user._id)),

  sendInvitationRabble: authProcedure()
    .input(bulkSmsDataValidator)
    .mutation(({ input, ctx }) => sendInvitationRabble(input, ctx.user._id)),

  getUserById: authProcedure()
    .input(z.string().optional())
    .mutation(({ ctx, input }) => {
      console.log({ input });
      const userId = input ? toObjectId(input) : ctx.user._id;

      return UserAccountModel.findById(userId).populate<{ 'managedBy.user': UserAccount }>('managedBy.user');
    }),
});

export default userRoute;
