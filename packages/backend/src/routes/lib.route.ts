import { healthProfileOptions } from '../../constants/diseases';
import { userProfileOptions } from '../../constants/profile';
import { states } from '../../constants/states';
import { authProcedure } from '../middleware/auth';
import t from '../startup/trpc';
import { generatedSignedUrl } from '../utils';
import { generatedSignedUrlValidator } from '../../../shared/validators/lib.validators';
import axios from 'axios';
import { format } from 'date-fns';
import { z } from 'zod';

const getAqiData = async ({ lat, lng, date }: { lat: number; lng: number; date: Date }) => {
  const { data } = await axios.get(
    `https://www.airnowapi.org/aq/forecast/latLong/?format=application/json&latitude=${lat}&longitude=${lng}&date=${format(
      date,
      'yyyy-MM-dd',
    )}&distance=25&API_KEY=D9E69C95-145B-453E-8AB2-F46D413AD12D`,
  );
  return data;
};

const getLocationFromIP = async (ip?: string) => {
  try {
    const { data } = await axios.get('https://ipapi.co/json/');
    return data;
  } catch (error) {
    console.error('Error fetching location:', error);
    throw new Error('Unable to retrieve IP location data.');
  }
};

export const libRouter = t.router({
  servicesS3SignedUrl: authProcedure()
    .input(generatedSignedUrlValidator)
    .mutation(({ input }) => generatedSignedUrl(input)),

  healthProfileOptions: t.procedure.query(() => healthProfileOptions),

  userProfileOptions: authProcedure().query(() => userProfileOptions),
  states: t.procedure.query(() => states),
  aqiData: t.procedure.input(z.object({ lat: z.number(), lng: z.number(), date: z.date() })).mutation(async ({ input }) => {
    const { lat, lng, date } = input;
    const aqiData = await getAqiData({ lat, lng, date });
    return aqiData;
  }),
  locationFromIp: t.procedure.input(z.object({})).query(async ({}) => {
    return getLocationFromIP();
  }),
});
