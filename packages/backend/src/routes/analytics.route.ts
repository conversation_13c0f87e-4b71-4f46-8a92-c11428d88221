import t from "../startup/trpc";
import { z } from "zod";
import { createAnalytics } from "../controller/analytics.controller";

/**
 * Router for analytics-related endpoints
 */
export const analyticsRouter = t.router({
  /**
   * Create a new analytics entry
   */
  createAnalytics: t.procedure
    .input(
      z.object({
        key: z.string().min(1),
        value: z.any()
      })
    )
    .mutation(({ input }) => {
      // Ensure value is not undefined to satisfy TypeScript
      return createAnalytics({
        key: input.key,
        value: input.value ?? null
      });
    }),
});

export default analyticsRouter;
