import t from "../startup/trpc";
import { authProcedure } from "../middleware/auth";
import { z } from "zod";
import {
  getTopicList,
  getUserTopics,
  upsertUserTopic,
  getUserDailyTrackers,
  upsertUserDailyTracker,
  unsubscribeUserTopic,
  getBehaviourAnswerDays,
  getNextQuestion,
  getCalenderEvents,
  getCalenderWeeksAndColor,
  clearCache,
  getStreak,
  getProgressReportHtml,
} from "../controller/manage/manage.controller";
import {
  createTopic,
  updateTopic,
  getTopicById,
  deleteTopic,
} from "../controller/manage/manage-authoring.controller";
import {
  getAllTopicCategories,
  createTopicCategory,
  updateTopicCategory,
  deleteTopicCategory,
} from "../controller/manage/topic-category.controller";
import {
  getBehaviourAnswerDaysValidator,
  getCalenderEventsValidator,
  getNextQuestionValidator,
  getUserDailyTrackerValidator,
  getUserTopicsValidator,
  streakValidator,
  topicListValidator,
  unsubscribeUserTopicValidator,
  upsertUserDailyTrackerValidator,
  upsertUserTopicValidator,
} from "../../../shared/validators/manage.validator";

const manageRouter = t.router({
  getTopics: authProcedure()
    .input(topicListValidator)
    .query(({ input }) => getTopicList(input)),

  getUserTopics: authProcedure()
    .input(getUserTopicsValidator)
    .query(({ input, ctx }) => getUserTopics(input, ctx.user._id.toString())),

  upsertUserTopic: authProcedure()
    .input(upsertUserTopicValidator)
    .mutation(({ input, ctx }) =>
      upsertUserTopic(input, ctx.user._id.toString())
    ),

  getUserDailyTrackers: authProcedure()
    .input(getUserDailyTrackerValidator)
    .query(({ input, ctx }) =>
      getUserDailyTrackers(input, ctx.user._id.toString())
    ),

  upsertUserDailyTracker: authProcedure()
    .input(upsertUserDailyTrackerValidator)
    .mutation(({ input, ctx }) =>
      upsertUserDailyTracker(input, ctx.user._id.toString())
    ),

  getBehaviourAnswerDays: authProcedure()
    .input(getBehaviourAnswerDaysValidator)
    .query(({ input, ctx }) =>
      getBehaviourAnswerDays(input, ctx.user._id.toString())
    ),

  getNextQuestion: authProcedure()
    .input(getNextQuestionValidator)
    .mutation(({ input, ctx }) =>
      getNextQuestion(input, ctx.user._id.toString())
    ),

  getCalenderEvents: authProcedure()
    .input(getCalenderEventsValidator)
    .mutation(({ input, ctx }) =>
      getCalenderEvents(input, ctx.user._id.toString())
    ),

  getCalenderWeeksAndColor: authProcedure()
    .input(getCalenderEventsValidator)
    .mutation(({ input, ctx }) =>
      getCalenderWeeksAndColor(input, ctx.user._id.toString())
    ),

  unsubscribeUserTopic: authProcedure()
    .input(unsubscribeUserTopicValidator)
    .mutation(({ input, ctx }) =>
      unsubscribeUserTopic(input, ctx.user._id.toString())
    ),

  clearCache: t.procedure.query(() => clearCache()),

  getStreak: authProcedure()
    .input(streakValidator)
    .query(({ ctx, input }) =>
      getStreak(ctx.user._id.toString(), input.topicId)
    ),

  getProgressReport: authProcedure()
    .input(getCalenderEventsValidator)
    .mutation(({ input, ctx }) =>
      getProgressReportHtml(input, ctx.user._id.toString())
    ),

  // Topic authoring endpoints
  createTopic: authProcedure()
    .input(
      z.object({
        name: z.string(),
        nickname: z.string(),
        category: z.array(z.string()).optional(),
        tags: z.array(z.string()),
        questions: z.array(z.any()),
        learningQuestions: z.array(z.any()).optional(),
        dailyTrackerQuestions: z.array(z.any()),
        externalId: z.string().optional(),
        isDeleted: z.boolean().optional(),
        logo: z.string().optional(),
      })
    )
    .mutation(({ input }) => createTopic(input)),

  updateTopic: authProcedure()
    .input(
      z.object({
        _id: z.string(),
        name: z.string().optional(),
        nickname: z.string().optional(),
        logo: z.string().optional(),
        category: z.array(z.string()).optional(),
        tags: z.array(z.string()).optional(),
        questions: z.array(z.any()).optional(),
        learningQuestions: z.array(z.any()).optional(),
        dailyTrackerQuestions: z.array(z.any()).optional(),
        externalId: z.string().optional(),
        isDeleted: z.boolean().optional(),
      })
    )
    .mutation(({ input }) => {
      const { _id, ...topicData } = input;
      return updateTopic(_id, topicData);
    }),

  // TODO: Add roles
  getTopicById: authProcedure()
    .input(z.string())
    .query(({ input }) => getTopicById(input)),

  // TODO: Add roles
  deleteTopic: authProcedure()
    .input(z.string())
    .mutation(({ input }) => deleteTopic(input)),

  // Topic category endpoints
  getTopicCategories: authProcedure().query(() => getAllTopicCategories()),

  // TODO: Add roles
  createTopicCategory: authProcedure()
    .input(
      z.object({
        name: z.string(),
        logo: z.string(),
      })
    )
    .mutation(({ input }) => createTopicCategory(input)),

  // TODO: Add roles
  updateTopicCategory: authProcedure()
    .input(
      z.object({
        _id: z.string(),
        name: z.string().optional(),
        logo: z.string().optional(),
      })
    )
    .mutation(({ input }) => {
      const { _id, ...categoryData } = input;
      return updateTopicCategory(_id, categoryData);
    }),

  // TODO: Add roles
  deleteTopicCategory: authProcedure()
    .input(z.string())
    .mutation(({ input }) => deleteTopicCategory(input)),
});

export default manageRouter;
