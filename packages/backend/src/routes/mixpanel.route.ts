import t from '../startup/trpc';
import mixpanel from '../utils/mixpanel';
import { createActionEventValidator } from '../../../shared/validators/mixpanel.validator';
import _ from 'lodash';

export const mixpanelRouter = t.router({
  createActionEvent: t.procedure.input(createActionEventValidator).mutation(async ({ input }) => {
    await mixpanel.trackEvent(input.action, { action: input.action, ...{ ...input.metadata } }, input.userId);

    return { status: 'ok' };
  }),
});
