import t from '../startup/trpc';
import { appUpdateValidator } from '../../../shared/validators/app-update.validator';
import { AppUpdateModel } from '../models/AppUpdate';
import { authProcedure } from '../middleware/auth';

/** Validators */

export const appUpdateRouter = t.router({
  appver: t.procedure.query(() => AppUpdateModel.findOne({}).lean()),
  setAppver: authProcedure()
    .input(appUpdateValidator)
    .mutation(({ input }) => {
      console.log(input);
      return AppUpdateModel.updateOne({}, input, { upsert: true, new: true });
    }),
});
