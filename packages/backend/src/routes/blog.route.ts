import { z } from "zod";
import { zString } from "../../../shared/validators";
import {
  createBlogTagValidator,
  updateBlogTagValidator,
  manageBlogValidator,
  getBlogsValidator,
  manageBlogsDashboardValidator,
  getBlogValidator,
  getTagsValidator,
  bulkUpdateBlogTagsValidator,
  bulkUpdateBlogsValidator,
  manageBlogLayoutValidator,
  getBlogLayoutsValidator,
  getBlogLayoutValidator,
  deleteBlogLayoutValidator,
  blogLikeValidator,
  getBlogLikeStatusValidator,
} from "../../../shared/validators/blog";
import {
  manageBlog,
  createBlogTag,
  updateBlogTag,
  getTags,
  getBlogs,
  manageBlogDashboard,
  getBlog,
  deleteBlogTag,
  bulkUpdateBlogTags,
  bulkUpdateBlogs,
  manageBlogLayout,
  getBlogLayouts,
  getBlogLayout,
  deleteBlogLayout,
  likeBlog,
  getFavoritedBlogs,
  getBlogLikeStatus,
} from "../controller/blog.controller";
import { authProcedure } from "../middleware/auth";
import { BlogDashboardModel, BlogModel } from "../models/Blog";
import t from "../startup/trpc";

export const blogRouter = t.router({
  tags: t.procedure
    .input(getTagsValidator)
    .query(({ input }) => getTags(input)),
  createBlogTag: t.procedure
    .input(createBlogTagValidator)
    .mutation(({ input }) => createBlogTag(input)),
  updateBlogTag: t.procedure
    .input(updateBlogTagValidator)
    .mutation(({ input }) => updateBlogTag(input)),
  createBlog: t.procedure
    .input(manageBlogValidator)
    .mutation(({ input }) => manageBlog(input)),
  getBlogs: t.procedure
    .input(getBlogsValidator)
    .query(({ input }) => getBlogs(input)),
  deleteBlog: t.procedure
    .input(zString)
    .mutation(({ input }) => BlogModel.findByIdAndDelete(input)),
  getBlog: t.procedure
    .input(getBlogValidator)
    .query(({ input }) => getBlog(input)),
  manageBlogDashboard: t.procedure
    .input(manageBlogsDashboardValidator)
    .mutation(({ input }) => manageBlogDashboard(input)),
  getBlogDashboard: t.procedure.query(() =>
    BlogDashboardModel.findOne().lean()
  ),
  deleteBlogTag: t.procedure
    .input(z.string())
    .mutation(({ input }) => deleteBlogTag(input)),
  bulkUpdateBlogTags: t.procedure
    .input(bulkUpdateBlogTagsValidator)
    .mutation(({ input }) => bulkUpdateBlogTags(input)),
  bulkUpdateBlogs: t.procedure
    .input(bulkUpdateBlogsValidator)
    .mutation(({ input }) => bulkUpdateBlogs(input)),

  // Blog Layout routes
  createBlogLayout: t.procedure
    .input(manageBlogLayoutValidator)
    .mutation(({ input }) => manageBlogLayout(input)),
  updateBlogLayout: t.procedure
    .input(manageBlogLayoutValidator)
    .mutation(({ input }) => manageBlogLayout(input)),
  getBlogLayouts: t.procedure
    .input(getBlogLayoutsValidator)
    .query(({ input }) => getBlogLayouts(input)),
  getBlogLayout: t.procedure
    .input(getBlogLayoutValidator)
    .query(({ input }) => getBlogLayout(input)),
  deleteBlogLayout: t.procedure
    .input(deleteBlogLayoutValidator)
    .mutation(({ input }) => deleteBlogLayout(input)),

  // Blog Like routes
  likeBlog: authProcedure()
    .input(blogLikeValidator)
    .mutation(async ({ input, ctx }) => await likeBlog(input, ctx.user._id)),

  getFavoritedBlogs: authProcedure().query(({ ctx }) =>
    getFavoritedBlogs(ctx.user._id)
  ),

  getBlogLikeStatus: authProcedure()
    .input(getBlogLikeStatusValidator)
    .query(({ input, ctx }) => getBlogLikeStatus(input.blog, ctx.user._id)),
});
