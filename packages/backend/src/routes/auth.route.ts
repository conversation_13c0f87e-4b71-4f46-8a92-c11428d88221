import t from '../startup/trpc';
import { loginValidator, requestLoginOtpValidator } from '../../../shared/validators/user.validator';
import { generateLoginOtpAndCreateUser, validateLoginOtp } from '../controller/user.controller';

const authRouter = t.router({
  requestLoginOtp: t.procedure.input(requestLoginOtpValidator).mutation(({ input }) => generateLoginOtpAndCreateUser(input)),
  validateLoginOtp: t.procedure.input(loginValidator).mutation(({ input }) => validateLoginOtp(input)),
});

export default authRouter;
