import { z } from "zod";

import {
  createUtmAnalyticsValidator,
  createUtmValidator,
  updateUtmValidator,
} from "../../../shared/validators/utm-validator";

import UtmAnalyticsModel, { UtmModel } from "../models/Utm";

export const createUtmAnalytics = async (
  payload: z.infer<typeof createUtmAnalyticsValidator>
) => {
  const utmAnalytics = await UtmAnalyticsModel.create(payload);
  return utmAnalytics.toJSON();
};

export const getUtmAnalytics = async (utmId: string) => {
  const utmAnalytics = await UtmAnalyticsModel.find({ utm_id: utmId }).lean();
  return utmAnalytics;
};

// CRUD UTMs

export const createUtm = async (
  payload: z.infer<typeof createUtmValidator>
) => {
  const utm = await UtmModel.create(payload);
  return utm.toJSON();
};

export const getUtm = async () => {
  const utm = await UtmModel.find().lean();
  return utm;
};

export const getUtmById = async (utmId: string) => {
  const utm = await UtmModel.findById(utmId).lean();
  if (!utm) throw new Error("UTM not found");
  return utm;
};

export const updateUtm = async (
  payload: z.infer<typeof updateUtmValidator>
) => {
  const utm = await UtmModel.findByIdAndUpdate(payload.utmId, payload, {
    new: true,
  });
  if (!utm) throw new Error("UTM not found");
  return utm.toJSON();
};

export const deleteUtm = async (utmId: string) => {
  const utm = await UtmModel.findByIdAndDelete(utmId);
  if (!utm) throw new Error("UTM not found");
  return utm.toJSON();
};
