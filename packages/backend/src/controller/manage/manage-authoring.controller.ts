import { TRPCError } from "@trpc/server";
import { Topic } from "../../../../shared/types/manage";
import TopicModel from "../../models/Topics";

/**
 * Create a new topic
 * @param topicData - The topic data to create
 * @returns The created topic
 */
export const createTopic = async (topicData: Omit<Topic, "_id">) => {
  try {
    // Create a new topic
    const topic = new TopicModel(topicData);
    const savedTopic = await topic.save();

    return savedTopic;
  } catch (error) {
    console.error("Error creating topic:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to create topic",
      cause: error,
    });
  }
};

/**
 * Update an existing topic
 * @param topicId - The ID of the topic to update
 * @param topicData - The updated topic data
 * @returns The updated topic
 */
export const updateTopic = async (
  topicId: string,
  topicData: Partial<Topic>
) => {
  try {
    // Check if topic exists
    const existingTopic = await TopicModel.findById(topicId);
    if (!existingTopic) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Topic not found",
      });
    }

    // Update the topic
    const updatedTopic = await TopicModel.findByIdAndUpdate(
      topicId,
      { $set: topicData },
      { new: true, runValidators: true }
    );

    if (!updatedTopic) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to update topic",
      });
    }

    return updatedTopic;
  } catch (error) {
    console.error("Error updating topic:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to update topic",
      cause: error,
    });
  }
};

/**
 * Get a topic by ID
 * @param topicId - The ID of the topic to retrieve
 * @returns The topic
 */
export const getTopicById = async (topicId: string) => {
  try {
    const topic = await TopicModel.findById(topicId).lean();
    if (!topic) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Topic not found",
      });
    }
    return topic;
  } catch (error) {
    console.error("Error getting topic:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to get topic",
      cause: error,
    });
  }
};

/**
 * Delete a topic (soft delete)
 * @param topicId - The ID of the topic to delete
 * @returns The deleted topic
 */
export const deleteTopic = async (topicId: string) => {
  try {
    // Check if topic exists
    const existingTopic = await TopicModel.findById(topicId);
    if (!existingTopic) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Topic not found",
      });
    }

    // Soft delete the topic
    const deletedTopic = await TopicModel.findByIdAndUpdate(
      topicId,
      { $set: { isDeleted: true } },
      { new: true }
    );

    if (!deletedTopic) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete topic",
      });
    }

    return deletedTopic;
  } catch (error) {
    console.error("Error deleting topic:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to delete topic",
      cause: error,
    });
  }
};
