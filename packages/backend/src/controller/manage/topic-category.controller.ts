import { TRPCError } from "@trpc/server";
import { TopicCategory } from "../../../../shared/types/manage";
import { TopicCategoryModel } from "../../models/Topics";

/**
 * Get all topic categories
 * @returns List of all topic categories
 */
export const getAllTopicCategories = async () => {
  try {
    const categories = await TopicCategoryModel.find().lean();
    return categories;
  } catch (error) {
    console.error("Error fetching topic categories:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch topic categories",
      cause: error,
    });
  }
};

/**
 * Create a new topic category
 * @param categoryData - The category data to create
 * @returns The created category
 */
export const createTopicCategory = async (categoryData: Omit<TopicCategory, "_id">) => {
  try {
    const category = new TopicCategoryModel(categoryData);
    const savedCategory = await category.save();
    return savedCategory;
  } catch (error) {
    console.error("Error creating topic category:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to create topic category",
      cause: error,
    });
  }
};

/**
 * Update an existing topic category
 * @param id - The ID of the category to update
 * @param categoryData - The updated category data
 * @returns The updated category
 */
export const updateTopicCategory = async (id: string, categoryData: Partial<TopicCategory>) => {
  try {
    const updatedCategory = await TopicCategoryModel.findByIdAndUpdate(
      id,
      { $set: categoryData },
      { new: true }
    );
    
    if (!updatedCategory) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Topic category not found",
      });
    }
    
    return updatedCategory;
  } catch (error) {
    if (error instanceof TRPCError) throw error;
    
    console.error("Error updating topic category:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to update topic category",
      cause: error,
    });
  }
};

/**
 * Delete a topic category
 * @param id - The ID of the category to delete
 * @returns The deleted category
 */
export const deleteTopicCategory = async (id: string) => {
  try {
    const deletedCategory = await TopicCategoryModel.findByIdAndDelete(id);
    
    if (!deletedCategory) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Topic category not found",
      });
    }
    
    return deletedCategory;
  } catch (error) {
    if (error instanceof TRPCError) throw error;
    
    console.error("Error deleting topic category:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to delete topic category",
      cause: error,
    });
  }
};
