import { Types } from "mongoose";
import { z } from "zod";
import {
  createCategoryValidator,
  createOrUpdateServiceValidator,
  updateServiceSequenceValidator,
  bulkUpdateServicesValidator,
} from "../../../shared/validators/service.validator";
import { ServiceCategoryModel, ServiceModel } from "../models/Service";

export const createCategory = (
  payload: z.infer<typeof createCategoryValidator>
) => new ServiceCategoryModel(payload).save();
export const createUpdateService = async ({
  _id,
  ...payload
}: z.infer<typeof createOrUpdateServiceValidator>) => {
  // Process the payload
  const processedPayload = { ...payload };

  // Handle tags - ensure we're using ObjectIds
  if (payload.tags && payload.tags.length > 0) {
    // Filter out valid ObjectId strings and convert them to ObjectIds
    const validTags: any[] = [];
    for (const tagId of payload.tags) {
      // Check if it's a valid ObjectId string
      if (typeof tagId === "string" && /^[0-9a-fA-F]{24}$/.test(tagId)) {
        try {
          validTags.push(new Types.ObjectId(tagId));
        } catch (err) {
          console.error(`Failed to convert ${tagId} to ObjectId:`, err);
        }
      } else if (typeof tagId !== "string") {
        // If it's already an ObjectId, just use it
        validTags.push(tagId);
      }
      // Skip strings that aren't valid ObjectIds
    }

    (processedPayload as any).tags = validTags;
  }

  // Create or update the service
  if (!_id) {
    await ServiceModel.create({
      ...processedPayload,
      // @ts-ignore
      category: [...new Set(processedPayload.category)],
    });
  } else {
    await ServiceModel.findOneAndUpdate(
      { _id },
      {
        ...processedPayload,
        // @ts-ignore
        category: [...new Set(processedPayload.category)],
      },
      { upsert: true }
    );
  }

  return { status: "OK" };
};

export const updateServiceSequence = async (
  payload: z.infer<typeof updateServiceSequenceValidator>
) => {
  const bulkOperation = payload.map((data) => ({
    updateOne: {
      filter: { _id: new Types.ObjectId(data.serviceId) },
      update: { $set: { sequence: data.sequence } },
    },
  }));

  await ServiceModel.bulkWrite(bulkOperation);

  return { status: "OK" };
};

export const bulkUpdateServices = async (
  payload: z.infer<typeof bulkUpdateServicesValidator>
) => {
  const { serviceIds, tagIds, status } = payload;

  // Validate that at least one field is provided for update
  if (!tagIds && !status) {
    throw new Error(
      "At least one field (tags or status) must be provided for update"
    );
  }

  // Validate that all service IDs exist
  const existingServices = await ServiceModel.find({
    _id: { $in: serviceIds },
  });
  if (existingServices.length !== serviceIds.length) {
    throw new Error("One or more services not found");
  }

  // Build update object with only provided fields
  const updateData: any = {};
  if (tagIds !== undefined) {
    // Convert tag IDs to ObjectIds
    updateData.tags = tagIds.map((id) => new Types.ObjectId(id));
  }
  if (status !== undefined) updateData.status = status;

  // Update all specified services
  const result = await ServiceModel.updateMany(
    { _id: { $in: serviceIds.map((id) => new Types.ObjectId(id)) } },
    { $set: updateData }
  );

  // Build success message based on what was updated
  const updatedFields = [];
  if (tagIds !== undefined) updatedFields.push("tags");
  if (status !== undefined) updatedFields.push("status");

  return {
    success: true,
    modifiedCount: result.modifiedCount,
    message: `Successfully updated ${
      result.modifiedCount
    } service(s) with new ${updatedFields.join(", ")}`,
  };
};
