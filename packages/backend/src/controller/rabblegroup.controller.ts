/* eslint-disable @typescript-eslint/no-shadow */
import * as z from "zod";
import { TRPCError } from "@trpc/server";
import _ from "lodash";
import {
  addUserToRabbleGroupValidator,
  createRabbleGroupValidator,
  deleteRabbleGroupValidator,
  getGroupInfoValidator,
  getRabbleInvitesValidator,
  getRabbleInvitesWithEmailValidator,
  getUsersInGroupValidator,
  handleRabbleInviteValidator,
  kickUserValidator,
  leaveGroupValidator,
  promoteDemoteRabbleUserValidator,
  requestInviteValidator,
  searchRabbleGroupValidator,
  updateRabbleGroupPartialsValidator,
} from "../../../shared/validators/rabblegroup.validator";
import {
  RabbleGroupModel,
  RabbleGroupUserModel,
  RabbleInviteModel,
} from "../models/RabbleGroup";
import { MID } from "../@types/common";
import { UserAccount } from "../../../shared/types/user";
import { createNotification } from "./notification.controller";
import { NotificationTypes } from "../../../shared/enums/notification";
import { CaregiverApprovalStatus } from "../../../shared/enums/user";

import log from "../utils/logger";
import { getUserJoinedGroupsAggregation } from "../models/aggregations/connect.aggregation";
import UserAccountModel from "../models/UserAccount";
import mixpanel from "../utils/mixpanel";
import { createOnesignalNotification } from "../utils/onesignal";

const getRabbleGroupById = async (rabbleGroupId: string) => {
  const rabbleGroup = await RabbleGroupModel.findById(rabbleGroupId);
  if (!rabbleGroup)
    throw new TRPCError({ code: "NOT_FOUND", message: "group not found" });
  return rabbleGroup;
};

// Rabble Users
export function addUserToGroup(
  payload: z.infer<typeof addUserToRabbleGroupValidator>
) {
  return RabbleGroupUserModel.findOneAndUpdate(payload, payload, {
    upsert: true,
    new: true,
  }).lean();
}

export const joinPublicRabbleGroup = async (
  payload: z.infer<typeof addUserToRabbleGroupValidator>
) => {
  const rabbleGroup = await getRabbleGroupById(payload.rabbleGroup);
  if (rabbleGroup.privacy !== "public")
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "not a public rabble group",
    });

  return addUserToGroup(payload);
};

export const promoteOrDemoteUser = async (
  {
    rabbleGroupId,
    role,
    targetUserId,
  }: z.infer<typeof promoteDemoteRabbleUserValidator>,
  userId: MID
) => {
  const users = await RabbleGroupUserModel.find({
    user: [targetUserId, userId],
    rabbleGroup: rabbleGroupId,
  });

  const user = users.find((_) => _.user.toString() === userId);
  const targetUser = users.find((_) => _.user.toString() === targetUserId);

  if (!user || !targetUser)
    throw new TRPCError({
      message: !user ? "user not found" : "target user not found",
      code: "NOT_FOUND",
    });
  if (targetUser.role === "owner")
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "can't promote/demote owner",
    });
  if (!(user?.role === "admin" || user?.role === "owner"))
    throw new TRPCError({ code: "UNAUTHORIZED", message: "unauthorized" });

  if (targetUser?.role) targetUser.role = role;
  return targetUser?.save();
};

export const getUsersInGroup = async ({
  group,
}: z.infer<typeof getUsersInGroupValidator>) => {
  const users = await RabbleGroupUserModel.find({
    rabbleGroup: Array.isArray(group) ? { $in: group } : group,
  })
    .populate<{ user: UserAccount }>("user")
    .lean();
  if (!users)
    throw new TRPCError({ message: "Group not found", code: "NOT_FOUND" });
  return users;
};

export const leaveGroup = async (
  { group }: z.infer<typeof leaveGroupValidator>,
  user: MID
) => {
  const groupUser = await RabbleGroupUserModel.findOne({
    user,
    rabbleGroup: group,
  });
  if (!groupUser)
    throw new TRPCError({ code: "NOT_FOUND", message: "group not found" });
  if (groupUser.role === "owner")
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "owners cannot leave a group",
    });
  await RabbleGroupUserModel.deleteOne({ user, rabbleGroup: group });
};

export const kickUser = async ({
  group,
  userId,
}: z.infer<typeof kickUserValidator>) => {
  const groupUser = await RabbleGroupUserModel.findOne({
    user: userId,
    rabbleGroup: group,
  });
  if (!groupUser)
    throw new TRPCError({ code: "NOT_FOUND", message: "group not found" });
  if (groupUser.role === "owner")
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Can't kick group owners",
    });
  await RabbleGroupUserModel.deleteOne({ user: userId, rabbleGroup: group });
};

// Rabble Groups
export const getGroupInfo = ({
  group,
}: z.infer<typeof getGroupInfoValidator>) =>
  RabbleGroupModel.findById({ _id: group }).lean();

export const createRabbleGroup = async (
  payload: z.infer<typeof createRabbleGroupValidator>,
  user: MID
) => {
  const rabbleGroup = await new RabbleGroupModel({
    ..._.omitBy(payload, _.isUndefined),
    createdBy: user,
  }).save();
  await addUserToGroup({
    rabbleGroup: rabbleGroup._id.toString(),
    user: user.toString(),
    role: "owner",
  });
  const users = await getUsersInGroup({ group: rabbleGroup._id.toString() });
  const userInfo = await UserAccountModel.findById(user.toString());
  // create one signal notification
  await createOnesignalNotification({
    heading: `${userInfo?.firstname} has joined the group ${rabbleGroup.groupName}`,
    message: "Open the app to give him a warm welcome",
    launchUrl: `myrabble://groups/${String(rabbleGroup._id)}`,
    userIds: users.map((user) => String(user._id)),
  });
  mixpanel.trackEvent(
    "RABBLEGROUP_CREATED",
    _.mapValues(rabbleGroup, String),
    String(user)
  );
  return rabbleGroup;
};

export const updateRabbleGroupPartials = async (
  { id, ...payload }: z.infer<typeof updateRabbleGroupPartialsValidator>,
  user: MID
) => {
  const rabbleGroupUser = await RabbleGroupUserModel.findOne({
    rabbleGroup: id,
    user,
  });
  if (!rabbleGroupUser)
    throw new TRPCError({ code: "NOT_FOUND", message: "user not found in db" });
  if (rabbleGroupUser.role === "user")
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "not authorized to accept request",
    });
  console.log({ payload });
  const rabbleGroup = await RabbleGroupModel.updateOne(
    { _id: id },
    { $set: payload },
    { new: true }
  );
  if (!rabbleGroup.modifiedCount) {
    throw new TRPCError({ code: "NOT_FOUND", message: "Group not found" });
  }

  mixpanel.trackEvent(
    "RABBLEGROUP_UPDATED",
    _.mapValues(rabbleGroup, String),
    String(user)
  );
};

export const searchRabbleGroup = ({
  name,
  diseaseTags,
}: z.infer<typeof searchRabbleGroupValidator>) =>
  RabbleGroupModel.aggregate([
    {
      $match: {
        deleted: { $ne: true },
      },
    },
    {
      $search: {
        index: "group_search",
        text: {
          query: name || " ",
          path: ["groupName", "groupDescription"],
          fuzzy: {
            maxEdits: 2,
          },
        },
      },
    },
    {
      $match: {
        deleted: { $ne: true },
        ...(diseaseTags && { diseaseTags: { $in: diseaseTags } }),
      },
    },
  ]);

export const getUserCreatedGroups = (user: MID) =>
  RabbleGroupModel.find({
    createdBy: user,
    deleted: false,
    // appearanceInDirectory: true,
  }).lean();

export const userJoinedGroups = ({
  user,
  showUserCreatedGroups,
}: {
  user: string | string[];
  showUserCreatedGroups?: boolean;
}) => {
  const results = RabbleGroupUserModel.aggregate(
    getUserJoinedGroupsAggregation({ userId: user, showUserCreatedGroups })
  ) as unknown as {
    _id: string;
    rabbleGroup: {
      _id: string;
      groupDescription: string;
      groupName: string;
      image: string;
      privacy: "private" | "public";
      deleted: boolean;
      createdAt: string;
    };
  }[];

  return results;
};

export const caregiverPatientJoinedGroups = async (user: MID) => {
  const patients = await UserAccountModel.find({
    "managedBy.user": user,
    "managedBy.approvalStatus": CaregiverApprovalStatus.APPROVED,
  })
    .select("_id")
    .lean();
  const patientIds = patients.map((p) => String(p._id));

  return userJoinedGroups({ user: patientIds, showUserCreatedGroups: true });
};

export const deleteRabbleGroup = async (
  { groupId }: z.infer<typeof deleteRabbleGroupValidator>,
  user: MID
) => {
  const rabbleGroup = await RabbleGroupModel.findById(groupId);
  if (!rabbleGroup)
    throw new TRPCError({ code: "NOT_FOUND", message: "group not found" });

  if (user !== rabbleGroup.createdBy.toString()) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "unauthorized to delete this group",
    });
  }

  rabbleGroup.deleted = true;
  mixpanel.trackEvent(
    "RABBLEGROUP_DELETED",
    _.mapValues(rabbleGroup.toJSON(), String),
    user
  );

  return rabbleGroup.save();
};

// Rabble Invites
const sendRequestInviteNotification = async (
  rabbleGroup: string,
  requestedBy: string,
  requestId: MID | undefined
) => {
  const privilagedUsers = await RabbleGroupUserModel.find({
    rabbleGroup,
    role: { $in: ["owner", "admin"] },
  });
  await Promise.all(
    privilagedUsers.map((privilagedUser) =>
      createNotification({
        notificationType: NotificationTypes.RABBLEGROUP_JOIN_REQUEST,
        userId: privilagedUser.user.toString(),
        content: "",
        requestedUserId: requestedBy,
        metadata: {
          groupId: rabbleGroup,
          requestId,
        },
      })
    )
  );
};

export const requestInvite = async (
  { rabbleGroup }: z.infer<typeof requestInviteValidator>,
  requestedBy: string
) => {
  const group = await getRabbleGroupById(rabbleGroup);

  if (group.privacy !== "private")
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "not a private rabble group",
    });

  const rabbleGroupUser = await RabbleGroupUserModel.findOne({
    rabbleGroup,
    user: requestedBy,
  });
  if (rabbleGroupUser)
    throw new TRPCError({
      code: "CONFLICT",
      message: "user already exist in Rabble group",
    });

  //   upsert to prevent duplicate invites
  const result = await RabbleInviteModel.updateOne(
    { rabbleGroup, requestedBy, requestStatus: "pending" },
    { rabbleGroup, requestedBy, requestStatus: "pending" },
    { upsert: true, new: true }
  );

  if (!result.modifiedCount) {
    // send notifications to all owners and admins
    sendRequestInviteNotification(
      rabbleGroup,
      requestedBy,
      result.upsertedId?.toString()
    ).catch((err) => log.error("@SendNotificationError", err));
  }
};

export const handleRabbleInvite = async (
  {
    rabbleGroup,
    requestId,
    requestStatus,
    requestedUser,
  }: z.infer<typeof handleRabbleInviteValidator>,
  user: MID
) => {
  const rabbleGroupInviteRequest = await RabbleInviteModel.findById(requestId);
  if (!rabbleGroupInviteRequest)
    throw new TRPCError({ code: "NOT_FOUND", message: "request not found" });

  const rabbleGroupUser = await RabbleGroupUserModel.findOne({
    rabbleGroup,
    user: rabbleGroupInviteRequest.invitedBy || user,
  });

  if (!rabbleGroupUser)
    throw new TRPCError({ code: "NOT_FOUND", message: "user not found in db" });

  if (rabbleGroupUser.role === "user")
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "not authorized to accept request",
    });

  rabbleGroupInviteRequest.requestStatus = requestStatus;
  await rabbleGroupInviteRequest.save();

  if (requestStatus === "accepted") {
    await addUserToGroup({ rabbleGroup, user: requestedUser, role: "user" });
  }
};

export const getRabbleInvites = async (
  { rabbleGroupId }: z.infer<typeof getRabbleInvitesValidator>,
  userId: string
) => {
  const rabbleGroupUser = await RabbleGroupUserModel.findOne({
    user: userId,
    rabbleGroup: rabbleGroupId,
  });
  if (!rabbleGroupUser)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "user not found in database",
    });

  if (rabbleGroupUser.role !== "owner" && rabbleGroupUser.role !== "admin")
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "admin and super_admin can see invites",
    });

  return RabbleInviteModel.find({
    rabbleGroup: rabbleGroupId,
    requestStatus: "pending",
  })
    .populate<{ requestedBy: UserAccount }>("requestedBy")
    .lean();
};

export const getRabbleInvitesWithEmail = async ({
  email,
  phone,
}: z.infer<typeof getRabbleInvitesWithEmailValidator>) => {
  let invites: any[] = [];

  if (email) {
    invites = await RabbleInviteModel.find({ email, requestStatus: "pending" })
      .populate<{ invitedBy: UserAccount }>("invitedBy")
      .populate("rabbleGroup")
      .lean();
  }

  if ((!invites || invites.length === 0) && phone) {
    invites = await RabbleInviteModel.find({ phone, requestStatus: "pending" })
      .populate<{ invitedBy: UserAccount }>("invitedBy")
      .populate("rabbleGroup")
      .lean();
  }

  return invites;
};

export const inviteSignupRabble = async (payload: any, user: MID) => {
  console.log("payload", payload);
  const { invitations = [] } = payload;
  const invitesWithUser = invitations.map((invite: any) => ({
    ...invite,
    invitedBy: user,
    requestedBy: user, // Add the required requestedBy field
  }));
  const invites = await RabbleInviteModel.insertMany(invitesWithUser);

  console.log("invites", invites);
  return invites;
};
