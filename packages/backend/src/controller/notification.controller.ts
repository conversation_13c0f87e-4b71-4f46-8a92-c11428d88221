import { z } from 'zod';
import { TRPCError } from '@trpc/server';

import {
    createNotificationValidator
} from '../../../shared/validators/notification.validator';

import NotificationModel from '../models/Notifications';
import { MID } from '../@types/common';
import { UserAccount } from '../../../shared/types/user';

export const createNotification = async (payload: z.infer<typeof createNotificationValidator>) => {
    const notification = await NotificationModel.create(payload);
    return notification.toJSON();
};


export const getNotifications = async (userId: MID) => {
    const notifications = await NotificationModel.find({ userId }).populate<{ requestedUserId: UserAccount }>('requestedUserId').sort({ createdAt: -1 }).lean();
    return notifications;
};

export const getNotificationsCount = async (userId: MID) => {
    const count = await NotificationModel.count({ userId, read: false });
    return count;
};


export const removeNotification = async (query: object) => {
   const result = await NotificationModel.deleteMany(query);
    if(!result.deletedCount) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'post not found' });
    }
};

export const updateReadNotification = async (userId: MID) => {
    const notifications = await NotificationModel.updateMany(
        { userId },
        { $set: { read: true } }
    );
    return notifications;
};