import { z } from 'zod';
import AnalyticsModel from '../models/Analytics';

/**
 * Create a new analytics entry
 * @param key The unique key for the analytics data
 * @param value The value to store (can be any type)
 * @returns The created analytics document
 */
export const createAnalytics = async ({ key, value }: { key: string; value: any }) => {
  // Use findOneAndUpdate with upsert to handle both creation and updates
  const analytics = await AnalyticsModel.findOneAndUpdate(
    { key },
    { key, value },
    { upsert: true, new: true }
  );
  
  return analytics.toJSON();
};
