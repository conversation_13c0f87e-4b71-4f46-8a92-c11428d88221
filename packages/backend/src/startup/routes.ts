import { Express, Router } from 'express';
import { renderTrpcPanel } from 'trpc-panel';
import createHttpError from 'http-errors';
import t from './trpc';
import userRoute from '../routes/user.route';
import serviceRoute from '../routes/service.route';
import authRouter from '../routes/auth.route';
import { libRouter } from '../routes/lib.route';
import { connectRouter } from '../routes/connect.route';
import { uploadFileTOS3, validateImage } from '../utils';
import { appUpdateRouter } from '../routes/app-update.route';
import notificationsRouter from '../routes/notification.route';
import { rabbleGroupRouter } from '../routes/rabblegroup.route';
import manageRouter from '../routes/manage.route';
import { blogRouter } from '../routes/blog.route';
import { mixpanelRouter } from '../routes/mixpanel.route';
import { utmRouter } from '../routes/utm.route';
import analyticsRouter from '../routes/analytics.route';

const prefix = process.env.API_PREFIX as string;

const router = Router();

// trpc router
export const appRouter = t.router({
  trpcStatus: t.procedure.query(() => ({ status: 'OK', message: 'ONLINE' })),
  user: userRoute,
  service: serviceRoute,
  auth: authRouter,
  lib: libRouter,
  connect: connectRouter,
  rabbleGroups: rabbleGroupRouter,
  appUpdates: appUpdateRouter,
  notification: notificationsRouter,
  blog: blogRouter,
  mixpanel: mixpanelRouter,
  manage: manageRouter,
  utm: utmRouter,
  analytics: analyticsRouter,
});

/**
 * File Upload Router
 */
const fileRouter = router.post('/', async (req, res) => {
  if (!req.body.filePath) throw new createHttpError.BadRequest('filePath is a required field');

  const image = validateImage(req.files);
  if (image) {
    const path = await uploadFileTOS3({ image, s3FilePath: req.body.filePath });
    return res.send({ status: 'ok', s3Path: path });
  }

  throw new createHttpError.BadRequest('something went wrong');
});

export default (app: Express) => {
  app.use(`${prefix}/panel`, (_, res) => res.send(renderTrpcPanel(appRouter, { url: '/api/v1/trpc', transformer: 'superjson' })));
  app.use(`${prefix}/upload`, fileRouter);
};
