import { initTRPC, inferAsyncReturnType } from '@trpc/server';
import { CreateExpressContextOptions } from '@trpc/server/adapters/express';
import SuperJSON from 'superjson';
import { TRPCPanelMeta } from 'trpc-panel';

export function createContext({ req }: CreateExpressContextOptions) {
  return { req };
}

const t = initTRPC.meta<TRPCPanelMeta>().context<inferAsyncReturnType<typeof createContext>>().create({
  transformer: SuperJSON,
});

export default t;
