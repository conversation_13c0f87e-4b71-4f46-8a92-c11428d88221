/* eslint-disable no-console */
import express, { Express } from 'express';
import cors from 'cors';
import fileUpload from 'express-fileupload';
import { createExpressMiddleware } from '@trpc/server/adapters/express';
import { appRouter } from './routes';
import { createContext } from './trpc';

export default (app: Express) => {
  app.use(cors({ origin: '*' }));
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // @ts-expect-error
  app.use(fileUpload());

  app.use(
    '/api/v1/trpc',
    createExpressMiddleware({
      router: appRouter,
      createContext,
      onError(err) {
        console.log(err.error);
      },
    }),
  );
};
