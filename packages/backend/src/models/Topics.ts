import mongoose, { Schema } from "mongoose";
import {
  Option,
  ActionTypes,
  Question,
  QuestionTypes,
  Topic,
  EventTypes,
  TopicCategory,
} from "../../../shared/types/manage";
import { BlogTagModel } from "./Blog";

const actionSchema = new Schema(
  {
    actionType: { required: true, type: String, enum: ActionTypes },
    event: { required: true, type: String, enum: EventTypes },
    metadata: { required: false, type: Schema.Types.Mixed, default: {} },
    isDeleted: { required: false, type: Boolean, default: false },
  },
  { timestamps: true }
);

export const optionsSchema = new Schema<Option>(
  {
    value: { required: true, type: String },
    description: { required: false, type: Schema.Types.Mixed },
    color: { required: false, type: String },
    isDeleted: { required: false, type: Boolean, default: false },
    externalId: { required: false, type: String },
    endOfQuestions: { type: Boolean, default: false },
    carePartnerValue: { required: false, type: String },
    prompt: { required: false, type: String },
    answersFromProfile: { required: false, type: Boolean },
  },
  { timestamps: true }
);

const questionsSchema = new Schema<Question>(
  {
    question: { required: true, type: String },
    subText: { required: false, type: String },
    type: {
      required: false,
      type: String,
      enum: QuestionTypes,
      default: QuestionTypes.Select,
    },
    actions: { required: false, type: [actionSchema], default: [] },
    carePartnerText: { required: false, type: String },
    getNextCompletedQuestion: { required: false, type: String },
    isDeleted: { required: false, type: Boolean, default: false },
    isOptional: { required: false, type: Boolean, default: false },
    options: { required: false, type: [optionsSchema] },
    externalId: { required: false, type: String },
    isSchedule: { type: Boolean, default: false, required: false },
    duration: { type: Number, required: false },
    delay: { type: Number, required: false },
    recurringDay: { type: Number, required: false },
    recurringQuestion: { type: Boolean, required: false },
    isActivityQuestion: { type: Boolean, required: false },
  },
  { timestamps: true }
);

const schema = new Schema<Topic>(
  {
    name: { required: true, type: String },
    nickname: { required: true, type: String },
    category: {
      required: false,
      type: [Schema.Types.ObjectId],
      ref: "topic-category",
    },
    tags: { required: true, type: [Schema.Types.ObjectId], ref: BlogTagModel },
    questions: { required: true, type: [questionsSchema] },
    learningQuestions: { required: false, type: [questionsSchema] },
    dailyTrackerQuestions: { required: true, type: [questionsSchema] },
    isDeleted: { required: false, type: Boolean, default: false },
    externalId: { required: false, type: String },
    logo: { required: false, type: String },
  },
  { timestamps: true }
);

const categorySchema = new Schema<TopicCategory>(
  {
    name: { required: true, type: String },
    logo: { required: true, type: String },
  },
  { timestamps: true }
);

const TopicModel = mongoose.model("topics", schema);

export default TopicModel;
export const TopicCategoryModel = mongoose.model(
  "topic-category",
  categorySchema
);
