import mongoose, { Schema } from "mongoose";

// Interface for the key-value document
export interface KeyValue {
  key: string;
  value: any;
  createdAt: Date;
  updatedAt: Date;
}

// Schema definition with flexible value type
const keyValueSchema = new Schema<KeyValue>(
  {
    key: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    value: {
      type: Schema.Types.Mixed,
      required: true,
    },
  },
  {
    timestamps: true,
    strict: false, // Allows for storing any fields in the value
  }
);

// Create and export the model
const AnalyticsModel = mongoose.model<KeyValue>("analytics", keyValueSchema);

export default AnalyticsModel;
