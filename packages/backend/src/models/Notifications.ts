import mongoose, { Schema } from 'mongoose';
import { Notification } from '../../../shared/types/notification';
import { NotificationTypes } from '../../../shared/enums/notification';
import mixpanel from '../utils/mixpanel';
import { createOnesignalNotification } from '../utils/onesignal';

const schema = new Schema<Notification>(
  {
    notificationType: { type: String, enum: NotificationTypes },
    content: { type: String },
    read: { type: Boolean, default: false },
    userId: { type: Schema.Types.ObjectId, ref: 'user-account' },
    requestedUserId: { type: Schema.Types.ObjectId, ref: 'user-account', required: false },
    metadata: { type: Schema.Types.Mixed, required: false },
  },
  { timestamps: true },
);

const getContent = (type: string, payload?: { heading?: string; message?: string }) => {
  switch (type) {
    case NotificationTypes.CAREGIVER_APPROVAL:
      return { heading: 'A user is requesting you to be your caregiver', message: 'Click to approve or reject' };
    case NotificationTypes.RABBLEGROUP_JOIN_REQUEST:
      return { heading: 'A user is requesting you to join your group', message: 'Click to approve or reject' };
    case NotificationTypes.MANAGE_DAILY_QUESTIONS:
      return {
        heading: payload?.heading ?? 'A user is requesting you to join your group',
        message: payload?.message ?? 'click here to track',
      };
    default:
      return { heading: 'You have a new notification', message: 'Click to view' };
  }
};

schema.post('save', async (doc) => {
  const { heading, message } = getContent(doc.notificationType, {
    ...(doc.notificationType === NotificationTypes.MANAGE_DAILY_QUESTIONS && { heading: doc.content }),
  });
  // one-signal
  await createOnesignalNotification({ heading: heading ?? doc.content, message, userIds: [String(doc.userId)] });

  // Track mixpanel events
  await mixpanel.trackEvent(
    'NOTIFICATION_CREATED',
    { notificationType: doc.notificationType as NotificationTypes, content: doc.content },
    String(doc.userId),
  );
});

const NotificationModel = mongoose.model('notification', schema);

// TODO: Index for notifications, keeping it commented for now.

// const NOTIFICATION_TTL_IN_DAYS = 30;

// schema.index({ createdAt: 1 }, {
//     expireAfterSeconds: NOTIFICATION_TTL_IN_DAYS * (60 * 60 * 24) });

export default NotificationModel;
