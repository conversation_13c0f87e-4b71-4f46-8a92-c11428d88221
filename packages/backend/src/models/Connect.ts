import mongoose, { Schema } from 'mongoose';
import _ from 'lodash';
import { Like, Post, ReportPost, Comment, CommentLike, ReportPostStatus } from '../../../shared/types/Connect';
import UserAccountModel from './UserAccount';
import { RabbleGroupModel, RabbleGroupUserModel } from './RabbleGroup';
import mixpanel from '../utils/mixpanel';
import { imageKit } from '../utils';
import { createOnesignalNotification } from '../utils/onesignal';

const postSchema = new Schema<Post>(
  {
    image: String,
    title: String,
    user: { required: true, type: Schema.Types.ObjectId, ref: UserAccountModel },
    disabled: Boolean,
    visibleTo: [
      {
        type: Schema.Types.ObjectId,
        ref: UserAccountModel,
        required: false,
      },
    ],
    postType: { required: true, default: 'CONNECT', type: String },
    rabbleGroup: { ref: RabbleGroupModel, type: Schema.Types.ObjectId },
    isEdited: { required: false, default: false, type: Boolean },
    isArchived: { required: false, default: false, type: Boolean },
  },
  { timestamps: true },
);

postSchema.post('save', async (post) => {
  if (post?.isNew || !post?.disabled) {
    const user = await UserAccountModel.findById(post.user);
    const rabbleGroup = await RabbleGroupModel.findById(post.rabbleGroup);

    // onesignal noti
    if (rabbleGroup) {
      let image: string | undefined;

      if (post.image) {
        image = imageKit({
          imagePath: post.image,
          transform: ['w-600'],
        });
      }

      // send notification to all users except author
      const groupUsers = await RabbleGroupUserModel.find({ rabbleGroup: rabbleGroup._id });
      createOnesignalNotification({
        heading: post.title,
        message: `A new post is added to the group ${rabbleGroup.groupName}`,
        imageUrl: image,
        userIds: groupUsers.map((_) => String(_.user)),
        launchUrl: `myrabble://posts/${String(post._id)}`,
      });
    }

    mixpanel.trackEvent(
      'CREATE_POST',
      {
        createdAt: String(new Date()),
        postId: String(post._id),
        username: user?.username || '',
        caption: post.title,
        rabbleGroup: rabbleGroup?.groupName,
        rabbleGroupId: String(rabbleGroup?._id),
      },
      String(post.user),
    );
  }
});

export const PostModel = mongoose.model('post', postSchema);

/**
 * Likes
 */
const likeSchema = new Schema<Like>(
  {
    post: { required: true, ref: PostModel, type: Schema.Types.ObjectId },
    user: { required: true, ref: PostModel, type: Schema.Types.ObjectId },
    reactionType: { required: false, type: String },
  },
  { timestamps: true },
);

likeSchema.post('findOneAndUpdate', async (like) => {
  const user = await UserAccountModel.findById(like.user);
  const post = await PostModel.findById(like.post);
  const postAdmin = await UserAccountModel.findById(post?.user);
  const group = post?.rabbleGroup ? await RabbleGroupModel.findById(post.rabbleGroup) : undefined;
  const groupAdmin = await UserAccountModel.findById(group?.createdBy);
  if (!user || !post) return;
  // os-notification

  let image: string | undefined;

  if (post.image) {
    image = imageKit({
      imagePath: post.image,
      transform: ['w-600'],
    });
  }

  await createOnesignalNotification({
    heading: `${user?.username} liked your post ${post?.title}`,
    userIds: [String(post.user.toString())],
    imageUrl: image,
    launchUrl: `myrabble://posts/${String(post._id)}`,
  });
  console.log('Post liked (Rabble screen)', { postAdmin, postUserId: post?.user });
  mixpanel.trackEvent(
    'Post liked (Rabble screen)',
    {
      email: user?.email || '',
      post_username: postAdmin?.username || '',
      post_id: String(post?._id) || '',
      group_title: group?.groupName || '',
      group_id: String(group?._id || '') || '',
      group_status: !group?.groupName ? 'Inactive' : `Active`,
      group_admin: `${groupAdmin?.firstname || ''} ${groupAdmin?.lastname || ''}`,
    },
    String(user?._id),
    'v2',
  );

  mixpanel.trackEvent(
    'LIKE_POST',
    {
      createdAt: String(new Date()),
      postId: String(like._id),
      username: user?.username || '',
    },
    String(like.user),
  );
});

likeSchema.post('findOneAndDelete', async (like) => {
  if (!like?.user) return;
  const user = await UserAccountModel.findById(like.user);
  const post = await PostModel.findById(like.post);
  if (!user || !post) return;

  mixpanel.trackEvent(
    'UNLIKE_POST',
    {
      createdAt: String(new Date()),
      postId: String(like._id),
      username: user?.username || '',
    },
    String(like.user),
  );
});

export const LikeModel = mongoose.model('like', likeSchema);

/**
 * Comments
 */
const commentSchema = new Schema<Comment>(
  {
    comment: { type: String, required: true },
    post: { type: Schema.Types.ObjectId, required: true, ref: PostModel },
    user: { type: Schema.Types.ObjectId, required: true, ref: UserAccountModel },
    repliedTo: { type: Schema.Types.ObjectId, required: false },
    isEdited: { type: Boolean, required: false, default: false },
  },
  { timestamps: true },
);

commentSchema.post('save', async (comment) => {
  const user = await UserAccountModel.findById(comment.user);
  const post = await PostModel.findById(comment.post);

  if (!user || !post) return;
  // os-notification

  let image: string | undefined;

  if (post.image) {
    image = imageKit({
      imagePath: post.image,
      transform: ['w-600'],
    });
  }

  await createOnesignalNotification({
    heading: 'A new comment on your post',
    message: `${user?.username} comment on your post ${post?.title}`,
    userIds: [String(post.user)],
    imageUrl: image,
    launchUrl: `myrabble://posts/${String(post._id)}`,
  });

  mixpanel.trackEvent(
    'COMMENT_POST',
    {
      createdAt: String(new Date()),
      postId: String(comment._id),
      username: user?.username || '',
      comment: comment.comment,
    },
    String(comment.user),
  );
});

export const CommentModel = mongoose.model('comment', commentSchema);

/**
 * Comment Like
 */
const commentLikeSchema = new Schema<CommentLike>({
  comment: { type: Schema.Types.ObjectId, ref: CommentModel, required: true },
  user: { type: Schema.Types.ObjectId, ref: UserAccountModel, required: true },
  reactionType: { type: String, required: false },
});
export const CommentLikeModel = mongoose.model('comment-like', commentLikeSchema);

commentLikeSchema.post('findOneAndUpdate', async (like) => {
  const user = await UserAccountModel.findById(like.user);
  const comment = await CommentModel.findById(like.comment);
  const post = await PostModel.findById(comment?._id);

  const groupUsers = await RabbleGroupUserModel.find({ rabbleGroup: post?.rabbleGroup });

  if (!user || !post || !comment) return;
  // os-notification

  let image: string | undefined;

  if (post.image) {
    image = imageKit({
      imagePath: post.image,
      transform: ['w-600'],
    });
  }

  await createOnesignalNotification({
    heading: `A new like on your comment`,
    message: `${user?.username} liked your comment ${post?.title}`,
    userIds: [String(user._id), ...groupUsers.map((_) => String(_.user))],
    imageUrl: image,
    launchUrl: `myrabble://posts/${String(post._id)}`,
  });
});
/**
 * Report post model
 */

const reportPostSchema = new Schema<ReportPost>(
  {
    post: { ref: PostModel, type: Schema.Types.ObjectId },
    comment: { ref: CommentModel, type: Schema.Types.ObjectId },
    reportedBy: { ref: UserAccountModel, type: Schema.Types.ObjectId, required: true },
    type: { type: String, required: true, enum: ['post', 'comment'] },
    status: { type: String, required: true, default: ReportPostStatus.PENDING },
  },
  { timestamps: true },
);

export const ReportPostHistoryModel = mongoose.model('report-post-history', reportPostSchema);

reportPostSchema.post('findOneAndUpdate', async (doc) => {
  if (!doc) return;
  if (doc.status === ReportPostStatus.APPROVE) {
    if (doc.comment) await PostModel.findByIdAndUpdate(doc.post, { disabled: true });
    if (doc.post) await CommentModel.findByIdAndDelete(doc.comment);
  }
  // new ReportPostHistoryModel(_.omit(doc, '_id')).save();
});

export const ReportPostModel = mongoose.model('report-post', reportPostSchema);
