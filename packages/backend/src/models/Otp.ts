import mongoose, { Schema } from 'mongoose';
import { Otp, OtpType } from '../../../shared/types/otp';
import UserAccountModel from './UserAccount';

const schema = new Schema<Otp>(
  {
    otp: { required: true, type: String },
    expiresAt: { required: true, type: Number, default: 300, index: { expires: 300 } },
    type: { type: String, required: true },
    user: { type: Schema.Types.ObjectId, ref: UserAccountModel, required: true },
  },
  { timestamps: true },
);

const OtpModel = mongoose.model('otp', schema);

export default OtpModel;

export const OTP_TYPES: {
  [key in OtpType]: OtpType;
} = { login: 'login', 'update-email-contact': 'update-email-contact' };
