import mongoose, { Schema } from 'mongoose';
import { UserQuestionAnswers, UserTopics } from '../../../shared/types/manage';
import UserAccountModel from './UserAccount';
import TopicModel from './Topics';
import _ from 'lodash';

const answerSchema = new Schema<UserQuestionAnswers>({
  question: { required: true, type: Schema.Types.ObjectId },
  selectedOption: { required: false, type: [Schema.Types.ObjectId] },
  answerText: { required: false, type: String, default: '' },
});

const schema = new Schema<UserTopics>(
  {
    user: { required: true, type: Schema.Types.ObjectId, ref: UserAccountModel },
    topic: { required: true, type: Schema.Types.ObjectId, ref: TopicModel },
    userExperince: { required: false, type: String },
    questionAnswers: { required: true, type: [answerSchema] },
    isCarePartner: { required: false, type: Boolean, default: false },
    isHealthCare: { required: false, type: Boolean, default: false },
    isDeleted: { required: false, type: Boolean, default: false },
  },
  { timestamps: true },
);

// schema.post('save', function (this) {
//   const userDailyTracker = this.toJSON();
//   mixpanel.trackEvent('ManageSubscriptionQuestions', _.mapValues(userDailyTracker, String));
// });
// schema.post('findOneAndUpdate', function (doc) {
//   const userDailyTracker = doc.toJSON();
//   mixpanel.trackEvent('ManageSubscriptionQuestions', _.mapValues(userDailyTracker, String));
// });
// schema.post('updateOne', async function (this) {
//   const updatedDocument = await this.model.findOne(this.getQuery());
//   const userDailyTracker = updatedDocument.toJSON();
//   mixpanel.trackEvent('ManageSubscriptionQuestions', _.mapValues(userDailyTracker, String));
// });
schema.index({ topic: 1, user: 1 }, { unique: true });


const UserTopicsModel = mongoose.model('user-topics', schema);

export default UserTopicsModel;
