import mongoose, { Schema } from "mongoose";
import { Utm, UTM_Analytics } from "../../../shared/types/Utm";

const utmModel = new Schema<Utm>(
  {
    name: { required: true, type: String },
    utm_campaign: { required: false, type: String },
    utm_content: { required: false, type: String },
    utm_medium: { required: false, type: String },
    utm_source: { required: false, type: String },
    utm_term: { required: false, type: String },
  },
  { timestamps: true }
);

const utmAnalyticsSchema = new Schema<UTM_Analytics>(
  {
    utm_id: { required: true, type: Schema.Types.ObjectId, ref: "utm" },
    utm_campaign: { required: false, type: String },
    utm_content: { required: false, type: String },
    utm_medium: { required: false, type: String },
    utm_source: { required: false, type: String },
    utm_term: { required: false, type: String },
  },
  { timestamps: true }
);

const UtmAnalyticsModel = mongoose.model("utm-analytics", utmAnalyticsSchema);
export const UtmModel = mongoose.model("utm", utmModel);

export default UtmAnalyticsModel;
