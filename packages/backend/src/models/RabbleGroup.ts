import mongoose, { Schema } from "mongoose";
import UserAccountModel from "./UserAccount";
import {
  RabbleGroup,
  RabbleGroupUser,
  RabbleInvite,
} from "../../../shared/types/RabbleGroup";
import {
  RABBLEGROUP_USER_ROLES,
  RABBLEINVITE_STATUS,
} from "../../../shared/validators/rabblegroup.validator";

// Rabble group
const rabbleGroupSchema = new Schema<RabbleGroup>(
  {
    createdBy: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: UserAccountModel,
    },
    groupDescription: { required: true, type: String },
    groupGuidelines: { required: false, type: String },
    groupName: { required: true, type: String, text: true },
    image: { required: false, type: String },
    privacy: { required: true, type: String },
    deleted: { type: Boolean, default: false },
    diseaseTags: [String],
    tags: [String],
    appearanceInDirectory: { type: Boolean, default: false },
  },
  { timestamps: true }
);
rabbleGroupSchema.path("groupName").index({ text: true });

export const RabbleGroupModel = mongoose.model(
  "rabble-group",
  rabbleGroupSchema
);

//   Rabble user
const rabbleUserSchema = new Schema<RabbleGroupUser>(
  {
    rabbleGroup: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: RabbleGroupModel,
    },
    user: {
      required: true,
      type: Schema.Types.ObjectId,
      ref: UserAccountModel,
    },
    role: {
      required: true,
      type: String,
      default: RABBLEGROUP_USER_ROLES.user,
    },
  },
  { timestamps: true }
);

export const RabbleGroupUserModel = mongoose.model(
  "rabble-group-user",
  rabbleUserSchema
);

// Rabble Invites
const rabbleInviteSchema = new Schema<RabbleInvite>(
  {
    rabbleGroup: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: RabbleGroupModel,
    },
    // requested if a user requested to join
    requestedBy: { type: Schema.Types.ObjectId, ref: UserAccountModel },
    requestStatus: {
      type: String,
      required: true,
      default: RABBLEINVITE_STATUS.pending,
    },
    // invited is if the admin invited a user
    invitedBy: { type: Schema.Types.ObjectId, ref: UserAccountModel },
    email: { type: String, required: false },
    phone: { type: String, required: false },
  },
  { timestamps: true }
);
export const RabbleInviteModel = mongoose.model(
  "rabble-invite",
  rabbleInviteSchema
);
