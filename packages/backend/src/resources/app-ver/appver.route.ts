import { Router } from 'express';
import Joi from 'joi';
import AppverModel from './appver.model';

const router = Router();

router.get('/', async (req, res) => {
  res.send(await AppverModel.findOne({}));
});

const verSchema = Joi.object({
  appVersion: Joi.string().required(),
  otaUpdate: Joi.boolean().required(),
});

const createVerSchema = Joi.object({ android: verSchema, ios: verSchema });

router.get('/', async (req, res) => {
  res.send(await AppverModel.findOne({}));
});

router.put('/', async (req, res) => {
  const validated = await createVerSchema.validateAsync(req.body);
  res.send(await AppverModel.updateOne({}, validated, { upsert: true, new: true }));
});

export default router;
