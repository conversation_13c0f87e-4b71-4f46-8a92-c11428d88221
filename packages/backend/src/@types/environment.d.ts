declare global {
  namespace NodeJS {
    interface ProcessEnv {
      PORT: string;
      API_PREFIX: string;
      DATABASE: string;
      NODE_ENV: 'development' | 'production';
      JWT_SECRET: string;
      OTP_EXPIRY_IN_SECONDS: number;
      AWS_ACCESS_KEY: string;
      AWS_SECRET: string;
      S3_BUCKET: string;
      S3_REGION: string;
      DATABASE_URL: string;
      SUPER_ADMIN: string;
      SUPER_ADMIN_PASSWORD: string;
      SUPER_ADMIN_EMPLOYEE_CODE: string;
      SUPER_ADMIN_ROLE: string;
      EMAIL: string;
      EMAIL_PASSWORD: string;
      POST_REPORTING_EMAIL: string;
      MIXPANEL_TOKEN: string;
      COUNTRY_CODE: string;
    }
  }
}

// If this file has no import/export statements (i.e. is a script)
// convert it into a module by adding an empty export statement.
export {};
