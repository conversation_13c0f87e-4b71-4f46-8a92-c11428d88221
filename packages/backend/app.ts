import 'express-async-errors';
import express from 'express';
import startup from './src/startup';
import './src/startup/mongoose';
import { appRouter } from './src/startup/routes';
import './src/cron/sendPinpointEmailAfter30days';
import './src/cron/sendManageRemainderNotifications';

const app = express();

app.get('/', (req, res) => res.sendStatus(200));

const { errorHandler, middleware, routes, Server } = startup;

// middleware
middleware(app);

// routes
routes(app);

// Error Handler
errorHandler(app);

// server
module.exports = Server(app);

export type AppRouter = typeof appRouter;
