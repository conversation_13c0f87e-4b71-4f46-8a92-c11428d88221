version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: latest
    commands:
      - npm install -g typescript
      - npm install -g yarn
  build:
    commands:
      - cd packages && rm -rf app && rm -rf dashboard && cd backend && yarn && npm run build && mv * ../.. || true
      - cd ../../
artifacts:
  files:
    - package.json
    - package-lock.json
    - yarn.lock
    - 'dist/**/*'
    - .ebextensions/**/*
    - .platform/**/*