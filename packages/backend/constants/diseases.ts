type Diagonsis = {
  name: string;
  subTypes: string[];
};
interface HealthProfileOptions {
  diagnosisTypes: {
    cancer: Diagonsis[];
    respiratory: Diagonsis[];
    neurology: Diagonsis[];
    nephrology: Diagonsis[];
    cardiovascular: Diagonsis[];
    inflammation: Diagonsis[];
    boneHealth: Diagonsis[];
  };
  patientBiopsyReports: string[];
  cancerJourneyStage: string[];
  her2Statuses: string[];
  personalizedHelp: string[];
  diagnosisDates: string[];
  diseaseTypes: {
    cancer: string;
    nephrology: string;
    neurology: string;
    cardiovascular: string;
    inflammation: string;
    boneHealth: string;
    respiratory: string;
  };
  diagnonsisStageFields: string[];
  additionalDiagonsisAppliers: {
    'Bone Cancer': string[];
    'Chronic kidney disease (CKD)': string[];
    'Ductal Carcinoma': string[];
    'Lobular Carcinoma': string[];
    'Inflammatory Breast Cancer': string[];
    unknown: string[];
  };
  diagnosisStageTypes: {
    'Ductal Carcinoma': string[];
  };
}

export const healthProfileOptions: HealthProfileOptions = {
  diagnosisTypes: {
    neurology: [
      {
        name: 'Migraine',
        subTypes: ['Severe migraine'],
      },
    ],
    nephrology: [
      {
        name: 'Kidney Cancer',
        subTypes: ['Parathyroid carcinoma'],
      },
      {
        name: 'Kidney Disease',
        subTypes: ['Chronic kidney disease (CKD)', 'Anemia', 'Secondary hyperparathyroidism (HPT) in chronic kidney disease (CKD)'],
      },
    ],
    cardiovascular: [
      {
        name: 'Heart Disease',
        subTypes: ['Heart failure', 'High cholesterol (LDL-C)'],
      },
    ],
    inflammation: [
      {
        name: 'Arthritis',
        subTypes: ['Rheumatoid Arthritis (RA)', 'Psoriatic Arthritis (PsA)', 'Juvenile Idiopathic Arthritis (JIA)'],
      },
      {
        name: 'Asthma',
        subTypes: ['Severe uncontrolled asthma'],
      },
      {
        name: 'Inflammatory Bowel Disease (IBD)',
        subTypes: ["Crohn's Disease (CD)", 'Ulcerative Colitis (UC)'],
      },
      {
        name: 'Inflammatory Disease',
        subTypes: ['Ankylosing Spondylitis (AS)', 'Hidradenitis Suppurativa (HS)', 'Uveitis (UV)'],
      },
      {
        name: 'Psoriasis',
        subTypes: ['Psoriasis Arthritis (PsA)', 'Plaque Psoriasis (Ps)', 'Juvenile Psoriatic Arthritis (JPsA)'],
      },
      {
        name: 'Rare Disease',
        subTypes: ['ANCA-associated vasculitis', 'Thyroid eye disease (TED)', 'Gout', 'Neuromyelitis optica spectrum disorder (NMOSD)'],
      },
    ],
    boneHealth: [
      {
        name: 'Osteoporosis',
        subTypes: ['Postmenopausal osteoporosis with high risk for fracture', 'Glucocorticoid-induced osteoporosis'],
      },
    ],
    respiratory: [
      {
        name: 'COPD',
        subTypes: ['Stage 1', 'Stage 2', 'Stage 3', 'Stage 4', 'Unknown'],
      },
      {
        name: 'Asthma',
        subTypes: ['Intermittent', 'Mild', 'Moderate', 'Severe'],
      },
    ],
    cancer: [
      {
        name: 'Bladder Cancer',
        subTypes: ['Bladder Cancer'],
      },
      {
        name: 'Blood - Leukemia',
        subTypes: [
          'Acute Lymphoblastic Leukemia',
          'Acute Myeloid Leukemia',
          'Biphenotypic Leukemia',
          'Chronic Lymphocytic Leukemia',
          'Chronic Myelogenous Leukemia',
          'Hybrid Mixed Lineage Leukemia',
          'Hypodiploid Leukemia',
          'Mixed Phenotype Leukemia',
          'Philadelphia Like Leukemia',
          'Philadelphia Positive Leukemia',
        ],
      },
      {
        name: 'Blood - Lymphoma',
        subTypes: [
          'Anaplastic Large Cell Lymphoma',
          'B-Cell Lymphomas - Diffuse Large B-Cell Lymphomas',
          'B-Cell Lymphomas - Follicular Lymphoma',
          'B-Cell Lymphomas - Mantle Cell Lymphoma',
          'Burkitt Lymphoma',
          'Diffuse Large B-cell Lymphoma',
          'Hodgkin’s Lymphoma',
          'Lymphoblastic Lymphoma',
          'Non-Hodgkin’s Lymphoblastic Lymphoma',
          'Peripheral T-Cell Lymphoma',
          'Primary Cutaneous Lymphomas',
        ],
      },
      {
        name: 'Bone Cancer',
        subTypes: ['Bone Cancer', 'Osteosarcoma'],
      },
      {
        name: 'Brain Cancer',
        subTypes: [
          'Anaplastic Ependymoma',
          'Astrocytoma',
          'Diffuse Intristic Pontine Glioma',
          'Diffuse Leptomeningeal Glioneuronal',
          'Diffuse Leptomeningeal Glioneuronal Tumor',
          'Ganglioglioma Brainstem Tumor',
          'Germinoma',
          'Glioblastoma Multiforme',
          'Gliomas',
          'Medulloblastoma',
          'Meningioma',
          'Neuroblastoma',
          'Optic Pathway Glioma',
        ],
      },
      {
        name: 'Breast Cancer',
        subTypes: ['Ductal Carcinoma', 'Inflammatory Breast Cancer', 'Lobular Carcinoma', 'unknown'],
      },
      {
        name: 'Colorectal Cancer',
        subTypes: ['Anal Cancer', 'Colon Cancer', 'Rectal Cancer'],
      },
      {
        name: 'Gynecological Cancer',
        subTypes: ['Cervical Cancer', 'Ovarian Cancer', 'Uterine Cancer'],
      },
      {
        name: 'Head and Neck Cancer',
        subTypes: [
          'Head and Neck Cancers - Nasopharyngeal Cancer',
          'Head and Neck Cancers - Oral Cancers',
          'Head and Neck Cancers - Oropharyngeal Cancer',
        ],
      },
      {
        name: 'Kidney Cancer',
        subTypes: ['Clear Cell Sarcoma of Kidney', 'Kidney Cancer', 'Translocation Renal Cell Carcinoma'],
      },
      {
        name: 'Liver Cancer',
        subTypes: ['Gallbladder and Bile Duct Cancers', 'Liver Cancer'],
      },
      {
        name: 'Lung Cancer',
        subTypes: ['Mesothelioma: Pleural - Malignant Pleural Mesothelioma', 'Non-Small Cell Lung Cancer', 'Small Cell Lung Cancer'],
      },
      {
        name: 'Multiple Myeloma',
        subTypes: ['Multiple Myeloma'],
      },
      {
        name: 'Other',
        subTypes: ['Other cancer'],
      },
      {
        name: 'Pancreatic Cancer',
        subTypes: ['Pancreatic Cancer'],
      },
      {
        name: 'Prostate Cancer',
        subTypes: ['Prostate Cancer'],
      },
      {
        name: 'Rare Cancer',
        subTypes: [
          'Adrenal Tumors',
          'Adrenalcortical Carcinoma',
          'Atypical Teratoid Rhabdoid Tumor',
          'Central Nervous System Ganglinoeuroblastoma',
          'Chordoma',
          'Choroid Plexus Carcinoma',
          'Desmoplastic Small Cell Tumor',
          'Diffuse Neuro Exodermal Tumor',
          'Embryonal Tumor with Multilayered Rosettes',
          'Epithelioid Sarcoma',
          'Esophageal Adrenocarcinoma',
          'Ewings Sarcoma',
          'Extra Renal Rhabdoid Tumour',
          'Fibrolamellar Carcinoma',
          'Gastrointestinal Stromal Tumors (GIST)',
          'Germ Cell Tumor',
          'Hepatocellular Carcinoma',
          'High-Grade Neuroepithelial Tumor',
          'Histiocytic Sarcoma',
          'Langerhans Cell Histiocytosis',
          'Mesenchymal Chondrosarcoma',
          'Myoepithelial Carcinoma',
          'Nasopharyngeal Carcinoma',
          'Neuroendocrine Tumors',
          'Non-germinomatous germ cell tumor',
          'Opsoclonus Myoclonus Ataxia Secondary Neuroblastoma',
          'Parathyroid Carcinoma',
          'Pineal Parenchymal Tumor of Intermediate Differentiation',
          'Pineoblastoma',
          'Pleuropulmonary Blastoma',
          'Pluromalmonary Blastoma',
          'Primitive Neuro Exodermal Tumor',
          'Retinoblastoma',
          'Sacrococcygeal Teratoma',
          'Soft Tissue Sarcoma',
          'Synovial Sarcoma',
          'Systemic Mastocytosis',
          'Undifferentiated Embryonal Sarcoma',
          'Undifferentiated Sarcoma internal tandem duplication',
          'Waldenström Macroglobulinemia',
          'Wilms Tumor',
        ],
      },
      {
        name: 'Skin Cancer',
        subTypes: ['Basal Cell Skin Cancer', 'Malignant Melanoma', 'Squamous Cell Carcinoma'],
      },
      {
        name: 'Stomach/Esophageal Cancer',
        subTypes: ['Esophageal Cancer', 'Stomach Cancer'],
      },
      {
        name: 'Thyroid Cancer',
        subTypes: ['Thyroid Cancer'],
      },
    ],
  },
  diagnosisStageTypes: {
    'Ductal Carcinoma': ['0 (DCIS/LCIS)', 'Ia', 'Ib', 'IIa', 'IIb', 'IIIa', 'IIIb', 'IIIc', 'IV-metastatic', 'unknown/prefer not to say'],
  },
  patientBiopsyReports: ['yes', 'no', 'unknown/prefer not to say'],
  cancerJourneyStage: [
    'newly diagnosed',
    'in active treatment',
    'in maintenance treatment',
    'completed treatment within <5 yrs',
    'completed treatment >5 years ago',
    'supporting a loved one navigating cancer',
  ],
  additionalDiagonsisAppliers: {
    'Ductal Carcinoma': ['PR Positive', 'ER Positive', 'HER2 Positive', 'TNBC (none of the above apply)', 'unknown/prefer not to say'],
    'Lobular Carcinoma': ['PR Positive', 'ER Positive', 'HER2 Positive', 'TNBC (none of the above apply)', 'unknown/prefer not to say'],
    'Inflammatory Breast Cancer': ['PR Positive', 'ER Positive', 'HER2 Positive', 'TNBC (none of the above apply)', 'unknown/prefer not to say'],
    unknown: ['PR Positive', 'ER Positive', 'HER2 Positive', 'TNBC (none of the above apply)', 'unknown/prefer not to say'],
    'Bone Cancer': ['Bone metastases from solid tumors', 'Giant cell tumor of bone'],
    'Chronic kidney disease (CKD)': ['Dialysis', 'Myelosuppressive chemotherapy'],
  },
  diagnonsisStageFields: ['Ductal Carcinoma'],
  her2Statuses: ['PR Positive', 'ER Positive', 'HER2 Positive', 'TNBC (none of the above apply)', 'unknown/prefer not to say'],
  personalizedHelp: ['Yes', 'No'],
  diagnosisDates: Array.from({ length: 50 }, (_, index) => {
    const year = new Date().getFullYear() - index;
    return String(year); // January 1st of the given year
  }),
  diseaseTypes: {
    boneHealth: 'Bone Health',
    respiratory: 'Respiratory',
    cardiovascular: 'Cardiovascular',
    inflammation: 'Inflammation',
    nephrology: 'Nephrology',
    cancer: 'Cancer',
    neurology: 'Neurology',
  },
} as const;
