"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createTRPCReact, httpBatchLink } from "@trpc/react-query";
import { getSession } from "next-auth/react";
import <PERSON><PERSON><PERSON><PERSON> from "superjson";
import { AppRouter } from "../../../backend/app";
import mixpanel from "mixpanel-browser";

mixpanel.init(process.env.NEXT_PUBLIC_MIXPANEL_TOKEN!);

// @ts-ignore
export const trpc = createTRPCReact<AppRouter>();

// trpc client
const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: process.env.NEXT_PUBLIC_TRPC_URL!,
      headers: async () => {
        const session = await getSession();
        return {
          "x-auth-token": session?.user.token,
        };
      },
    }),
  ],
  transformer: SuperJSON,
});
// query client
const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      onError: (err) => {
        // if (err instanceof Error) toast.error(err.message);
      },
    },
  },
});

export default function Providers({ children }: React.PropsWithChildren) {
  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </trpc.Provider>
  );
}
