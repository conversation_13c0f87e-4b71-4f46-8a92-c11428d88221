import { zodResolver } from "@hookform/resolvers/zod";
import { FormComboBoxPopover } from "@web/components/form/FormComboPopover";
import <PERSON>Field from "@web/components/form/FormField";
import S3FileUpload from "@web/components/form/S3FileUpload";
import { FormTextField } from "@web/components/form/FormTextField";
import { Button } from "@web/components/ui/button";
import { Label } from "@web/components/ui/label";
import { ScrollArea } from "@web/components/ui/scroll-area";
import { trpc } from "@web/providers/Providers";
import { createCategoryValidator } from "packages/shared/validators/service.validator";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { toastPromise } from "@web/lib/utils";

type Form = z.infer<typeof createCategoryValidator>;

export default function CreateCategory({
  isEditMode = false,
}: {
  isEditMode?: boolean;
}) {
  const util = trpc.useUtils();
  const createCategory = trpc.service.createCategory.useMutation();

  const methods = useForm<Form>({
    resolver: zodResolver(createCategoryValidator),
    defaultValues: {
      description: "",
      logo: "",
      title: "",
    },
  });

  const handleSubmit = methods.handleSubmit((data) =>
    toastPromise({
      asyncFunc: createCategory.mutateAsync(data),
      success: "category created successfully",
      onSuccess: () => util.service.getCategories.invalidate(),
    })
  );

  return (
    <ScrollArea>
      <form className="m-3" onSubmit={handleSubmit}>
        <FormProvider {...methods}>
          <Label>Create Categories</Label>
          <div className="mt-2 flex flex-col gap-4">
            <FormField
              name="title"
              label="Category Name"
              placeholder="Enter category name name"
              disabled={isEditMode}
            />
            <FormTextField
              name="description"
              label="Enter Category Description"
              placeholder="Enter service description"
              disabled={isEditMode}
            />

            <S3FileUpload
              name="logo"
              path="service-category"
              prefix="service-category"
              disabled={isEditMode}
            />
            <Button disabled={isEditMode}>Create Category</Button>
          </div>
        </FormProvider>
      </form>
    </ScrollArea>
  );
}
