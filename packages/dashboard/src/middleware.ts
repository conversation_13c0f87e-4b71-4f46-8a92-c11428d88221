import { getToken } from "next-auth/jwt";
import { withAuth } from "next-auth/middleware";
import { NextFetchEvent, NextRequest, NextResponse } from "next/server";

const publicPages = [
  "/terms_conditions",
  "/groups",
  "/profile",
  "/blogs",
  "/learn",
  "/download",
];

const exactPaths = ["/manage/download-reports"];

export default async function middleware(
  req: NextRequest,
  event: NextFetchEvent
) {
  const token = await getToken({ req });
  const isAuthenticated = !!token;

  if (publicPages.some((page) => req.nextUrl.pathname.includes(page))) return;
  if (exactPaths.some((page) => req.nextUrl.pathname === page)) return;
  if (req.nextUrl.pathname.includes(".")) return;
  if (req.nextUrl.pathname.startsWith("/login") && isAuthenticated) {
    return NextResponse.redirect(new URL("/", req.url));
  }

  const authMiddleware = await withAuth({
    pages: {
      signIn: `/login`,
    },
  });

  // @ts-expect-error
  return authMiddleware(req, event);
}
