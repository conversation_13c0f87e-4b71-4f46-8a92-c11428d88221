"use client";

import { useState, use<PERSON>emo, useEffect } from "react";
import { But<PERSON> } from "@web/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import { toastPromise } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@web/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogAction,
  AlertDialogCancel,
} from "@web/components/ui/alert-dialog";
import { Label } from "@web/components/ui/label";
import { Badge } from "@web/components/ui/badge";
import {
  Trash,
  Plus,
  Tag as TagIcon,
  Filter,
  FilterX,
  Edit,
} from "lucide-react";
import { Checkbox } from "@web/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@web/components/ui/popover";
import {
  createBlogTagValidator,
  updateBlogTagValidator,
} from "packages/shared/validators/blog";
import { z } from "zod";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@web/components/ui/card";
import { Alert, AlertDescription } from "@web/components/ui/alert";
import FormField from "@web/components/form/FormField";
import { FormMultiSelect } from "@web/components/form/FormMultiSelect";

type TagForm = z.infer<typeof createBlogTagValidator>;
type EditTagForm = z.infer<typeof updateBlogTagValidator>;

export default function Tags() {
  const [open, setOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [currentTag, setCurrentTag] = useState<any>(null);
  const [error, setError] = useState("");
  const [editError, setEditError] = useState("");

  const [selectedScopeFilters, setSelectedScopeFilters] = useState<string[]>([
    "learn",
    "rabbles",
    "services",
  ]);
  const [filterPopoverOpen, setFilterPopoverOpen] = useState(false);

  // Get all tags first to extract available scope values
  const allTags = trpc.blog.tags.useQuery();

  // Get filtered tags based on selected scope filters
  const tags = trpc.blog.tags.useQuery(
    selectedScopeFilters.length > 0
      ? { scope: selectedScopeFilters }
      : undefined
  );

  const deleteTag = trpc.blog.deleteBlogTag.useMutation();
  const createTag = trpc.blog.createBlogTag.useMutation();
  const updateTag = trpc.blog.updateBlogTag.useMutation();

  // Extract all unique scope values from all tags
  const availableScopeValues = useMemo(() => {
    if (!allTags.data) return [];
    const scopes = allTags.data.flatMap((tag) => tag.scope || []);
    return Array.from(new Set(scopes)).sort();
  }, [allTags.data]);

  // Convert scope values to options format for FormMultiSelect
  const scopeOptions = useMemo(() => {
    return availableScopeValues.map((scope) => ({
      value: scope,
      label: scope,
    }));
  }, [availableScopeValues]);

  const methods = useForm<TagForm>({
    resolver: zodResolver(createBlogTagValidator),
    defaultValues: {
      tag: "",
      scope: [],
    },
  });

  const editMethods = useForm<EditTagForm>({
    resolver: zodResolver(updateBlogTagValidator),
    defaultValues: {
      tagId: "",
      tag: "",
      scope: [],
    },
  });

  // Filter management functions
  const toggleScopeFilter = (scope: string) => {
    setSelectedScopeFilters((prev) =>
      prev.includes(scope) ? prev.filter((s) => s !== scope) : [...prev, scope]
    );
  };

  const resetToDefaultFilters = () => {
    setSelectedScopeFilters(["learn", "rabbles", "services"]);
  };

  const clearAllFilters = () => {
    setSelectedScopeFilters([]);
  };

  const isDefaultFilters = () => {
    const defaultFilters = ["learn", "rabbles", "services"];
    return (
      selectedScopeFilters.length === defaultFilters.length &&
      selectedScopeFilters.every((filter) => defaultFilters.includes(filter))
    );
  };

  // Refetch tags when filters change
  useEffect(() => {
    tags.refetch();
  }, [selectedScopeFilters]);

  const handleDeleteTag = async (tagId: string) => {
    if (confirm("Are you sure you want to delete this tag?")) {
      toastPromise({
        asyncFunc: deleteTag.mutateAsync(tagId),
        success: "Tag deleted successfully",
        error: "Failed to delete tag",
        onSuccess() {
          tags.refetch();
          allTags.refetch(); // Refetch to update available scope values
        },
      });
    }
  };

  const handleEditTag = (tag: any) => {
    setCurrentTag(tag);
    editMethods.reset({
      tagId: String(tag._id),
      tag: tag.tag,
      scope: tag.scope || [],
    });
    setEditError("");
    setEditOpen(true);
  };

  const handleCreateTag = methods.handleSubmit(async (data) => {
    // Check if tag already exists
    const tagExists = tags.data?.some(
      (t) => t.tag.toLowerCase() === data.tag.toLowerCase()
    );

    if (tagExists) {
      setError("This tag already exists");
      return;
    }

    toastPromise({
      asyncFunc: createTag.mutateAsync(data),
      success: "Tag created successfully",
      error: "Failed to create tag",
      onSuccess() {
        setError("");
        setOpen(false);
        tags.refetch();
        allTags.refetch(); // Refetch to update available scope values
        methods.reset();
      },
    });
  });

  const handleUpdateTag = editMethods.handleSubmit(async (data) => {
    // Check if tag name already exists (excluding current tag)
    const tagExists = tags.data?.some(
      (t) =>
        t.tag.toLowerCase() === data.tag?.toLowerCase() &&
        String(t._id) !== data.tagId
    );

    if (tagExists) {
      setEditError("This tag name already exists");
      return;
    }

    toastPromise({
      asyncFunc: updateTag.mutateAsync(data),
      success: "Tag updated successfully",
      error: "Failed to update tag",
      onSuccess() {
        setEditError("");
        setEditOpen(false);
        tags.refetch();
        allTags.refetch(); // Refetch to update available scope values
        editMethods.reset();
        setCurrentTag(null);
      },
    });
  });

  return (
    <div className="container py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold flex items-center">
          <TagIcon className="mr-2 h-6 w-6" /> Manage Tags
        </h1>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add New Tag
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New Tag</DialogTitle>
              <DialogDescription>
                Add a new tag for categorizing blog posts with optional scope.
              </DialogDescription>
            </DialogHeader>
            <FormProvider {...methods}>
              <form onSubmit={handleCreateTag} className="space-y-4">
                <FormField
                  name="tag"
                  label="Tag Name"
                  placeholder="Enter tag name"
                />

                <FormMultiSelect
                  name="scope"
                  label="Scope (Optional)"
                  placeholder="Select or create scope values..."
                  options={scopeOptions}
                  creatable={true}
                  hidePlaceholderWhenSelected={true}
                  emptyIndicator={
                    <p className="text-center text-sm text-muted-foreground">
                      No scope values found. Type to create a new one.
                    </p>
                  }
                />

                {error && <p className="text-sm text-destructive">{error}</p>}
              </form>
            </FormProvider>
            <DialogFooter>
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateTag}>Create Tag</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Blog Tags</span>
            <div className="flex items-center gap-2">
              {/* Filter Results Count */}

              {/* Active Filters Indicator */}
              {selectedScopeFilters.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {selectedScopeFilters.length} filter
                  {selectedScopeFilters.length !== 1 ? "s" : ""} applied
                </Badge>
              )}

              {/* Filter Controls */}
              <div className="flex gap-2">
                {selectedScopeFilters.length > 0 && (
                  <>
                    {!isDefaultFilters() && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={resetToDefaultFilters}
                        className="h-8"
                      >
                        <FilterX className="h-4 w-4 mr-1" />
                        Reset to Default
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearAllFilters}
                      className="h-8"
                    >
                      <FilterX className="h-4 w-4 mr-1" />
                      Clear All Filters
                    </Button>
                  </>
                )}

                <Popover
                  open={filterPopoverOpen}
                  onOpenChange={setFilterPopoverOpen}
                >
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="sm" className="h-8">
                      <Filter className="h-4 w-4 mr-1" />
                      Filter by Scope
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-64" align="end">
                    <div className="space-y-3">
                      <div className="font-medium text-sm">Filter by Scope</div>
                      {availableScopeValues.length === 0 ? (
                        <p className="text-sm text-muted-foreground">
                          No scope values available
                        </p>
                      ) : (
                        <div className="space-y-2">
                          {availableScopeValues.map((scope) => (
                            <div
                              key={scope}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`scope-${scope}`}
                                checked={selectedScopeFilters.includes(scope)}
                                onCheckedChange={() => toggleScopeFilter(scope)}
                              />
                              <Label
                                htmlFor={`scope-${scope}`}
                                className="text-sm font-normal cursor-pointer"
                              >
                                {scope}
                              </Label>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {tags.isLoading ? (
            <div className="flex justify-center py-8">Loading tags...</div>
          ) : tags.data?.length === 0 ? (
            <Alert>
              <AlertDescription>
                {selectedScopeFilters.length > 0
                  ? "No tags found matching the selected scope filters. Try adjusting your filters or create a new tag."
                  : "No tags found. Create your first tag to get started."}
              </AlertDescription>
            </Alert>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">#</TableHead>
                  <TableHead>Tag</TableHead>
                  <TableHead>Scope</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tags.data?.map((t, idx) => (
                  <TableRow key={String(t._id)}>
                    <TableCell>{idx + 1}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="px-3 py-1">
                        {t.tag}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {t.scope && t.scope.length > 0 ? (
                          t.scope.map((scopeItem, scopeIdx) => (
                            <Badge
                              key={scopeIdx}
                              variant="secondary"
                              className="text-xs"
                            >
                              {scopeItem}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            No scope
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center gap-2 justify-end">
                        <Button
                          onClick={() => handleEditTag(t)}
                          variant="outline"
                          size="sm"
                        >
                          <Edit className="h-4 w-4 mr-1" /> Edit
                        </Button>
                        <Button
                          onClick={() => handleDeleteTag(String(t._id))}
                          variant="destructive"
                          size="sm"
                        >
                          <Trash className="h-4 w-4 mr-1" /> Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Tag Dialog */}
      <AlertDialog open={editOpen} onOpenChange={setEditOpen}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Edit Tag</AlertDialogTitle>
            <AlertDialogDescription>
              Update the tag details below.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <FormProvider {...editMethods}>
            <form onSubmit={handleUpdateTag} className="space-y-4">
              <FormField
                name="tag"
                label="Tag Name"
                placeholder="Enter tag name"
              />

              <FormMultiSelect
                name="scope"
                label="Scope (Optional)"
                placeholder="Select or create scope values..."
                options={scopeOptions}
                creatable={true}
                hidePlaceholderWhenSelected={true}
                emptyIndicator={
                  <p className="text-center text-sm text-muted-foreground">
                    No scope values found. Type to create a new one.
                  </p>
                }
              />

              {editError && (
                <p className="text-sm text-destructive">{editError}</p>
              )}
            </form>
          </FormProvider>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setEditOpen(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleUpdateTag}>
              Update Tag
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
