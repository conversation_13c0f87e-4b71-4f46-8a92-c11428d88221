"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON><PERSON><PERSON>og,
  AlertDialog<PERSON>ontent,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@web/components/ui/alert-dialog";
import { Badge } from "@web/components/ui/badge";
import { Button } from "@web/components/ui/button";
import { Checkbox } from "@web/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import ManageBlogPostForm from "@web/elements/form/ManageBlogPost";
import { toastPromise } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";
import { format } from "date-fns";
import {
  Edit,
  Trash,
  X,
  ChevronUp,
  ChevronDown,
  ChevronsUpDown,
  Tags,
} from "lucide-react";
import { FormMultiSelect } from "@web/components/form/FormMultiSelect";
import { FormSelect } from "@web/components/form/FormSelect";
import { FormDatePicker } from "@web/components/form/FormDatePicker";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState, useMemo } from "react";
import { SubmitHandler, useForm, FormProvider } from "react-hook-form";
import { z } from "zod";
import { RouterOutput } from "../../../../shared";
import { BlogStatus } from "../../../../shared/types/Blog";
import { manageBlogValidator } from "../../../../shared/validators/blog";

type BlogForm = z.infer<typeof manageBlogValidator>;

type SortField = "title" | "author" | "status" | "publishDate";
type SortDirection = "asc" | "desc";

export default function UpdateBlog() {
  const [open, setOpen] = useState(false);

  // Sorting state
  const [sortField, setSortField] = useState<SortField>("publishDate");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");

  // Bulk selection state
  const [selectedBlogIds, setSelectedBlogIds] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);

  const blogs = trpc.blog.getBlogs.useQuery();
  const blogTags = trpc.blog.tags.useQuery();

  const methods = useForm<BlogForm>({
    resolver: zodResolver(manageBlogValidator),
    defaultValues: {
      author: "",
      banner: "",
      blogId: "",
      contentHtml: "",
      description: "",
      status: BlogStatus.DRAFT,
      tags: [],
      timeToReadInMins: "" as unknown as number,
      title: "",
      editorData: [],
    },
  });

  const createBlog = trpc.blog.createBlog.useMutation();
  const deleteBlog = trpc.blog.deleteBlog.useMutation();
  const router = useRouter();

  // Form for bulk updates
  const bulkUpdateForm = useForm({
    defaultValues: {
      tags: [] as string[],
      status: "" as BlogStatus | "",
      publishDate: undefined as Date | undefined,
    },
  });

  // Sorted and filtered blogs
  const sortedBlogs = useMemo(() => {
    if (!blogs.data) return [];

    const sorted = [...blogs.data].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortField) {
        case "title":
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case "author":
          aValue = a.author.toLowerCase();
          bValue = b.author.toLowerCase();
          break;
        case "status":
          aValue = a.status;
          bValue = b.status;
          break;
        case "publishDate":
          aValue = new Date(a.publishDate).getTime();
          bValue = new Date(b.publishDate).getTime();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [blogs.data, sortField, sortDirection]);

  // Handle column sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Handle bulk selection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = sortedBlogs.map((blog) => String(blog._id));
      setSelectedBlogIds(allIds);
      setShowBulkActions(true);
    } else {
      setSelectedBlogIds([]);
      setShowBulkActions(false);
    }
  };

  const handleSelectBlog = (blogId: string, checked: boolean) => {
    if (checked) {
      const newSelected = [...selectedBlogIds, blogId];
      setSelectedBlogIds(newSelected);
      setShowBulkActions(newSelected.length > 0);
    } else {
      const newSelected = selectedBlogIds.filter((id) => id !== blogId);
      setSelectedBlogIds(newSelected);
      setShowBulkActions(newSelected.length > 0);
    }
  };

  // Create separate mutations for each bulk update operation
  const bulkUpdateBlogTags = trpc.blog.bulkUpdateBlogs.useMutation();
  const bulkUpdateBlogStatus = trpc.blog.bulkUpdateBlogs.useMutation();
  const bulkUpdateBlogPublishDate = trpc.blog.bulkUpdateBlogs.useMutation();

  // Handle bulk updates
  const handleBulkTagAssignment = () => {
    const selectedTags = bulkUpdateForm.getValues("tags");
    if (selectedBlogIds.length === 0 || selectedTags.length === 0) return;

    toastPromise({
      asyncFunc: bulkUpdateBlogTags.mutateAsync({
        blogIds: selectedBlogIds,
        tagIds: selectedTags,
      }),
      success: `Successfully updated ${selectedBlogIds.length} blog(s) with new tags`,
      onSuccess: () => {
        blogs.refetch();
        setSelectedBlogIds([]);
        bulkUpdateForm.reset();
        setShowBulkActions(false);
      },
    });
  };

  const handleBulkStatusUpdate = () => {
    const selectedStatus = bulkUpdateForm.getValues("status");
    if (selectedBlogIds.length === 0 || !selectedStatus) return;

    toastPromise({
      asyncFunc: bulkUpdateBlogStatus.mutateAsync({
        blogIds: selectedBlogIds,
        status: selectedStatus as BlogStatus,
      }),
      success: `Successfully updated ${selectedBlogIds.length} blog(s) status to ${selectedStatus}`,
      onSuccess: () => {
        blogs.refetch();
        setSelectedBlogIds([]);
        bulkUpdateForm.reset();
        setShowBulkActions(false);
      },
    });
  };

  const handleBulkPublishDateUpdate = () => {
    const selectedDate = bulkUpdateForm.getValues("publishDate");
    if (selectedBlogIds.length === 0 || !selectedDate) return;

    toastPromise({
      asyncFunc: bulkUpdateBlogPublishDate.mutateAsync({
        blogIds: selectedBlogIds,
        publishDate: selectedDate,
      }),
      success: `Successfully updated ${selectedBlogIds.length} blog(s) publish date`,
      onSuccess: () => {
        blogs.refetch();
        setSelectedBlogIds([]);
        bulkUpdateForm.reset();
        setShowBulkActions(false);
      },
    });
  };

  // Get sort icon for column headers
  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ChevronsUpDown className="h-4 w-4" />;
    }
    return sortDirection === "asc" ? (
      <ChevronUp className="h-4 w-4" />
    ) : (
      <ChevronDown className="h-4 w-4" />
    );
  };

  // Tag options for bulk assignment
  const tagOptions = useMemo(() => {
    return (blogTags.data || []).map((tag) => ({
      value: String(tag._id),
      label: tag.tag,
    }));
  }, [blogTags.data]);

  const isAllSelected =
    selectedBlogIds.length === sortedBlogs.length && sortedBlogs.length > 0;
  const isIndeterminate =
    selectedBlogIds.length > 0 && selectedBlogIds.length < sortedBlogs.length;

  const handleSubmit: SubmitHandler<BlogForm> = (data) => {
    // Validate required fields
    if (!data.blogId) {
      console.error(
        "UpdateBlog form errors: Missing blogId for update operation"
      );
      return;
    }

    // Handle both string array and object array from FormMultiSelect
    let tagNames: string[] = [];
    if (Array.isArray(data.tags)) {
      tagNames = data.tags.map((tag: any) => {
        // Handle both object format {value: 'tagName', label: 'tagName'} and string format
        if (typeof tag === "object" && tag.value) {
          return tag.value;
        }
        return typeof tag === "string" ? tag : String(tag);
      });
    }

    // Map tag names to tag IDs
    const selectedTagIds = tagNames
      .map((tagName) =>
        blogTags.data?.find((tag) => tag.tag === tagName)?._id.toString()
      )
      .filter((id): id is string => Boolean(id));

    const finalData = {
      ...data,
      tags: selectedTagIds,
      // diseaseTags field is deprecated, use tags instead
    };

    toastPromise({
      asyncFunc: createBlog.mutateAsync(finalData),
      success: "Blog Updated Successfully",
      error: "Failed to update blog",
      onError: (error) => {
        console.error("UpdateBlog form errors:", error);
      },
      onSuccess: () => {
        methods.reset();
        router.replace("/blogs");
        blogs.refetch();
        setOpen(false);
      },
    });
  };

  const onBlogSelect = (blog: RouterOutput["blog"]["getBlogs"][number]) => {
    methods.setValue("title", blog.title);
    methods.setValue("author", blog.author);
    methods.setValue("banner", blog.banner as string);
    methods.setValue("blogId", String(blog._id));
    methods.setValue("contentHtml", blog.contentHtml);
    methods.setValue("description", blog.description);
    methods.setValue("publishDate", blog.publishDate);
    // diseaseTags field is deprecated, use tags instead
    methods.setValue("status", blog.status);
    methods.setValue("editorData", blog.editorData);
    methods.setValue(
      "tags",
      // @ts-ignore
      (blog.tags || []).map((_) => String(_.tag))
    );
    methods.setValue("timeToReadInMins", blog.timeToReadInMins as number);

    setOpen(true);
  };

  return (
    <div className="container flex flex-col  ">
      <div className="flex  justify-end mt-5 gap-4">
        <Button asChild>
          <Link href={"/blogs/layout"}>Manage Layout</Link>
        </Button>
        <Button asChild>
          <Link href={"/blogs/create"}>Create Blog</Link>
        </Button>
        <Button asChild>
          <Link href={"/blogs/manage"}>Dashboard</Link>
        </Button>
      </div>

      {/* Update Blogpost dialog */}
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent className="min-w-[80vw] ">
          {/* Close button */}
          <button
            onClick={() => setOpen(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
            aria-label="Close"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>

          <AlertDialogHeader>
            <AlertDialogTitle>Update Blog Post</AlertDialogTitle>
          </AlertDialogHeader>
          <div className="max-h-[70vh] overflow-y-scroll">
            <ManageBlogPostForm
              methods={methods}
              blogTags={blogTags.data || []}
              onFormSubmit={handleSubmit}
            />
          </div>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Actions Section */}
      {showBulkActions && (
        <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-900">
                {selectedBlogIds.length} blog(s) selected
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedBlogIds([]);
                  setShowBulkActions(false);
                  bulkUpdateForm.reset();
                }}
              >
                Clear Selection
              </Button>
            </div>

            <FormProvider {...bulkUpdateForm}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Tags Section */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Tags
                  </label>
                  <div className="flex gap-2">
                    <FormMultiSelect
                      name="tags"
                      label=""
                      placeholder="Select tags..."
                      options={tagOptions}
                    />
                    <Button
                      onClick={handleBulkTagAssignment}
                      disabled={
                        bulkUpdateForm.watch("tags").length === 0 ||
                        bulkUpdateBlogTags.isLoading
                      }
                      size="sm"
                    >
                      <Tags className="h-4 w-4 mr-1" />
                      {bulkUpdateBlogTags.isLoading
                        ? "Updating..."
                        : "Update Tags"}
                    </Button>
                  </div>
                </div>

                {/* Status Section */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Status
                  </label>
                  <div className="flex gap-2">
                    <FormSelect
                      name="status"
                      label=""
                      placeholder="Select status..."
                      options={[
                        { value: BlogStatus.DRAFT, label: "Draft" },
                        { value: BlogStatus.PUBLISHED, label: "Published" },
                      ]}
                    />
                    <Button
                      onClick={handleBulkStatusUpdate}
                      disabled={
                        !bulkUpdateForm.watch("status") ||
                        bulkUpdateBlogStatus.isLoading
                      }
                      size="sm"
                    >
                      {bulkUpdateBlogStatus.isLoading
                        ? "Updating..."
                        : "Update Status"}
                    </Button>
                  </div>
                </div>

                {/* Publish Date Section */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Publish Date
                  </label>
                  <div className="flex gap-2">
                    <FormDatePicker
                      name="publishDate"
                      label=""
                      placeholder="Select date..."
                    />
                    <Button
                      onClick={handleBulkPublishDateUpdate}
                      disabled={
                        !bulkUpdateForm.watch("publishDate") ||
                        bulkUpdateBlogPublishDate.isLoading
                      }
                      size="sm"
                    >
                      {bulkUpdateBlogPublishDate.isLoading
                        ? "Updating..."
                        : "Update Date"}
                    </Button>
                  </div>
                </div>
              </div>
            </FormProvider>
          </div>
        </div>
      )}

      <Table className="mt-8">
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                onCheckedChange={handleSelectAll}
                aria-label="Select all blogs"
                className={
                  isIndeterminate ? "data-[state=checked]:bg-blue-600" : ""
                }
              />
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => handleSort("title")}
            >
              <div className="flex items-center gap-2">
                Blog Title
                {getSortIcon("title")}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => handleSort("author")}
            >
              <div className="flex items-center gap-2">
                Author
                {getSortIcon("author")}
              </div>
            </TableHead>
            <TableHead>Tags</TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => handleSort("status")}
            >
              <div className="flex items-center gap-2">
                Status
                {getSortIcon("status")}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => handleSort("publishDate")}
            >
              <div className="flex items-center gap-2">
                Publish Date
                {getSortIcon("publishDate")}
              </div>
            </TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedBlogs.map((b) => {
            const blogId = String(b._id);
            const isSelected = selectedBlogIds.includes(blogId);

            return (
              <TableRow key={blogId} className={isSelected ? "bg-blue-50" : ""}>
                <TableCell>
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={(checked) =>
                      handleSelectBlog(blogId, checked as boolean)
                    }
                    aria-label={`Select ${b.title}`}
                  />
                </TableCell>
                <TableCell>{b.title}</TableCell>
                <TableCell>{b.author}</TableCell>
                <TableCell className="space-x-2">
                  {b.tags?.map((t) => {
                    return (
                      <Badge key={String(t._id)} className="rounded-full">
                        {t.tag}
                      </Badge>
                    );
                  })}
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      b.status === BlogStatus.PUBLISHED
                        ? "default"
                        : "secondary"
                    }
                    className="rounded-full"
                  >
                    {b.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  {b.publishDate && format(b.publishDate, "PPP")}
                </TableCell>
                <TableCell className="flex items-center gap-2">
                  <Button onClick={() => onBlogSelect(b)} size="sm">
                    <Edit className="h-4 w-4 mr-1" />
                    Update
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() =>
                      toastPromise({
                        asyncFunc: deleteBlog.mutateAsync(String(b._id)),
                        success: "blog deleted successfully",
                        onSuccess() {
                          blogs.refetch();
                          // Remove from selection if it was selected
                          setSelectedBlogIds((prev) =>
                            prev.filter((id) => id !== blogId)
                          );
                        },
                      })
                    }
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
