"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { manageBlogsDashboardValidator } from "../../../../../shared/validators/blog";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import S3FileUpload from "@web/components/form/S3FileUpload";
import Form<PERSON>ield from "@web/components/form/FormField";
import { Button } from "@web/components/ui/button";
import { Label } from "@web/components/ui/label";
import { toastPromise } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";

type Form = z.infer<typeof manageBlogsDashboardValidator>;

export default function Dashboard() {
  const methods = useForm<Form>({
    defaultValues: { bannerImage: "", title: "" },
    resolver: zodResolver(manageBlogsDashboardValidator),
  });

  const manageDashboard = trpc.blog.manageBlogDashboard.useMutation();

  const handleSubmit = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: manageDashboard.mutateAsync(data),
      onSuccess: () => {
        methods.reset();
      },
    });
  });

  return (
    <div className="p-4">
      <h2 className="font-medium text-xl">Dashboard</h2>

      <form
        className="space-y-4 max-w-xl p-4 border rounded-xl"
        onSubmit={handleSubmit}
      >
        <FormProvider {...methods}>
          <div>
            <Label>Select Banner image</Label>
            <S3FileUpload name="bannerImage" path="blogs" prefix="blog" />
          </div>
          <FormField name="title" label="Title" placeholder="Enter title" />
          <Button>Save</Button>
        </FormProvider>
      </form>
    </div>
  );
}
