"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
  Alert<PERSON>ialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@web/components/ui/alert-dialog";
import { Badge } from "@web/components/ui/badge";
import { Button } from "@web/components/ui/button";
import { Checkbox } from "@web/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import { toastPromise } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";

import {
  Edit,
  Trash,
  X,
  Plus,
  Image as ImageIcon,
  ArrowUpDown,
  Eye,
} from "lucide-react";
import { FormMultiSelect } from "@web/components/form/FormMultiSelect";
import Link from "next/link";
import { useState, useMemo } from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm, FormProvider } from "react-hook-form";
import { z } from "zod";
import { RouterOutput } from "packages/shared";
import { manageBlogLayoutValidator } from "packages/shared/validators/blog";
import { BlogStatus } from "packages/shared/types/Blog";
import FormField from "@web/components/form/FormField";
import S3FileUpload from "@web/components/form/S3FileUpload";

import { Label } from "@web/components/ui/label";

type BlogLayoutForm = z.infer<typeof manageBlogLayoutValidator>;

type SortField = "sectionName" | "sequence" | "blogCount";
type SortDirection = "asc" | "desc";

export default function BlogLayoutPage() {
  const [open, setOpen] = useState(false);
  const [editingLayout, setEditingLayout] = useState<string | null>(null);
  const [selectedLayoutIds, setSelectedLayoutIds] = useState<string[]>([]);
  const [sortField, setSortField] = useState<SortField>("sequence");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [previewLayoutId, setPreviewLayoutId] = useState<string | null>(null);

  const methods = useForm<BlogLayoutForm>({
    resolver: zodResolver(manageBlogLayoutValidator),
    defaultValues: {
      sectionName: "",
      image: "",
      blogs: [],
      sequence: 0,
    },
  });

  // API calls
  const blogLayouts = trpc.blog.getBlogLayouts.useQuery({});
  const blogs = trpc.blog.getBlogs.useQuery({ status: BlogStatus.PUBLISHED });
  const createBlogLayout = trpc.blog.createBlogLayout.useMutation();
  const updateBlogLayout = trpc.blog.updateBlogLayout.useMutation();
  const deleteBlogLayout = trpc.blog.deleteBlogLayout.useMutation();

  // Get layout for preview
  const previewLayout = trpc.blog.getBlogLayout.useQuery(
    { layoutId: previewLayoutId! },
    { enabled: !!previewLayoutId }
  );

  const handleSubmit: SubmitHandler<BlogLayoutForm> = (data) => {
    const mutation = editingLayout ? updateBlogLayout : createBlogLayout;
    const payload = editingLayout ? { ...data, layoutId: editingLayout } : data;

    toastPromise({
      asyncFunc: mutation.mutateAsync(payload),
      onSuccess: () => {
        methods.reset();
        setOpen(false);
        setEditingLayout(null);
        blogLayouts.refetch();
      },
    });
  };

  const handleEdit = (layout: RouterOutput["blog"]["getBlogLayouts"][0]) => {
    setEditingLayout(layout._id!.toString());
    methods.reset({
      sectionName: layout.sectionName,
      image: layout.image,
      blogs: layout.blogs?.map((blog: any) => blog._id) || [],
      sequence: layout.sequence || 0,
    });
    setOpen(true);
  };

  const handleDelete = (layoutId: string) => {
    if (confirm("Are you sure you want to delete this layout?")) {
      toastPromise({
        asyncFunc: deleteBlogLayout.mutateAsync({ layoutId }),
        onSuccess: () => {
          blogLayouts.refetch();
        },
      });
    }
  };

  const handleBulkDelete = () => {
    if (
      selectedLayoutIds.length > 0 &&
      confirm(
        `Are you sure you want to delete ${selectedLayoutIds.length} layout(s)?`
      )
    ) {
      Promise.all(
        selectedLayoutIds.map((layoutId) =>
          deleteBlogLayout.mutateAsync({ layoutId })
        )
      ).then(() => {
        setSelectedLayoutIds([]);
        blogLayouts.refetch();
      });
    }
  };

  // Blog options for multi-select
  const blogOptions = useMemo(() => {
    return (
      blogs.data?.map((blog) => ({
        label: blog.title,
        value: blog._id!.toString(),
      })) || []
    );
  }, [blogs.data]);

  // Sorted layouts
  const sortedLayouts = useMemo(() => {
    if (!blogLayouts.data) return [];

    return [...blogLayouts.data].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortField) {
        case "sectionName":
          aValue = a.sectionName.toLowerCase();
          bValue = b.sectionName.toLowerCase();
          break;
        case "sequence":
          aValue = a.sequence || 0;
          bValue = b.sequence || 0;
          break;

        case "blogCount":
          aValue = a.blogs?.length || 0;
          bValue = b.blogs?.length || 0;
          break;
        default:
          return 0;
      }

      if (sortDirection === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  }, [blogLayouts.data, sortField, sortDirection]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />;
    return sortDirection === "asc" ? "↑" : "↓";
  };

  return (
    <div className="container flex flex-col">
      <div className="flex justify-between items-center mt-5 mb-6">
        <h1 className="text-2xl font-bold">Blog Layouts</h1>
        <div className="flex gap-4">
          <Button
            onClick={() => {
              setEditingLayout(null);
              methods.reset({
                sectionName: "",
                image: "",
                blogs: [],
                sequence: 0,
              });
              setOpen(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Layout
          </Button>
          <Button variant="outline">
            <Link href="/blogs">Back to Blogs</Link>
          </Button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedLayoutIds.length > 0 && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-900">
              {selectedLayoutIds.length} layout(s) selected
            </span>
            <div className="flex gap-2">
              <Button
                variant="destructive"
                size="sm"
                onClick={handleBulkDelete}
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete Selected
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedLayoutIds([])}
              >
                Clear Selection
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Layouts Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={
                    selectedLayoutIds.length === sortedLayouts.length &&
                    sortedLayouts.length > 0
                  }
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedLayoutIds(
                        sortedLayouts.map((layout) => layout._id!.toString())
                      );
                    } else {
                      setSelectedLayoutIds([]);
                    }
                  }}
                />
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("sectionName")}
                  className="h-auto p-0 font-semibold"
                >
                  Section Name {getSortIcon("sectionName")}
                </Button>
              </TableHead>
              <TableHead>Image</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("blogCount")}
                  className="h-auto p-0 font-semibold"
                >
                  Blogs {getSortIcon("blogCount")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("sequence")}
                  className="h-auto p-0 font-semibold"
                >
                  Sequence {getSortIcon("sequence")}
                </Button>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedLayouts.map((layout) => (
              <TableRow key={layout._id!.toString()}>
                <TableCell>
                  <Checkbox
                    checked={selectedLayoutIds.includes(layout._id!.toString())}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedLayoutIds([
                          ...selectedLayoutIds,
                          layout._id!.toString(),
                        ]);
                      } else {
                        setSelectedLayoutIds(
                          selectedLayoutIds.filter(
                            (id) => id !== layout._id!.toString()
                          )
                        );
                      }
                    }}
                  />
                </TableCell>
                <TableCell className="font-medium">
                  {layout.sectionName}
                </TableCell>
                <TableCell>
                  {layout.image ? (
                    <img
                      src={layout.image}
                      alt={layout.sectionName}
                      className="w-12 h-12 object-cover rounded"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                      <ImageIcon className="h-6 w-6 text-gray-400" />
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant="secondary">
                    {layout.blogs?.length || 0} blogs
                  </Badge>
                </TableCell>
                <TableCell>{layout.sequence || 0}</TableCell>
                <TableCell>
                  <Badge variant="outline">Active</Badge>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPreviewLayoutId(layout._id!.toString())}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(layout)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(layout._id!.toString())}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Create/Edit Layout Dialog */}
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent className="min-w-[60vw]">
          <button
            onClick={() => setOpen(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
            aria-label="Close"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>

          <AlertDialogHeader>
            <AlertDialogTitle>
              {editingLayout ? "Edit Layout" : "Create Layout"}
            </AlertDialogTitle>
          </AlertDialogHeader>

          <div className="max-h-[70vh] overflow-y-scroll">
            <FormProvider {...methods}>
              <form
                onSubmit={methods.handleSubmit(handleSubmit)}
                className="space-y-4"
              >
                <FormField
                  name="sectionName"
                  label="Section Name"
                  placeholder="e.g., Asthma Diagnosis"
                />

                <div>
                  <Label>Section Image</Label>
                  <S3FileUpload name="image" path="blogs" prefix="layout" />
                </div>

                <FormField
                  name="sequence"
                  label="Sequence"
                  type="number"
                  placeholder="0"
                />

                <FormMultiSelect
                  name="blogs"
                  label="Select Blogs"
                  options={blogOptions}
                  placeholder="Choose blogs for this section"
                  creatable={false}
                />

                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingLayout ? "Update" : "Create"} Layout
                  </Button>
                </div>
              </form>
            </FormProvider>
          </div>
        </AlertDialogContent>
      </AlertDialog>

      {/* Preview Layout Dialog */}
      <AlertDialog
        open={!!previewLayoutId}
        onOpenChange={() => setPreviewLayoutId(null)}
      >
        <AlertDialogContent className="min-w-[80vw]">
          <button
            onClick={() => setPreviewLayoutId(null)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
            aria-label="Close"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>

          <AlertDialogHeader>
            <AlertDialogTitle>
              Preview: {previewLayout.data?.sectionName}
            </AlertDialogTitle>
          </AlertDialogHeader>

          <div className="max-h-[70vh] overflow-y-scroll">
            {previewLayout.data && (
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  {previewLayout.data.image && (
                    <img
                      src={previewLayout.data.image}
                      alt={previewLayout.data.sectionName}
                      className="w-16 h-16 object-cover rounded"
                    />
                  )}
                  <div>
                    <h3 className="text-lg font-semibold">
                      {previewLayout.data.sectionName}
                    </h3>
                    <p className="text-sm text-gray-600">
                      Sequence: {previewLayout.data.sequence || 0}
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-4">
                    Blogs in this section (
                    {previewLayout.data.blogs?.length || 0}):
                  </h4>
                  <div className="border rounded-lg">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Banner</TableHead>
                          <TableHead>Title</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Author</TableHead>
                          <TableHead>Read Time</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {previewLayout.data.blogs?.map((blog: any) => (
                          <TableRow key={blog._id}>
                            <TableCell>
                              {blog.banner ? (
                                <img
                                  src={blog.banner}
                                  alt={blog.title}
                                  className="w-16 h-16 object-cover rounded"
                                />
                              ) : (
                                <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                  <ImageIcon className="h-8 w-8 text-gray-400" />
                                </div>
                              )}
                            </TableCell>
                            <TableCell className="font-medium">
                              {blog.title}
                            </TableCell>
                            <TableCell className="max-w-xs">
                              <p className="text-sm text-gray-600 line-clamp-3">
                                {blog.description}
                              </p>
                            </TableCell>
                            <TableCell className="text-sm">
                              {blog.author}
                            </TableCell>
                            <TableCell className="text-sm">
                              {blog.timeToReadInMins ? (
                                <Badge variant="secondary">
                                  {blog.timeToReadInMins} min
                                </Badge>
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
