import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials";

const handler = NextAuth({
  secret: process.env.NEXTAUTH_SECRET,
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        response: {},
      },
      async authorize(credentials) {
        const user = JSON.parse(credentials?.response || "{}");
        if (user.token) return user;
        throw new Error("user token not found");
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      return { ...token, ...user };
    },

    async session({ session, token, user }) {
      session.user = token as any;
      return session;
    },
  },
  pages: { signIn: "/", signOut: "/", error: "/" },
});

export { handler as GET, handler as POST };
