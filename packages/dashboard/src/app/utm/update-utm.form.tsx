import FormField from "@web/components/form/FormField";
import { But<PERSON> } from "@web/components/ui/button";
import { FormProvider, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { z } from "zod";
import { trpc } from "@web/providers/Providers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const schema = z.object({
  utmId: z.string().min(1, "UTM ID is required"),
  name: z.string().min(1, "Name is required"),
  utm_source: z.string().optional(),
  utm_medium: z.string().optional(),
  utm_campaign: z.string().optional(),
  utm_term: z.string().optional(),
  utm_content: z.string().optional(),
});

type Form = z.infer<typeof schema>;

interface UpdateUtmFormProps {
  utmId: string;
}

export default function UpdateUtmForm({ utmId }: UpdateUtmFormProps) {
  const router = useRouter();
  const updateUtm = trpc.utm.updateUtm.useMutation();
  const { data: utm, isLoading } = trpc.utm.getUtmById.useQuery(utmId);

  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      utmId: utmId,
      name: "",
      utm_campaign: "",
      utm_content: "",
      utm_medium: "",
      utm_source: "",
      utm_term: "",
    },
  });

  useEffect(() => {
    if (utm) {
      methods.reset({
        utmId: utmId,
        name: utm.name,
        utm_campaign: utm.utm_campaign || "",
        utm_content: utm.utm_content || "",
        utm_medium: utm.utm_medium || "",
        utm_source: utm.utm_source || "",
        utm_term: utm.utm_term || "",
      });
    }
  }, [utm, methods, utmId]);

  const onSubmit = methods.handleSubmit((data) => {
    updateUtm.mutate(data, {
      onSuccess: () => {
        toast.success("UTM updated successfully");
        router.push("/utm");
      },
      onError: (error) => {
        toast.error(error.message || "Failed to update UTM");
      },
    });
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <form className="space-y-4" onSubmit={onSubmit}>
      <FormProvider {...methods}>
        <FormField
          name="name"
          placeholder="Enter UTM name"
          label="Name"
          required
        />
        <FormField
          name="utm_campaign"
          placeholder="Enter campaign"
          label="Campaign"
        />
        <FormField
          name="utm_content"
          placeholder="Enter content"
          label="Content"
        />
        <FormField
          name="utm_medium"
          placeholder="Enter medium"
          label="Medium"
        />
        <FormField
          name="utm_source"
          placeholder="Enter source"
          label="Source"
        />
        <FormField name="utm_term" placeholder="Enter term" label="Term" />
        <Button type="submit" disabled={updateUtm.isLoading}>
          {updateUtm.isLoading ? "Updating..." : "Update UTM"}
        </Button>
      </FormProvider>
    </form>
  );
}
