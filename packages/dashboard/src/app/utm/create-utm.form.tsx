import FormField from "@web/components/form/FormField";
import { <PERSON><PERSON> } from "@web/components/ui/button";
import { FormProvider, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { z } from "zod";
import { trpc } from "@web/providers/Providers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";

const schema = z.object({
  name: z.string().min(1, "Name is required"),
  utm_source: z.string().optional(),
  utm_medium: z.string().optional(),
  utm_campaign: z.string().optional(),
  utm_term: z.string().optional(),
  utm_content: z.string().optional(),
});

type Form = z.infer<typeof schema>;

export default function CreateUtmForm() {
  const router = useRouter();
  const createUtm = trpc.utm.createUtm.useMutation();

  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: "",
      utm_campaign: "",
      utm_content: "",
      utm_medium: "",
      utm_source: "",
      utm_term: "",
    },
  });

  const onSubmit = methods.handleSubmit((data) => {
    createUtm.mutate(data, {
      onSuccess: () => {
        toast.success("UTM created successfully");
        methods.reset();
        router.push("/utm");
      },
      onError: (error) => {
        toast.error(error.message || "Failed to create UTM");
      },
    });
  });

  return (
    <form className="space-y-4" onSubmit={onSubmit}>
      <FormProvider {...methods}>
        <FormField
          name="name"
          placeholder="Enter UTM name"
          label="Name"
          required
        />
        <FormField
          name="utm_campaign"
          placeholder="Enter campaign"
          label="Campaign"
        />
        <FormField
          name="utm_content"
          placeholder="Enter content"
          label="Content"
        />
        <FormField
          name="utm_medium"
          placeholder="Enter medium"
          label="Medium"
        />
        <FormField
          name="utm_source"
          placeholder="Enter source"
          label="Source"
        />
        <FormField name="utm_term" placeholder="Enter term" label="Term" />
        <Button type="submit" disabled={createUtm.isLoading}>
          {createUtm.isLoading ? "Creating..." : "Create UTM"}
        </Button>
      </FormProvider>
    </form>
  );
}
