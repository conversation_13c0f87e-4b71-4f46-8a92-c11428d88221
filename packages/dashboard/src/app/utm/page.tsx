"use client";

import { <PERSON><PERSON> } from "@web/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import { trpc } from "@web/providers/Providers";
import { format } from "date-fns";
import { <PERSON><PERSON>, Edit, Printer, Trash } from "lucide-react";
import { toastPromise } from "@web/lib/utils";
import Link from "next/link";
import toast from "react-hot-toast";
import QRCode from "qrcode";

export default function UtmScreen() {
  const utms = trpc.utm.getUtm.useQuery();
  const deleteUtm = trpc.utm.deleteUtm.useMutation();

  const handleDeleteUtm = async (utmId: string) => {
    toastPromise({
      asyncFunc: deleteUtm.mutateAsync(utmId),
      success: "UTM deleted successfully",
      onSuccess() {
        utms.refetch();
      },
    });
  };

  const generateUtmUrl = (utm: any) => {
    const baseUrl = `${window.location.origin}/download`;
    const params = new URLSearchParams();

    // Add the UTM ID for analytics tracking
    if (utm._id) params.append("utm_id", utm._id.toString());

    // Add standard UTM parameters
    if (utm.utm_source) params.append("utm_source", utm.utm_source);
    if (utm.utm_medium) params.append("utm_medium", utm.utm_medium);
    if (utm.utm_campaign) params.append("utm_campaign", utm.utm_campaign);
    if (utm.utm_term) params.append("utm_term", utm.utm_term);
    if (utm.utm_content) params.append("utm_content", utm.utm_content);

    return params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl;
  };

  const handleCopyLink = (utm: any) => {
    const utmUrl = generateUtmUrl(utm);

    navigator.clipboard
      .writeText(utmUrl)
      .then(() => toast.success("Link copied to clipboard"))
      .catch(() => toast.error("Failed to copy link"));
  };

  const handlePrintQRCode = async (utm: any) => {
    try {
      // Generate URL with UTM parameters and UTM ID for analytics
      const utmUrl = generateUtmUrl(utm);

      // Generate QR code as data URL
      const qrCodeDataUrl = await QRCode.toDataURL(utmUrl, {
        width: 300,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#ffffff",
        },
      });

      // Create a printable HTML document with the QR code and UTM details
      const printContent = `<html>
<head>
  <title>UTM QR Code - ${utm.name}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
    .container { max-width: 600px; margin: 0 auto; text-align: center; }
    .qr-code { margin: 20px 0; }
    .qr-code img { max-width: 300px; }
    .utm-details { margin-top: 30px; text-align: left; }
    .utm-details table { width: 100%; border-collapse: collapse; }
    .utm-details th, .utm-details td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
    .utm-details th { font-weight: bold; }
    .utm-url { margin-top: 20px; word-break: break-all; font-size: 14px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>${utm.name}</h1>
    <div class="qr-code">
      <img src="${qrCodeDataUrl}" alt="QR Code" />
    </div>
    <div class="utm-url">
      <strong>URL:</strong> ${utmUrl}
    </div>
    <div class="utm-details">
      <table>
        <tr><th>Parameter</th><th>Value</th></tr>
        ${
          utm.utm_source
            ? `<tr><td>Source</td><td>${utm.utm_source}</td></tr>`
            : ""
        }
        ${
          utm.utm_medium
            ? `<tr><td>Medium</td><td>${utm.utm_medium}</td></tr>`
            : ""
        }
        ${
          utm.utm_campaign
            ? `<tr><td>Campaign</td><td>${utm.utm_campaign}</td></tr>`
            : ""
        }
        ${utm.utm_term ? `<tr><td>Term</td><td>${utm.utm_term}</td></tr>` : ""}
        ${
          utm.utm_content
            ? `<tr><td>Content</td><td>${utm.utm_content}</td></tr>`
            : ""
        }
      </table>
    </div>
  </div>
  <script>
    // Ensure image is loaded before printing
    window.onload = function() {
      setTimeout(function() {
        window.print();
      }, 500);
    };
  </script>
</body>
</html>`;

      // Create a new window for printing
      const printWindow = window.open("", "_blank");
      if (!printWindow) {
        toast.error(
          "Unable to open print window. Please check your popup blocker settings."
        );
        return;
      }

      // Write content to the new window
      printWindow.document.write(printContent);
      printWindow.document.close();
    } catch (error) {
      console.error("Error generating QR code:", error);
      toast.error("Failed to generate QR code");
    }
  };

  if (utms.isLoading) return <div>Loading...</div>;

  return (
    <div className="p-4">
      <div className="flex justify-end mb-4">
        <Button asChild>
          <Link href="/utm/create">Create UTM</Link>
        </Button>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Campaign</TableHead>
            <TableHead>Source</TableHead>
            <TableHead>Medium</TableHead>
            <TableHead>Content</TableHead>
            <TableHead>Term</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {utms.data?.map((utm) => (
            <TableRow key={String(utm._id)}>
              <TableCell>{utm.name}</TableCell>
              <TableCell>{utm.utm_campaign || "-"}</TableCell>
              <TableCell>{utm.utm_source || "-"}</TableCell>
              <TableCell>{utm.utm_medium || "-"}</TableCell>
              <TableCell>{utm.utm_content || "-"}</TableCell>
              <TableCell>{utm.utm_term || "-"}</TableCell>
              <TableCell>
                {utm.createdAt && format(new Date(utm.createdAt), "PPP")}
              </TableCell>
              <TableCell className="flex items-center gap-2">
                <Button size="sm" asChild>
                  <Link href={`/utm/edit/${utm._id}`}>
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Link>
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  disabled
                  onClick={() => handleDeleteUtm(String(utm._id))}
                >
                  <Trash className="h-4 w-4" />
                </Button>
                <Button size="icon" onClick={() => handlePrintQRCode(utm)}>
                  <Printer className="size-4" />
                </Button>
                <Button size="icon" onClick={() => handleCopyLink(utm)}>
                  <Copy className="size-4" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
