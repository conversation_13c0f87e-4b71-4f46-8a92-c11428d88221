"use client";
import { AlertDialogCancel } from "@radix-ui/react-alert-dialog";
import PreviewPost from "@web/components/posts/preview-post";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@web/components/ui/alert-dialog";
import { But<PERSON> } from "@web/components/ui/button";
import { ScrollArea } from "@web/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@web/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import { toastPromise } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";
import { format } from "date-fns";
import { ReportPostStatus } from "../../../../shared/types/Connect";
import { useState } from "react";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>Des<PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@web/components/ui/sheet";
import { History } from "lucide-react";

export default function Reports() {
  const [filter, setFilter] = useState<ReportPostStatus>(
    ReportPostStatus.PENDING
  );

  const reports = trpc.connect.getReportedPosts.useQuery({ status: filter });
  const [open, setOpen] = useState<string>();
  const [selectedPost, setSelectedPost] = useState<string>();

  const { data: posts } = trpc.connect.getPosts.useInfiniteQuery(
    { postId: selectedPost as string, limit: 1 },
    {
      initialCursor: 0,
      enabled: !!selectedPost,
    }
  );

  const flatPost = posts?.pages?.at(0)?.posts?.at(0);

  const {
    data: comments,
    isLoading: isCommentsLoading,
    refetch: refetchComments,
  } = trpc.connect.getComments.useQuery(
    { post: selectedPost as string },
    { enabled: !!selectedPost }
  );

  const manageReportDocument = trpc.connect.manageReportPost.useMutation();

  const handleReportAction = (v: string, reportId: string) => {
    return toastPromise({
      asyncFunc: manageReportDocument.mutateAsync({
        reportId,
        status: v as ReportPostStatus,
      }),
      async onSuccess() {
        reports.refetch();
      },
    });
  };

  return (
    <div className="p-6">
      <Select onValueChange={(data) => setFilter(data as ReportPostStatus)}>
        <SelectTrigger className="max-w-[150px]">
          <SelectValue placeholder="Select Filter" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={ReportPostStatus.PENDING}>Pending</SelectItem>
          <SelectItem value={ReportPostStatus.APPROVE}>Approved</SelectItem>
          <SelectItem value={ReportPostStatus.REJECT}>Rejected</SelectItem>
        </SelectContent>
      </Select>
      <Table className="mt-4">
        <TableHeader className="font-medium">
          <TableRow>
            <TableCell>Type</TableCell>
            <TableCell>Author</TableCell>
            <TableCell>Author Email</TableCell>
            <TableCell>Author Phone</TableCell>
            <TableCell>Reporter</TableCell>
            <TableCell>Reporter email</TableCell>
            <TableCell>Reporter phone</TableCell>
            <TableCell>Reported At</TableCell>
            <TableCell>Post</TableCell>
            <TableCell>Comment</TableCell>
            <TableCell>Actions</TableCell>
            <TableCell>History</TableCell>
          </TableRow>
        </TableHeader>
        <TableBody>
          {reports.data?.map((report) => {
            const { comment, post, type, _id, reportedBy, status } = report;
            return (
              <TableRow key={String(_id)}>
                <TableCell className="capitalize">{type}</TableCell>
                <TableCell>{post?.user.username}</TableCell>
                <TableCell>{post?.user.email || "-"}</TableCell>
                <TableCell>{post?.user?.contact?.phone || "-"}</TableCell>
                <TableCell className="capitalize">
                  {reportedBy?.username || "-"}
                </TableCell>
                <TableCell className="capitalize">
                  {reportedBy?.email || "-"}
                </TableCell>
                <TableCell className="capitalize">
                  {reportedBy?.contact?.phone || "-"}
                </TableCell>
                {/* @ts-ignore */}
                <TableCell>{format(report.createdAt as Date, "PPP")}</TableCell>
                <TableCell className="capitalize">{post?.title}</TableCell>
                <TableCell className="capitalize">{comment?.comment}</TableCell>
                <TableCell className="flex items-center gap-2">
                  {/* Dialog */}
                  <PreviewPost
                    post={flatPost}
                    comments={comments}
                    alertProps={{
                      open: open === _id,
                      onOpenChange: (v) => {
                        if (v) {
                          setSelectedPost(String(post?._id ?? comment.post));
                        }
                        setOpen(v ? String(_id) : undefined);
                      },
                    }}
                  />
                  <Select
                    onValueChange={(v) => handleReportAction(v, String(_id))}
                    defaultValue={status}
                    disabled={status !== "pending"}
                  >
                    <SelectTrigger className="max-w-[200px]">
                      <SelectValue placeholder="Select Action" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ReportPostStatus.APPROVE}>
                        APPROVE
                      </SelectItem>
                      <SelectItem value={ReportPostStatus.REJECT}>
                        REJECT
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </TableCell>
                <TableCell>{status}</TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
