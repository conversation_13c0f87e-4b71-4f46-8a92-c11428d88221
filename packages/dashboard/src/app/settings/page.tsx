"use client";
import { <PERSON><PERSON><PERSON>ckBox } from "@web/components/form/FormCheckBox";
import <PERSON><PERSON>ield from "@web/components/form/FormField";
import { <PERSON><PERSON> } from "@web/components/ui/button";
import { toastPromise } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { appUpdateValidator } from "../../../../shared/validators/app-update.validator";

type Form = z.infer<typeof appUpdateValidator>;
export default function Settings() {
  const methods = useForm<Form>({
    defaultValues: {
      android: { v: "", storeUrl: "", isOptionalUpdate: false },
      ios: { v: "", storeUrl: "", isOptionalUpdate: false },
    },
  });

  trpc.appUpdates.appver.useQuery(undefined, {
    onSuccess(data) {
      // @ts-expect-error
      Object.entries(data).forEach(([key, value]) => {
        // @ts-expect-error
        methods.setValue(key, value);
      });
    },
  });

  const updateAppVer = trpc.appUpdates.setAppver.useMutation();

  return (
    <div className="flex p-4 gap-10">
      {/* Developer */}
      <div className="max-w-[500px] gap-4 flex flex-1 flex-col">
        <p>Developer only</p>

        <FormProvider {...methods}>
          <FormField
            name="android.v"
            label="Android app.ver**"
            placeholder="Android app.ver-ex: 1.0.0"
          />
          <FormField
            name="android.storeUrl"
            label="Android Store Url"
            placeholder="Android store url"
          />
          <FormCheckBox
            name="android.isOptionalUpdate"
            label="Optional Update"
          />
          <div className="my-5 border-b" />
          <FormField
            name="ios.v"
            label="Ios app.ver"
            placeholder="Ios app.ver-ex: 1.0.0"
          />
          <FormField
            name="ios.storeUrl"
            label="Ios Store Url"
            placeholder="Ios store url"
          />
          <FormCheckBox name="ios.isOptionalUpdate" label="Optional Update" />

          <Button
            onClick={methods.handleSubmit((data) =>
              toastPromise({
                asyncFunc: updateAppVer.mutateAsync(data),
                success: "app.ver updated successfully",
              })
            )}
          >
            Update
          </Button>
        </FormProvider>
      </div>
    </div>
  );
}
