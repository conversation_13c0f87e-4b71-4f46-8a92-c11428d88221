"use client";

import { <PERSON><PERSON> } from "@web/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@web/components/ui/card";
import {
  loginValidator,
  requestLoginOtpValidator,
} from "../../../../shared/validators/user.validator";
import { z } from "zod";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import FormField from "@web/components/form/FormField";
import { Separator } from "@web/components/ui/separator";
import { trpc } from "@web/providers/Providers";
import { useState } from "react";
import { cn, toastPromise } from "@web/lib/utils";
import toast from "react-hot-toast";
import { signIn } from "next-auth/react";

type RequestOtpForm = z.infer<typeof requestLoginOtpValidator>;
type LoginForm = z.infer<typeof loginValidator>;

export default function Login() {
  const [showLoginForm, setShowLoginForm] = useState(false);

  const otpMethods = useForm<RequestOtpForm>({
    resolver: zodResolver(requestLoginOtpValidator),
  });

  const loginMethods = useForm<LoginForm>({
    resolver: zodResolver(loginValidator),
    defaultValues: { email: undefined, phone: undefined, otp: "" },
  });

  const requestOtp = trpc.auth.requestLoginOtp.useMutation();
  const login = trpc.auth.validateLoginOtp.useMutation();

  const handleRequestLogin = loginMethods.handleSubmit(async (data) => {
    const res = await login.mutateAsync(data);

    // if (res.role !== "SUPER_ADMIN") return toast.error("unauthorized");
    signIn("credentials", { response: JSON.stringify(res), redirect: false });
  });

  const handleRequestOtp = otpMethods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: requestOtp.mutateAsync(data, {
        onSuccess: () => {
          data.email &&
            loginMethods.setValue("email", data.email.toLowerCase());
          data.phone && loginMethods.setValue("phone", data.phone);
          setShowLoginForm(true);
        },
      }),
      success: "otp sent successfully",
    });
  });

  return (
    <div className="min-h-screen flex flex-col justify-center items-center">
      <Card className="w-[550px]">
        <CardHeader>
          <CardTitle>Login</CardTitle>
          <CardDescription>Admin only login page</CardDescription>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={handleRequestOtp}
            className={cn(showLoginForm && "hidden")}
          >
            <FormProvider {...otpMethods}>
              <div className="grid w-full items-center gap-4">
                <FormField
                  label="Email"
                  placeholder="Enter email"
                  name="email"
                  type="email"
                />
                <div className="relative mt-2">
                  <Separator />
                  <span className="absolute -top-3 bg-white right-[43%]  px-4">
                    OR
                  </span>
                </div>
                <FormField
                  label="Phone"
                  type="tel"
                  placeholder="Enter Phone"
                  name="phone"
                />
                <Button className="w-full">Request Otp</Button>
              </div>
            </FormProvider>
          </form>
          <form
            onSubmit={handleRequestLogin}
            className={cn(!showLoginForm && "hidden")}
          >
            <FormProvider {...loginMethods}>
              <div className="grid w-full items-center gap-4">
                {loginMethods.watch("email") && (
                  <FormField
                    label="Email"
                    placeholder="Enter email"
                    name="email"
                    type="email"
                  />
                )}
                {loginMethods.watch("phone") && (
                  <FormField
                    label="Phone"
                    type="tel"
                    placeholder="Enter Phone"
                    name="phone"
                  />
                )}
                <FormField
                  type="number"
                  name="otp"
                  label="Enter Otp"
                  placeholder="Enter your 4 digit OTP"
                />
                <Button className="w-full">Login</Button>
              </div>
            </FormProvider>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
