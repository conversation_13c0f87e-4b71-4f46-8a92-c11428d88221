import type { Metada<PERSON> } from 'next';
import { Montserrat } from 'next/font/google';
import Providers from '@web/providers/Providers';
import SideBar from '@web/components/common/sidebar';
import NavBar from '@web/components/common/navbar';
import { Toaster } from 'react-hot-toast';
import { getServerSession } from 'next-auth';
import SessionProvider from '../providers/SessionProvider';

import './globals.css';
import '@blocknote/react/style.css';
import { TooltipProvider } from '@web/components/ui/tooltip';

const inter = Montserrat({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Rabble Groups Admin',
  description: '',
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession();

  return (
    <html lang='en'>
      <body className={inter.className}>
        <SessionProvider session={session}>
          <TooltipProvider delayDuration={100}>
            <Toaster position='top-right' />
            <main className='flex flex-1 min-h-screen'>
              <SideBar />
              <div className='flex flex-col flex-1'>
                <NavBar />
                <Providers>{children}</Providers>
              </div>
            </main>
          </TooltipProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
