"use client";

import { <PERSON><PERSON> } from "@web/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import { Badge } from "@web/components/ui/badge";
import { trpc } from "@web/providers/Providers";
import { Edit, Plus, Image as ImageIcon, ExternalLink } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { toastPromise } from "@web/lib/utils";
import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@web/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@web/components/ui/select";
import S3FileUpload from "@web/components/form/S3FileUpload";
import { FormProvider, useForm } from "react-hook-form";

export default function Manage() {
  const [isLogoDialogOpen, setIsLogoDialogOpen] = useState(false);
  const [currentTopic, setCurrentTopic] = useState<any>(null);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get status from URL query params or default to "Published"
  const statusParam = searchParams.get("status") || "Published";

  // Ensure status is a valid value
  const status = ["All", "Published", "Draft"].includes(statusParam)
    ? statusParam
    : "Published";

  const {
    data: topics,
    isLoading,
    refetch,
  } = trpc.manage.getTopics.useQuery({ status: status as any });
  const updateTopic = trpc.manage.updateTopic.useMutation();

  // Handle status filter change
  const handleStatusChange = (newStatus: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("status", newStatus);
    router.push(`/manage?${params.toString()}`);
  };

  const handleUpdateLogo = (topic: any) => {
    setCurrentTopic(topic);
    setIsLogoDialogOpen(true);
  };

  const logoForm = useForm({
    defaultValues: {
      logo: "",
    },
  });

  return (
    <div className="container p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Manage Topics</h1>
        <div className="flex space-x-2">
          <div className="flex items-center mr-4">
            <span className="mr-2 text-sm font-medium">Status:</span>
            <Select value={status} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All</SelectItem>
                <SelectItem value="Published">Published</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button variant="outline" asChild>
            <Link href="/manage/categories">Topic Categories</Link>
          </Button>
          <Button variant="outline" asChild>
            <a
              href="https://docs.google.com/spreadsheets/d/1Fk3n8UNKwm2FSgcj-F6gmkaAcYMi2DOykzrEaWceDws/edit?usp=sharing"
              target="_blank"
              rel="noopener noreferrer"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Excel Template
            </a>
          </Button>
          <Button asChild>
            <Link href="/manage/create">
              <Plus className="h-4 w-4 mr-2" />
              Create Topic
            </Link>
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">Loading topics...</div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[60px]">Logo</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Tags</TableHead>
              <TableHead className="w-[100px]">Status</TableHead>
              <TableHead className="w-[140px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {topics?.map((topic) => (
              <TableRow key={String(topic._id)}>
                <TableCell>
                  {topic.logo ? (
                    <img
                      src={topic.logo}
                      alt={topic.name}
                      className="w-10 h-10 object-contain rounded"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-muted rounded flex items-center justify-center text-xs text-muted-foreground">
                      No logo
                    </div>
                  )}
                </TableCell>
                <TableCell className="font-medium">{topic.name}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {Array.isArray(topic.category) &&
                    topic.category.length > 0 ? (
                      topic.category.map((cat: any, index) => (
                        <span
                          key={index}
                          className="bg-muted px-2 py-1 rounded-md text-xs"
                        >
                          {cat.name || cat}
                        </span>
                      ))
                    ) : (
                      <span className="text-muted-foreground text-xs">
                        No categories
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {topic.tags && topic.tags.length > 0 ? (
                      topic.tags.map((tag: any, index) => (
                        <span
                          key={index}
                          className="bg-muted px-2 py-1 rounded-md text-xs"
                        >
                          {tag.tag || tag}
                        </span>
                      ))
                    ) : (
                      <span className="text-muted-foreground text-xs">
                        No tags
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={!topic.isDeleted ? "default" : "secondary"}
                    className="rounded-full"
                  >
                    {!topic.isDeleted ? "Published" : "Draft"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button size="sm" asChild>
                      <Link href={`/manage/create?id=${topic._id}`}>
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Link>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleUpdateLogo(topic)}
                    >
                      <ImageIcon className="h-4 w-4 mr-1" />
                      Update Logo
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {topics?.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  No topics found. Create your first topic!
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}

      {/* Logo Update Dialog */}
      <Dialog open={isLogoDialogOpen} onOpenChange={setIsLogoDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Topic Logo</DialogTitle>
            <DialogDescription>
              Select a new logo for {currentTopic?.name}
            </DialogDescription>
          </DialogHeader>
          <FormProvider {...logoForm}>
            <form
              onSubmit={logoForm.handleSubmit(async (data) => {
                try {
                  await toastPromise({
                    asyncFunc: updateTopic.mutateAsync({
                      _id: currentTopic._id,
                      logo: data.logo,
                    }),
                    success: "Logo updated successfully",
                    error: "Failed to update logo",
                    onSuccess: () => {
                      setIsLogoDialogOpen(false);
                      refetch();
                    },
                  });
                } catch (error) {
                  console.error("Error updating logo:", error);
                }
              })}
              className="space-y-4 py-4"
            >
              <S3FileUpload name="logo" path="services" prefix="topic" />
              <div className="flex justify-end">
                <Button type="submit">Update Logo</Button>
              </div>
            </form>
          </FormProvider>
        </DialogContent>
      </Dialog>
    </div>
  );
}
