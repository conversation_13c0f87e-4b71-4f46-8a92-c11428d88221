"use client";

import { <PERSON><PERSON> } from "@web/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import { trpc } from "@web/providers/Providers";
import { Edit, Plus, Trash, X } from "lucide-react";
import { useState } from "react";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@web/components/ui/alert-dialog";
import { Label } from "@web/components/ui/label";
import { Input } from "@web/components/ui/input";
import { toastPromise } from "@web/lib/utils";
import S3FileUpload from "@web/components/form/S3FileUpload";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

// Define the Zod schema for category form validation
const categorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  logo: z.string().min(1, "Logo is required"),
});

export default function TopicCategories() {
  const {
    data: categories,
    isLoading,
    refetch,
  } = trpc.manage.getTopicCategories.useQuery();
  const createCategory = trpc.manage.createTopicCategory.useMutation();
  const updateCategory = trpc.manage.updateTopicCategory.useMutation();
  const deleteCategory = trpc.manage.deleteTopicCategory.useMutation();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentCategory, setCurrentCategory] = useState<any>(null);

  // Create a form using react-hook-form with Zod validation
  const methods = useForm<z.infer<typeof categorySchema>>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      logo: "",
    },
  });

  const handleOpenDialog = (category?: any) => {
    if (category) {
      setCurrentCategory(category);
      methods.reset({
        name: category.name,
        logo: category.logo,
      });
    } else {
      setCurrentCategory(null);
      methods.reset({
        name: "",
        logo: "",
      });
    }
    setIsDialogOpen(true);
  };

  const handleOpenDeleteDialog = (category: any) => {
    setCurrentCategory(category);
    setIsDeleteDialogOpen(true);
  };

  const handleSubmit = methods.handleSubmit(async (data) => {
    try {
      if (currentCategory) {
        // Update existing category
        await toastPromise({
          asyncFunc: updateCategory.mutateAsync({
            _id: currentCategory._id,
            ...data,
          }),
          success: "Category updated successfully",
          error: "Failed to update category",
        });
      } else {
        // Create new category
        await toastPromise({
          asyncFunc: createCategory.mutateAsync(data),
          success: "Category created successfully",
          error: "Failed to create category",
        });
      }
      setIsDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Error saving category:", error);
    }
  });

  const handleDelete = async () => {
    try {
      await toastPromise({
        asyncFunc: deleteCategory.mutateAsync(currentCategory._id),
        success: "Category deleted successfully",
        error: "Failed to delete category",
      });
      setIsDeleteDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Error deleting category:", error);
    }
  };

  return (
    <div className="container p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Topic Categories</h1>
        <Button onClick={() => handleOpenDialog()}>
          <Plus className="h-4 w-4 mr-2" />
          Create Category
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">Loading categories...</div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead className="w-[150px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categories?.map((category) => (
              <TableRow key={String(category._id)}>
                <TableCell className="font-medium flex items-center gap-4">
                  {category.logo ? (
                    <div className="w-10 h-10">
                      <img
                        src={category.logo}
                        alt={category.name}
                        className="object-contain w-full h-full"
                      />
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-xs">
                      No logo
                    </span>
                  )}
                  {category.name}
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOpenDialog(category)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleOpenDeleteDialog(category)}
                    >
                      <Trash className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {!categories?.length && (
              <TableRow>
                <TableCell colSpan={3} className="text-center py-8">
                  No categories found. Create your first category!
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}

      {/* Create/Edit Category Dialog */}
      <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <AlertDialogContent className="">
          {/* Close button */}
          <button
            onClick={() => setIsDialogOpen(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
            aria-label="Close"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>

          <AlertDialogHeader>
            <AlertDialogTitle>
              {currentCategory ? "Edit Category" : "Create Category"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {currentCategory
                ? "Update the category details below."
                : "Fill in the details to create a new category."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <FormProvider {...methods}>
            <form onSubmit={handleSubmit} className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Category Name</Label>
                <Input
                  id="name"
                  placeholder="Enter category name"
                  {...methods.register("name")}
                  className={
                    methods.formState.errors.name ? "border-red-500" : ""
                  }
                />
                {methods.formState.errors.name && (
                  <p className="text-red-500 text-sm mt-1">
                    {methods.formState.errors.name.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="logo">Category Logo</Label>
                <S3FileUpload
                  name="logo"
                  path="topic-categories"
                  prefix="category"
                />
                {methods.formState.errors.logo && (
                  <p className="text-red-500 text-sm mt-1">
                    {methods.formState.errors.logo.message}
                  </p>
                )}
              </div>
              <AlertDialogFooter className="pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {currentCategory ? "Update Category" : "Create Category"}
                </Button>
              </AlertDialogFooter>
            </form>
          </FormProvider>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent className="">
          {/* Close button */}
          <button
            onClick={() => setIsDeleteDialogOpen(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
            aria-label="Close"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>

          <AlertDialogHeader>
            <AlertDialogTitle>Delete Category</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the category "
              {currentCategory?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Delete Category
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
