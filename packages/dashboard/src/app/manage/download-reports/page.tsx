"use client";
import { trpc } from "@web/providers/Providers";
import { endOfMonth, endOfWeek, startOfMonth, subMonths } from "date-fns";
import _ from "lodash";
import { useEffect, useMemo, useState } from "react";

export default function DownloadReports() {
  const [bottomSheet, setBottomSheet] = useState(false);
  const [filter, setFilter] = useState("All Topics");
  const [selectedFilter, setSelectedFilter] = useState("All Topics");
  const [events, setEvents] = useState({});
  const [startDate, setStartDate] = useState(
    subMonths(startOfMonth(new Date()), 12)
  );
  const [endDate, setEndDate] = useState(endOfWeek(endOfMonth(new Date())));

  const userTopics = trpc.manage.getUserTopics.useQuery(
    {},
    {
      enabled: false,
    }
  );

  const subscribedTopics = useMemo(
    () => [
      { id: null, name: "All Topics" },
      ...(userTopics.data?.map((userTopic) => ({
        _id: userTopic.topic._id,
        name: userTopic.topic.name,
      })) || []),
    ],
    [userTopics.data]
  );

  const selectTopicId = useMemo(() => {
    const topicId = _.get(
      _.find(subscribedTopics, (topic) => topic.name === selectedFilter),
      "_id"
    );
    return topicId;
  }, [filter]);

  const getCalenderEvents = trpc.manage.getCalenderEvents.useMutation();

  useEffect(() => {
    (async () => {
      const data = await getCalenderEvents.mutateAsync({
        startDate,
        endDate,
        topicId: selectTopicId as string,
      });
      setEvents(data?.events);
    })();
  }, [filter]);

  return <div className="container p-4 border"></div>;
}
