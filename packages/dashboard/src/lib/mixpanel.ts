import axios from "axios";
import { createTRPCProxyClient, httpBatchLink } from "@trpc/client";
import { AppRouter } from "../../../backend/app";
import Super<PERSON><PERSON><PERSON> from "superjson";
import { getSession } from "next-auth/react";

export const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN!;
export const MIXPANEL_TOKEN_V2 = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN_V2!;

// Create a raw TRPC client for direct calls
export const rawTrpcClient = createTRPCProxyClient<AppRouter>({
  transformer: SuperJSON,
  links: [
    httpBatchLink({
      url: process.env.NEXT_PUBLIC_TRPC_URL!,
      headers: async () => {
        const session = await getSession();
        return {
          "x-auth-token": session?.user?.token,
        };
      },
    }),
  ],
});

type Action = Record<string, string>;

interface MixpanelEventTypes {
  [key: string]: Action;
}

class MixpanelTracker {
  private token: string;
  private baseUrl: string = "https://api.mixpanel.com/track";

  constructor(token: string) {
    this.token = token;
  }

  async trackEvent<T extends keyof MixpanelEventTypes>(
    event: T | string,
    properties: MixpanelEventTypes[T],
    distinct_id?: string | number,
    version?: "v1" | "v2"
  ): Promise<any> {
    const data = {
      event,
      token: this.token,
      ...(distinct_id && { distinct_id }),
      ...properties,
    };

    const dataV2 = {
      event,
      properties: {
        token: MIXPANEL_TOKEN_V2,
        ...(distinct_id && { distinct_id }),
        ...properties,
      },
    };

    // Create a unique key based on event name, user ID, and timestamp
    const timestamp = new Date().toISOString();
    const analyticsKey = `${event}_${distinct_id || "anonymous"}_${timestamp}`;

    // Create a promise for saving analytics data to database using TRPC
    const analyticsPromise = rawTrpcClient.analytics.createAnalytics
      .mutate({
        key: analyticsKey,
        value: {
          event,
          properties: {
            ...properties,
            distinct_id: distinct_id || "anonymous",
            timestamp,
            version: version || "v1",
          },
        },
      })
      .catch((error) => {
        console.error("Failed to save analytics to database:", error);
        // Return null to avoid disrupting the Promise.all
        return null;
      });

    if (version === "v2") {
      // Run all requests in parallel
      return Promise.all([
        analyticsPromise,
        axios.post(this.baseUrl, [dataV2], {
          headers: { accept: "text/plain", "Content-Type": "application/json" },
          params: { verbose: "2" },
        }),
        axios.post(this.baseUrl, [data], {
          headers: { accept: "text/plain", "Content-Type": "application/json" },
          params: { verbose: "2" },
        }),
      ]);
    }

    // Run both requests in parallel
    return Promise.all([
      analyticsPromise,
      axios.post(this.baseUrl, [data], {
        headers: { accept: "text/plain", "Content-Type": "application/json" },
        params: { verbose: "2" },
      }),
    ]);
  }
}

export default new MixpanelTracker(MIXPANEL_TOKEN!);
