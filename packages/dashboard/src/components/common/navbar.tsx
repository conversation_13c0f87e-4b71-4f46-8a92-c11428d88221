'use client';
import { Avatar, AvatarFallback } from '@web/components/ui/avatar';
import { nameToInitials } from '@web/lib/utils';
import { Button } from '../ui/button';
import { signOut, useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';

export default function NavBar() {
  const { data: session } = useSession();

  if (!session) return null;

  return (
    <nav className="h-[75px] w-full relative z-10">
      <div className="h-[75px] flex flex-row items-center justify-end p-4 border-b fixed left-0 right-0 bg-white gap-4">
        <Avatar>
          {/* <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" /> */}
          <AvatarFallback className="uppercase">
            {nameToInitials('Rafeeq Shaik')}
          </AvatarFallback>
        </Avatar>

        <Button variant="outline" onClick={() => signOut()}>
          Logout
        </Button>
      </div>
    </nav>
  );
}
