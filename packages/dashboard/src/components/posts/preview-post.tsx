import { Eye } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog';
import { Button } from '../ui/button';
import { ScrollArea } from '../ui/scroll-area';
import { AlertDialogProps } from '@radix-ui/react-alert-dialog';
import { RouterOutput } from '../../../../shared';
import Image from 'next/image';
import { imgUrl } from '@web/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';

type Props = {
  alertProps: AlertDialogProps;
  post?: RouterOutput['connect']['getPosts']['posts'][number];
  comments?: RouterOutput['connect']['getComments'];
};

export default function PreviewPost({ alertProps, post, comments }: Props) {
  return (
    <AlertDialog {...alertProps}>
      <AlertDialogTrigger asChild>
        <Button>
          <Eye className='w-4 h-4 mr-2' /> Preview
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className='min-w-[800px]'>
        <AlertDialogHeader>
          <AlertDialogTitle>Post Preview</AlertDialogTitle>
        </AlertDialogHeader>
        <ScrollArea className='h-[60vh]'>
          <h2>{post?.title}</h2>
          {post && post.image && (
            <Image
              width={800}
              height={500}
              alt='post'
              src={imgUrl(post.image)}
            />
          )}

          {/* Comments */}
          <div>
            <h2>Comments</h2>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Username</TableHead>
                  <TableHead>Comment</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {comments?.map((comment) => {
                  const userImage = comment?.user?.profilePicture;
                  return (
                    <TableRow key={String(comment._id)}>
                      <TableCell>{comment?.user?.username}</TableCell>
                      <TableCell>{comment.comment}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </ScrollArea>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction>Continue</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
