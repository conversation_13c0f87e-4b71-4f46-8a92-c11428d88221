import { useController } from "react-hook-form";
import { Label } from "../ui/label";
import { z } from "zod";
import { Form } from "../../../../@types/Form";
import ChipInput from "../ui/chip-input";

export const tagsSchema = z.array(
  z.object({
    id: z.string(),
    text: z.string(),
  })
);

export default function FormChipInput({
  name,
  label,
  placeholder,
}: Form & {
  name: string;
  label: string;
  placeholder: string;
}) {
  const { field } = useController({ name });
  return (
    <div>
      {label && <Label htmlFor={name}>{label}</Label>}
      <ChipInput
        tags={field.value}
        setTags={field.onChange}
        placeholder={placeholder}
      />
      {/* <FormError name={name} /> */}
    </div>
  );
}
