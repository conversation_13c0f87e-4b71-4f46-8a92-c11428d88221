"use client";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useFormContext } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField as FF,
  FormItem,
  FormLabel,
  FormMessage,
} from "@web/components/ui/form";
import { Input } from "@web/components/ui/input";
import { Form } from "../../../../@types/Form";

type Props = Form & React.InputHTMLAttributes<HTMLInputElement>;

export default function FormField({
  name,
  label,
  description,
  ...otherProps
}: Props) {
  const methods = useFormContext();

  return (
    <FF
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Input {...field} {...otherProps} />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
