"use client";

import {
  format,
  getHours,
  getMinutes,
  isValid,
  setHours,
  setMinutes,
} from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import { cn, getDayHoursMins } from "@web/lib/utils";
import { Button } from "@web/components/ui/button";
import { Calendar } from "@web/components/ui/calendar";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@web/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@web/components/ui/popover";
import { Form } from "../../../../@types/Form";
import { ScrollArea } from "../ui/scroll-area";

type Props = Form & {
  disabled?: (date: Date) => boolean;
  showTimePicker?: boolean;
};

export function FormDatePicker({
  name,
  description,
  label,
  disabled,
  showTimePicker,
}: Props) {
  const methods = useFormContext();
  const { hours, mins, timeOfDay } = getDayHoursMins();

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          {label && <FormLabel>{label}</FormLabel>}
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant={"outline"}
                  className={cn(
                    "w-[300px] pl-3 text-left font-normal",
                    !field.value && "text-muted-foreground"
                  )}
                >
                  {field.value ? (
                    format(field.value, `PPP${showTimePicker ? " h:mm a" : ""}`)
                  ) : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 flex flex-row" align="start">
              <Calendar
                mode="single"
                selected={field.value}
                onSelect={field.onChange}
                disabled={disabled}
                initialFocus
              />
              {showTimePicker && (
                <div className="border-l rdp p-3 flex flex-row w-52">
                  {/* Hours */}
                  <ScrollArea className="flex flex-1 border-r flex-col h-[350px] overflow-y-auto">
                    <div className="h-4 text-center font-semibold">Hrs</div>
                    {hours.map((hr) => (
                      <Button
                        onClick={() =>
                          isValid(field.value) &&
                          field.onChange(setHours(field.value, hr))
                        }
                        variant={
                          isValid(field.value)
                            ? (getHours(field.value) > 12
                                ? getHours(field.value) - 12
                                : getHours(field.value)) === hr
                              ? "default"
                              : "ghost"
                            : "ghost"
                        }
                        key={hr}
                      >
                        {hr}
                      </Button>
                    ))}
                  </ScrollArea>
                  {/* Mins */}
                  <div className="flex flex-1 border-r">
                    <ScrollArea className="flex flex-1 border-r flex-col h-[350px] overflow-y-auto">
                      <div className="h-4 text-center font-semibold">Mins</div>
                      {mins.map((min) => (
                        <Button
                          onClick={() =>
                            isValid(field.value) &&
                            field.onChange(setMinutes(field.value, min))
                          }
                          variant={
                            isValid(field.value)
                              ? getMinutes(field.value) === min
                                ? "default"
                                : "ghost"
                              : "ghost"
                          }
                          key={min}
                        >
                          {min}
                        </Button>
                      ))}
                    </ScrollArea>
                  </div>

                  {/* AM/Pm */}
                  <div className="flex-1 flex-col">
                    <div className="h-4 text-center font-semibold">Hrs</div>
                    {timeOfDay.map((_) => (
                      <Button
                        onClick={() => {
                          if (_ === "PM" && isValid(field.value)) {
                            const hours = getHours(field.value);
                            if (hours <= 12)
                              field.onChange(setHours(field.value, hours + 12));
                          } else {
                            const hours = getHours(field.value);
                            if (hours > 12)
                              field.onChange(setHours(field.value, hours - 12));
                          }
                        }}
                        variant={
                          isValid(field.value)
                            ? getHours(field.value) <= 12 && _ === "AM"
                              ? "default"
                              : getHours(field.value) > 12 && _ === "PM"
                              ? "default"
                              : "ghost"
                            : "ghost"
                        }
                        key={_}
                      >
                        {_}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </PopoverContent>
          </Popover>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
