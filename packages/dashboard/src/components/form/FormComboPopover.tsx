"use client";

import { useFormContext } from "react-hook-form";
import { Checkbox } from "@web/components/ui/checkbox";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "@web/components/ui/form";
import { Form } from "../../../../@types/Form";
import { ComboBoxOption, ComboboxPopover } from "../ui/form-combo-popover";

type Props = Form & {
  options: ComboBoxOption | ComboBoxOption[];
  mode?: "multiple" | "single";
};

export function FormComboBoxPopover({
  name,
  placeholder,
  label,
  options,
  mode = "multiple",
}: Props) {
  const methods = useFormContext();

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <ComboboxPopover
          label={label || ""}
          options={options}
          mode={mode}
          value={field.value}
          onChange={field.onChange}
          placeholder={placeholder}
        />
      )}
    />
  );
}
