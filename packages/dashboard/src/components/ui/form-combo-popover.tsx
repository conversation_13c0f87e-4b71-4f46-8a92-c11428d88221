"use client";

import * as React from "react";

import { Button } from "@web/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@web/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@web/components/ui/popover";
import { Badge } from "../ui/badge";
import { Label } from "../ui/label";

export type ComboBoxOption = {
  value: string;
  label: string;
  icon?: JSX.Element; //TODO: work on icon
};

type Props = {
  label: string;
  mode?: "single" | "multiple";
  placeholder?: string;
  options: ComboBoxOption | ComboBoxOption[];
  value?: string | string[];
  onChange?: (data: string[]) => void;
  onValueSelect?: (data: string) => void;
};
export function ComboboxPopover({
  label,
  placeholder,
  options,
  value,
  mode,
  onChange,
  onValueSelect,
}: Props) {
  const [open, setOpen] = React.useState(false);

  const _options: ComboBoxOption[] = Array.isArray(options)
    ? options
    : [options];
  const _value: string[] = Array.isArray(value) ? value : value ? [value] : [];

  return (
    <div>
      <Label>{label}</Label>

      <div className="border rounded-lg p-4">
        {!!_value.length && (
          <div className="flex flex-wrap gap-1">
            {_value.map((value) => {
              return (
                <Badge
                  className="cursor-pointer"
                  key={value}
                  onClick={() => {
                    onValueSelect?.(value);
                    onChange?.(
                      _value.filter((item) => {
                        return item !== value;
                      })
                    );
                  }}
                >
                  {_options.find((_) => _.value === value)?.label}
                </Badge>
              );
            })}
          </div>
        )}
        <div>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start mt-4"
              >
                + Select
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-0" side="right" align="start">
              <Command>
                <CommandInput placeholder={placeholder} />
                <CommandList>
                  <CommandEmpty>No results found.</CommandEmpty>
                  <CommandGroup>
                    {_options.map((item) => {
                      if (item?.value === undefined) return null;
                      return (
                        <CommandItem
                          key={item.value}
                          value={item.value}
                          onSelect={(value) => {
                            // check if the option exist in values
                            const option = _value.find((_) => _ === value);
                            if (mode === "single") {
                              onChange?.([item.value]);
                              setOpen(false);
                            } else {
                              if (option)
                                onChange?.(
                                  _options
                                    .filter((option) => option.value !== value)
                                    .map((_) => _.value)
                                );
                              else onChange?.([..._value, item.value]);
                            }
                          }}
                        >
                          {item.icon}
                          <span>{item.label}</span>
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );
}
