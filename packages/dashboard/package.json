{"name": "fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@blocknote/core": "^0.12.4", "@blocknote/react": "^0.12.4", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^4.0.0", "@tanstack/react-table": "^8.10.7", "@trpc/client": "^10.44.1", "@trpc/react": "^9.27.4", "@trpc/react-query": "^10.44.1", "@trpc/server": "^10.44.1", "@types/mixpanel-browser": "^2.51.0", "@uiw/react-markdown-preview": "^5.1.1", "@xyflow/react": "^12.3.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "emblor": "^1.4.8", "lucide-react": "^0.378.0", "mixpanel-browser": "^2.58.0", "motion": "^11.18.1", "next": "14.0.3", "next-auth": "^4.24.5", "qrcode": "^1.5.3", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.9.1", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-time-input": "^1.0.1", "superjson": "1.13.3", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}