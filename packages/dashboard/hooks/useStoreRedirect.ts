"use client";
import { useEffect } from "react";

const ps = "https://play.google.com/store/apps/details?id=com.rabblehealth.app";
const as = "https://apps.apple.com/us/app/myrabble/id6463632712";

export default function useStoreRedirect(canRedirect = true) {
  useEffect(() => {
    if (!canRedirect) return;
    if (/Android/i.test(navigator.userAgent)) window.location.assign(ps);
    else window.location.assign(as);
  }, [canRedirect]);
  return null;
}
