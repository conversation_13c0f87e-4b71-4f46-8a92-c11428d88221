import { useState, useEffect } from 'react';

function useDebounce<T>(value: T, delay: number, callback?: (debouncedValue: T) => void): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
            if (callback) {
                callback(value);  // Call the callback with the debounced value
            }
        }, delay);

        // Cleanup function to clear the timeout if value or delay changes
        return () => {
            clearTimeout(handler);
        };
    }, [value, delay, callback]); // Re-run effect if value, delay, or callback changes

    return debouncedValue;
}

export default useDebounce;
