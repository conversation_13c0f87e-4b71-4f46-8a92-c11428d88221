{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@components/*": ["./app/components/*"], "@providers/*": ["./app/providers/*"], "@services/*": ["./app/services/*"], "@assets/*": ["./app/assets/*"], "@config/*": ["./app/config/*"], "@theme/*": ["./app/theme/*"], "@constants/*": ["./app/constants/*"], "@hooks/*": ["./app/hooks/*"], "@models/*": ["./app/models/*"], "@navigation/*": ["./app/navigation/*"], "@screens/*": ["./app/screens/*"], "@utils/*": ["./app/utils/*"]}}}