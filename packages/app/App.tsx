import Constants from "expo-constants";
import { requestTrackingPermissionsAsync } from "expo-tracking-transparency";
import { useEffect } from "react";
import { LogBox, Platform } from "react-native";
import { AppEventsLogger, Settings } from "react-native-fbsdk-next";
import "react-native-gesture-handler";
import {
	LogLevel,
	OSNotificationPermission,
	OneSignal,
} from "react-native-onesignal";
import * as Sentry from "sentry-expo";

import AppUpdateModal from "@components/common/AppUpdateModal";
import RootNavigator from "@navigation/root-navigator";
import RootProvider from "@providers/RootProvider";
import ExpoUpdateModal from "./app/components/common/ExpoUpdateModal";

// Configuration constants
const CONFIG = {
	FACEBOOK_APP_ID: "434973687769836",
	SENTRY_DSN:
		"https://<EMAIL>/4505826277457920",
	SENTRY_DEBUG: !__DEV__,
} as const;

/**
 * Initialize Facebook SDK and tracking
 */
const initializeFacebookSDK = async (): Promise<void> => {
	try {
		Settings.initializeSDK();

		// Log app open event
		AppEventsLogger.logEvent("APP_OPEN", {
			timestamp: Date.now(),
			platform: Platform.OS,
		});

		// Request tracking permissions
		const { status } = await requestTrackingPermissionsAsync();
		if (status === "granted") {
			await Settings.setAdvertiserTrackingEnabled(true);
		}
	} catch (error) {
		console.error("Failed to initialize Facebook SDK:", error);
		Sentry.Native.captureException(error);
	}
};

/**
 * Initialize Sentry error tracking
 */
const initializeSentry = (): void => {
	Sentry.init({
		dsn: CONFIG.SENTRY_DSN,
		enableInExpoDevelopment: true,
		debug: CONFIG.SENTRY_DEBUG,
	});
};

/**
 * Initialize OneSignal notifications
 */
const initializeNotifications = async (): Promise<void> => {
	try {
		OneSignal.Debug.setLogLevel(LogLevel.Debug);
		OneSignal.initialize(Constants.expoConfig?.extra?.oneSignalAppId);

		if (Platform.OS === "ios") {
			const permStatus = await OneSignal.Notifications.permissionNative();
			if (permStatus !== OSNotificationPermission.Denied) {
				await OneSignal.Notifications.requestPermission(true);
			}
		} else {
			const canRequestPermission =
				await OneSignal.Notifications.canRequestPermission();
			if (canRequestPermission) {
				await OneSignal.Notifications.requestPermission(true);
			}
		}
	} catch (error) {
		console.error("Failed to initialize notifications:", error);
		Sentry.Native.captureException(error);
	}
};

export default function App() {
	useEffect(() => {
		const initializeApp = async () => {
			try {
				if (!__DEV__) {
					// disabled facebook sdk setup in development
					await initializeFacebookSDK();
				}
				initializeSentry();
				await initializeNotifications();
			} catch (error) {
				console.error("Failed to initialize app:", error);
				Sentry.Native.captureException(error);
			}
		};

		initializeApp();
	}, []);

	// Disable all console warnings in production
	LogBox.ignoreAllLogs();

	return (
		<RootProvider>
			{!__DEV__ && <AppUpdateModal />}
			<RootNavigator />
			<ExpoUpdateModal />
		</RootProvider>
	);
}
