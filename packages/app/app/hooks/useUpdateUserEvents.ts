import { useEffect, useCallback } from "react";
import { trpc } from "@providers/RootProvider";
import AsyncStorage from "@utils/asyncStorage";

import { useSession } from "./persistUser";

const userVisitedKey = "visited-after-signin";

export default function useUserVisitedAppAfterDay() {
  const { user } = useSession();
  const updateUserEvents = trpc.user.updateUserEvents.useMutation();

  const fn = useCallback(async () => {
    if (user?.createdAt) {
      const userVisitedKeyPresent = await AsyncStorage.retrieveData(
        userVisitedKey
      );
      if (true || !userVisitedKeyPresent) {
        const createdAt = new Date(user.createdAt);
        const now = new Date();
        const hoursDifference =
          (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
        if (hoursDifference > 24) {
          updateUserEvents.mutateAsync(
            { visitedAgainAfterSignedIn: true },
            {
              onError: (err) => alert(err),
              onSuccess: async () => {
                await AsyncStorage.storeData(userVisitedKey, true);
                console.log("Created event after 24hrs");
              },
            }
          );
        }
      }
    }
  }, [user]);

  useEffect(() => {
    fn();
  }, [user?._id]);
}
