import * as ImagePicker from "expo-image-picker";
import { useState } from "react";

export default function useImagePicker() {
  const [images, setImages] = useState<ImagePicker.ImagePickerAsset[] | null>(
    null
  );

  const imagePicker = async (
    options: ImagePicker.ImagePickerOptions | undefined = {
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    }
  ) => {
    let result = await ImagePicker.launchImageLibraryAsync(options);

    if (result.canceled) return null;
    setImages(result.assets);
    return result.assets;
  };

  return { images, imagePicker };
}
