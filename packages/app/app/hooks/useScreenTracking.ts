import { queryClient, trpc } from "@providers/RootProvider";
import { useMutation, useQuery } from "@tanstack/react-query";
import AsyncStorage from "@utils/asyncStorage";

export const localOnBoardingProgressScreen = "progress-screen";

export default function useScreenTracking() {
  const setOnboardingProgressScreen = useMutation({
    mutationKey: [localOnBoardingProgressScreen],
    async mutationFn(screen: string) {
      await AsyncStorage.storeData(localOnBoardingProgressScreen, screen);
      await queryClient.cancelQueries({
        queryKey: [localOnBoardingProgressScreen],
      });
      queryClient.setQueryData([localOnBoardingProgressScreen], () => screen);
    },
  });

  const onboardingScreen = useQuery({
    queryKey: [localOnBoardingProgressScreen],
    async queryFn() {
      const screen = await AsyncStorage.retrieveData<string>(
        localOnBoardingProgressScreen
      );
      return screen;
    },
  });
  return {
    setOnboardingProgressScreen,
    onboardingScreen,
  };
}
