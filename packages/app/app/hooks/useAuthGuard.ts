import { useNavigation } from "@react-navigation/native";
import usePersistedUser, { useSession } from "./persistUser";
import { StackNavigationProp } from "@react-navigation/stack";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { useEffect, useRef } from "react";

export default function useAuthGuard() {
  const { user, isLoading } = useSession();

  const rootNavigation = useNavigation<StackNavigationProp<RootNavParams>>();

  const userRef = useRef(user);

  useEffect(() => {
    userRef.current = user;
  }, [user]);

  const authGuard = <T>(callback?: (...args: any[]) => T) => {
    if (!userRef.current?.token) {
      rootNavigation.push("LoginMobileScreen", { presentation: "modal" });
    } else return callback?.();
  };

  authGuard.user = userRef.current;
  return authGuard;
}
