import { trpc } from "@providers/RootProvider";
import Constants from "expo-constants";
import { Platform } from "react-native";
const appVer = Constants.expoConfig?.version;

export default function useAppUpdates() {
  const { data, isLoading, status } = trpc.appUpdates.appver.useQuery();
  const device = Platform.OS === "android" ? "android" : "ios";

  const isUpdateAvailable =
    data?.[device]?.v !== appVer &&
    data?.[device]?.isOptionalUpdate === false &&
    status === "success";

  return {
    isUpdateAvailable,
    data: data?.[device],
    isLoading,
  };
}
