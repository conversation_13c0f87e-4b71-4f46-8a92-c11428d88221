import { useEffect } from 'react';
import mixpanel from '@utils/mixpanel';
import AsyncStorage from '@utils/asyncStorage';
import { User } from '@models/User';
import { JwtUser } from '../../../shared/types/user';

export function useAppOpenEvent() {
  useEffect(() => {
    (async () => {
      const user = await AsyncStorage.retrieveData<JwtUser>('persisted-user');

      mixpanel.trackEvent(
        'APP_OPEN',
        {
          isGuestUser: !user?._id,
          email: user?.email!,
          firstname: user?.firstname,
          lastname: user?.lastname,
          phone:
            user?.contact?.countryCode ??
            (user?.contact?.countryCode + user?.contact?.phone || ''),
        },
        String(user?._id)
      );
    })();
  }, []);
}
