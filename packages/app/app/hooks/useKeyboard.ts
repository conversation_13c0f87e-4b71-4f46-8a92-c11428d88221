import { useEffect, useState } from "react";
import { Keyboard, Platform, KeyboardEvent } from "react-native";

const useKeyboard = () => {
  const [keyboardOpen, setKeyboardOpen] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState<number>(0);

  useEffect(() => {
    let keyboardDidShowListener: any;
    let keyboardDidHideListener: any;

    if (Platform.OS === "ios") {
      keyboardDidShowListener = Keyboard.addListener(
        "keyboardWillShow",
        (e: KeyboardEvent) => {
          setKeyboardHeight(e.endCoordinates.height);
          setKeyboardOpen(true);
        }
      );

      keyboardDidHideListener = Keyboard.addListener("keyboardWillHide", () => {
        setKeyboardHeight(0);
        setKeyboardOpen(false);
      });
    } else {
      keyboardDidShowListener = Keyboard.addListener(
        "keyboardDidShow",
        (e: KeyboardEvent) => {
          setKeyboardHeight(e.endCoordinates.height);
          setKeyboardOpen(true);
        }
      );

      keyboardDidHideListener = Keyboard.addListener("keyboardDidHide", () => {
        setKeyboardHeight(0);
        setKeyboardOpen(false);
      });
    }

    return () => {
      setKeyboardOpen(false);
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return { keyboardHeight, keyboardOpen };
};

export default useKeyboard;
