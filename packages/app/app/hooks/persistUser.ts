import { queryClient, trpc } from "@providers/RootProvider";
import { useMutation, useQuery } from "@tanstack/react-query";
import AsyncStorage from "@utils/asyncStorage";
import { JwtUser } from "../../../shared/types/user";

// this hook provides a wrapper around async store to Create, Read, Delete user
export const localUserKey = "persisted-user";
export const localUserKeyNew = "user-context";

export default function usePersistedUser(state: "signup" | "login" = "login") {
  /**
   * retrieve user if it exists in local storage (DEPRECATED | Will be removed after migration)
   */
  const persistedUser = useQuery({
    queryKey: [localUserKey],
    async queryFn() {
      const user = await AsyncStorage.retrieveData<JwtUser>(localUserKey);
      return user;
    },
  });

  // store user in local storage
  const setPersistedUser = useMutation({
    mutationKey: [localUserKey],
    async mutationFn(user: JwtUser) {
      await AsyncStorage.storeData(localUserKey, user);

      await queryClient.cancelQueries({ queryKey: [localUserKey] });

      queryClient.setQueryData([localUserKey], () => user);
    },
  });

  const removePersistedUser = useMutation({
    mutationKey: [localUserKey],
    mutationFn() {
      return AsyncStorage.removeData(localUserKey);
    },
    onSettled() {
      queryClient.invalidateQueries({ queryKey: [localUserKey] });
    },
  });

  const user = useQuery({
    queryKey: [localUserKeyNew],
    async queryFn() {
      const user = await AsyncStorage.retrieveData<JwtUser>(localUserKeyNew);
      return user;
    },
  });

  const setUser = useMutation({
    mutationKey: [localUserKeyNew],
    async mutationFn(user: JwtUser) {
      await AsyncStorage.storeData(localUserKeyNew, user);

      await queryClient.cancelQueries({ queryKey: [localUserKeyNew] });

      queryClient.setQueryData([localUserKeyNew], () => user);
    },
  });

  return {
    userKey: localUserKey,
    persistedUser,
    setPersistedUser,
    removePersistedUser,
    user,
    setUser,
  };
}

export function useSession() {
  const { persistedUser, removePersistedUser } = usePersistedUser();
  const { data: _user, isLoading: persistedUserLoading } = persistedUser;
  const token = _user?.token;

  const {
    data: user,
    isLoading: userLoading,
    status,
    refetch,
  } = trpc.user.me.useQuery(undefined, {
    enabled: !!token,
    onError(err) {
      if (err.message === "user not found in database") {
        removePersistedUser.mutateAsync();
        queryClient.clear();
      }
    },
  });

  return {
    user: token ? { ...user, token } : undefined,
    isLoading: token ? userLoading || persistedUserLoading : false,
    refetch,
    status,
  };
}
