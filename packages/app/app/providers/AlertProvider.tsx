import InfoModal from '@components/common/InfoModal';
import { TRPCClientError } from '@trpc/client';
import React, { useCallback, useRef, useState } from 'react';
import { Modal, Text, Button, View } from 'react-native';

type RefMethods = { show: (message?: string) => void };

let ref: React.MutableRefObject<RefMethods>;

const AlertProvider = React.forwardRef(function (
  _,
  ref: React.ForwardedRef<RefMethods>
) {
  const [visible, setVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  const show = (message?: string) => {
    setVisible(true);
    setAlertMessage(message ?? '');
  };

  const close = () => {
    setVisible(false);
    setAlertMessage('');
  };

  React.useImperativeHandle(
    ref,
    React.useCallback(
      () => ({
        show,
      }),
      [show]
    )
  );

  return (
    <InfoModal
      visible={visible}
      showLogo
      description={alertMessage}
      actionText='ok'
      handleAction={close}
    />
  );
});

export default function () {
  return (
    <AlertProvider
      ref={(_ref) => {
        if (_ref) ref = { current: _ref };
      }}
    />
  );
}

export function alert(message: string | Error | any) {
  let text = '';
  if (message instanceof TRPCClientError || message instanceof Error)
    text = message.message;
  else text = message;
  ref.current?.show(text);
}
