import { ComponentType, createContext, useContext } from 'react';
import { GestureResponderEvent } from 'react-native';

type OnPressListenerProps = {
  onPress?: ((event: GestureResponderEvent) => void) | null | undefined;
  actionTag?: string;
  metadata?: Record<string, string>;
};

// Global listener
export type TouchContextProps = {
  onPress: (payload: {
    actionTag?: string;
    metadata?: Record<string, string>;
  }) => void;
};

const TouchContext = createContext<TouchContextProps | undefined>(undefined);

export const TouchProvider = ({
  children,
  onPress,
}: React.PropsWithChildren<TouchContextProps>) => {
  return (
    <TouchContext.Provider value={{ onPress }}>
      {children}
    </TouchContext.Provider>
  );
};

/** Button Press Listner */
export const onPressListener = <T extends object>(
  WrappedComponent: ComponentType<T>
): ComponentType<T & OnPressListenerProps> => {
  return ({ onPress, ...props }: OnPressListenerProps & { title?: string }) => {
    const context = useContext(TouchContext);
    // if (!context) {
    //   throw new Error('onPressListener must be used within a TouchProvider');
    // }

    const handlePress = (e: GestureResponderEvent) => {
      context?.onPress?.({
        metadata: props.metadata,
        actionTag: props.actionTag || props.title || '',
      });
      onPress?.(e);
    };

    return (
      <WrappedComponent
        {...(props as T)}
        {...(props.title && { actionTag: props.title })}
        onPress={handlePress}
      />
    );
  };
};
