import { FlatList, View } from "react-native";
import { RouterOutput } from "../../../../shared";
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import {
  eachDayOfInterval,
  endOfWeek,
  format,
  isAfter,
  isSameDay,
  isToday,
  isYesterday,
  startOfWeek,
  subDays,
} from "date-fns";
import { ManageWeekDay } from "@components/manage/ManageWeekDay";
import AppText from "@components/common/AppText";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import CalendarIcon from "@assets/svg/CalendarIcon";

export default function ManageWeeklyInfoSection({
  dailyTrackers,
  endDate,
  onDateSelect,
  selectedWeekDay,
  startDate,
}: {
  startDate: Date;
  endDate: Date;
  dailyTrackers: RouterOutput["manage"]["getUserDailyTrackers"];
  selectedWeekDay: Date;
  onDateSelect: (date: Date) => void;
}) {
  const datesRange = eachDayOfInterval({
    start: startOfWeek(subDays(startDate, 7)),
    end: endOfWeek(endDate),
  });
  const navigation = useNavigation<StackNavigationProp<ManageNavParams>>();
  const ref = useRef<FlatList>(null);

  useEffect(() => {
    if (ref.current && selectedWeekDay) {
      const selectedIndex = datesRange.findIndex((date) =>
        isSameDay(date, selectedWeekDay)
      );
      if (selectedIndex !== -1) {
        ref.current?.scrollToIndex({ index: selectedIndex, animated: false, viewPosition: 0.5, });
      }
    }
  }, [ref.current, selectedWeekDay, datesRange]);
  

  const getItemLayout = useCallback(
    (data: any, index: number) => ({
      length: 80, // Adjust this to match your ManageWeekDay component's width
      offset: 80 * index,
      index,
    }),
    [datesRange]
  );

  return (
    <View className="">
      <View className="flex-row items-center justify-between">
        <AppText className="text-[20px] font-montserratSemiBold my-[23px] text-[#336d9f]">
          {isToday(selectedWeekDay)
            ? `Today, ${format(selectedWeekDay, "MMM dd")}`
            : isYesterday(selectedWeekDay)
            ? `Yesterday, ${format(selectedWeekDay, "MMM dd")}`
            : `${format(selectedWeekDay, "EEE, MMM dd")}`}
        </AppText>
        <CalendarIcon
          onPress={() => navigation.navigate("ManageCalendarScreen")}
        />
      </View>

      <View>
        <FlatList
          getItemLayout={getItemLayout}
          pagingEnabled
          decelerationRate="fast"
          ref={ref}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ marginBottom: 4 }}
          data={datesRange}
          keyExtractor={(date) => String(date)}
          renderItem={({ item: date, index }) => {
            const tracker = dailyTrackers.find((t) =>
              isSameDay(new Date(t.date), date)
            );
            const trackerExists = !!tracker;
            const disabledDate = isAfter(date, new Date());
            const active = isSameDay(date, selectedWeekDay);

            return (
              <ManageWeekDay
                day={format(date, "EE").substring(0, 2)}
                date={format(date, "dd")}
                disabled={disabledDate}
                dateObject={date}
                active={active}
                showCheckIcon={trackerExists}
                partiallyAnswered={!tracker?.completed}
                onPress={() => {
                  // ref.current?.scrollToIndex({
                  //   index: 5,
                  //   animated: false,
                  // });
                  onDateSelect(date);
                }}
              />
            );
          }}
        />
      </View>
    </View>
  );
}
