import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import AppFormInput from "@components/form/AppFormInput";
import AppFormOtpInput from "@components/form/AppFormOtpInput";
import { ENV } from "@constants/index";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "@hooks/persistUser";
import { trpc } from "@providers/RootProvider";
import clsx from "clsx";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { Modal, View } from "react-native";
import { z } from "zod";

interface EmailPhoneUpdateProps {
  userOrPatientId: string;
}

const schema = z
  .object({
    email: z.string().email().or(z.literal("")).optional(),
    phone: z.string().optional(),
    otp: z.string().length(4, "invalid otp"),
  })
  .superRefine(({ email, phone }, ctx) => {
    if (!(email || phone))
      return ctx.addIssue({
        code: "custom",
        message: "email or phone is required",
        path: ["phone", "email"],
      });
  });

type Form = z.infer<typeof schema>;

export default function EmailPhoneUpdate({
  userOrPatientId,
}: EmailPhoneUpdateProps) {
  const [showModal, setShowModal] = useState(false);
  const [type, setType] = useState<"phone" | "email">("email");

  const methods = useForm<Form>({
    defaultValues: { otp: "" },
    resolver: zodResolver(schema),
  });

  const { data } = trpc.user.me.useQuery(userOrPatientId, {
    onSuccess: (data) => {
      methods.reset({
        email: data.email,
        phone: data.contact?.phone,
      });
    },
  });

  const requestUpdateEmailPhoneOtp =
    trpc.user.requestUpdateEmailPhoneOtp.useMutation();
  const validateUpdateEmailPhoneOtp =
    trpc.user.validateUpdateEmailPhoneOtp.useMutation();
  const updateEmail = trpc.user.updateUserOrPatientEmail.useMutation();
  const updatePhone = trpc.user.updateUserOrPatientPhone.useMutation();

  const currentEmailAddress = methods.watch("email");
  const currentPhoneNumber = methods.watch("phone");

  const isEmailSame = data && data?.email === currentEmailAddress;
  const isPhoneSame = data && data?.contact?.phone === currentPhoneNumber;

  const requestOtp = async (type: "phone" | "email") => {
    methods.clearErrors();
    methods.setValue("otp", "");
    setType(type);

    try {
      if (type === "email") z.string().email().parse(methods.getValues(type));
    } catch (ex) {
      return methods.setError("email", { message: `invalid email` });
    }

    if (!methods.getValues(type)) {
      return methods.setError(type, { message: `${type} is required` });
    }
    try {
      await requestUpdateEmailPhoneOtp.mutateAsync(
        {
          updateType: type,
          [type]: methods.getValues(type),
          userOrPatientId,
        },
        {
          onSuccess: () => setShowModal(true),
          onError: (err) => {
            methods.setError(type, { message: err.message });
          },
        }
      );
    } catch (ex) {}
  };

  const utils = trpc.useUtils();

  const clearOtp = () => {
    methods.setValue("otp", "");
    methods.clearErrors("otp");
  };

  const revalidateData = () => {
    setShowModal(false);
    utils.user.me.invalidate();
    utils.user.meOrPatientProfile.invalidate();
  };
  const handleSubmit = methods.handleSubmit(async (data) => {
    await validateUpdateEmailPhoneOtp.mutateAsync(
      { otp: data.otp, userOrPatientId },
      {
        onError: (err) => methods.setError("otp", { message: err.message }),
        onSuccess: async () => {
          if (type === "email") {
            if (data.email)
              await updateEmail.mutateAsync(
                { email: data.email, userOrPatientId },
                {
                  onError(err) {
                    methods.setError("email", { message: err.message });
                  },
                  onSuccess: () => revalidateData(),
                }
              );
          } else {
            if (data.phone)
              await updatePhone.mutateAsync(
                { contact: { phone: data.phone }, userOrPatientId },
                {
                  onError(err) {
                    methods.setError("phone", { message: err.message });
                  },
                  onSuccess: () => revalidateData(),
                }
              );
          }
        },
      }
    );
  });

  const isEmailError = methods.formState.errors.email;
  const isPhoneError = methods.formState.errors.phone;

  return (
    <View className="flex mb-2" style={{ gap: 8 }}>
      <FormProvider {...methods}>
        <Modal
          transparent
          visible={showModal}
          onRequestClose={() => setShowModal(false)}
        >
          <View className="flex flex-1 bg-neutral-400/30 justify-center items-center">
            <View className="w-[70%] h-56 bg-white rounded-lg p-7">
              <Pressable
                actionTag="email otp modal"
                className="absolute right-2 top-2 p-2"
                onPress={() => setShowModal(false)}
              >
                {/* You can use any close icon or text here */}
                <AppText className="text-black text-lg">X</AppText>
              </Pressable>
              <AppText className="font-montserratSemiBold text-primary mb-4">
                magic code
              </AppText>
              <AppFormOtpInput name="otp" />
              <Pressable
                actionTag="clear otp"
                onPress={clearOtp}
                className="pb-2"
              >
                <AppText className="text-center text-blue mb">Clear</AppText>
              </Pressable>
              <View className="flex flex-row">
                <AppButton onPress={handleSubmit} title="Update" />
              </View>
            </View>
          </View>
        </Modal>
        <View className="flex flex-row items-center w-full">
          <AppFormInput
            name="email"
            label="Email"
            placeholder="Enter email"
            keyboardType="email-address"
            wrapperClass="flex-1"
            autoCapitalize={"none"}
          />
          {!isEmailSame && (
            <Pressable
              actionTag="request email otp"
              onPress={() => requestOtp("email")}
              className={clsx(
                "p-4 bg-accent rounded-lg ml-2 mt-7",
                isEmailError && "mb-5"
              )}
            >
              <AppText className="text-white">Update</AppText>
            </Pressable>
          )}
        </View>
        <View className="flex flex-row items-center">
          <AppFormInput
            name="phone"
            label="Phone"
            placeholder="Enter phone"
            keyboardType="number-pad"
            wrapperClass="flex-1"
          />
          {!isPhoneSame && (
            <Pressable
              actionTag="request phone otp"
              onPress={() => requestOtp("phone")}
              className={clsx(
                "p-4 bg-accent rounded-lg ml-2 mt-7",
                isPhoneError && "mb-5"
              )}
            >
              <AppText className="text-white">Update</AppText>
            </Pressable>
          )}
        </View>
      </FormProvider>
    </View>
  );
}
