import AppText from "@components/common/AppText";
import { styled } from "nativewind";
import { StyleSheet, View } from "react-native";
import Plus from "@assets/svg/Plus";
import ModalBottomSheet from "@components/common/ModalBottomSheet";
import { useState } from "react";
import SectionLink from "@components/profile/ProfileSectionItem";
import { StackNavigationProp } from "@react-navigation/stack";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { useNavigation } from "@react-navigation/native";
import Pressable from "@components/common/Pressable";

const Wrapper = styled(
  Pressable,
  "flex flex-row items-center py-4 px-4 bg-white rounded-xl my-6"
);

export default function AddNewPatient(props: any) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();

  function navigateAndCloseModal(navigateTo: any) {
    setIsModalOpen(false);
    navigation.navigate(navigateTo);
  }

  return (
    <Wrapper
      {...props}
      style={[
        styles.container,
        { borderTopStartRadius: 0, borderTopEndRadius: 0 },
      ]}
    >
      <Pressable
        actionTag="add new patient"
        onPress={() => setIsModalOpen((_isModalOpen) => !_isModalOpen)}
        className="flex-1 flex-row justify-between items-center"
      >
        <AppText className="text-sm font-montserratMedium">
          {props.patientsExists ? "add another patient" : "add a patient"}
        </AppText>

        <Plus />
      </Pressable>
      <ModalBottomSheet
        visible={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      >
        {/* Content of the modal */}
        <View className="my-6 mx-2">
          <View>
            <SectionLink
              showRightArrow={false}
              title="Find the patient"
              onPress={() => navigateAndCloseModal("FindCaregiverPatient")}
            />
            <SectionLink
              showRightArrow={false}
              title="Create a new patient profile"
              onPress={() => navigateAndCloseModal("CreateCaregiverPatient")}
            />
          </View>
        </View>
      </ModalBottomSheet>
    </Wrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    shadowColor: "#004987",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    padding: 0,
    top: 0,
    marginTop: -23,
  },
});
