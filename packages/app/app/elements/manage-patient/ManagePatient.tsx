import AppText from "@components/common/AppText";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Modal, View } from "react-native";
import { RouterOutput } from "../../../../shared";
import React, { useState } from "react";
import AppButton from "@components/common/AppButton";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { ChevronDown, HeartHandshake, User } from "lucide-react-native";
import LogoSimple from "@assets/svg/LogoOutline";
import { Image as CacheImage } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import ModalBottomSheet from "@components/common/ModalBottomSheet";
import Pressable from "@components/common/Pressable";

type Props = {
  patients?: RouterOutput["user"]["getCareGiverPatients"];
};

export default function ManagePatient({ patients }: Props) {
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();

  const [open, setOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<string>();
  const [bottomSheetVisible, setBottomSheetVisible] = useState(false);

  return (
    <>
      <View
        className="p-3"
        style={{
          shadowColor: "#004987",
          shadowOffset: { width: 1, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 1,
          elevation: 4,
        }}
      >
        {/* <Modal
          animationType="fade"
          transparent
          visible={open}
          onRequestClose={() => setOpen(false)}
        >
          <Pressable
            onPress={() => setOpen(false)}
            className="flex-1 bg-neutral-500/50 justify-end"
          >
            <Pressable className="bg-white flex-[0.5] rounded-xl p-4">
              <AppText className="font-montserratMedium">
                Choose Options
              </AppText>
              <View className="gap-3 mt-2">
                <Pressable
                  className="flex-row"
                  style={{ gap: 8 }}
                  onPress={() =>
                    selectedPatient && patientUserProfile(selectedPatient)
                  }
                >
                  <User color="#004987" />
                  <AppText className="font-montserratMedium">
                    Manage User Profile
                  </AppText>
                </Pressable>
                <Pressable
                  className="flex-row"
                  style={{ gap: 8 }}
                  onPress={() =>
                    selectedPatient && patientHealthProfile(selectedPatient)
                  }
                >
                  <HeartHandshake color="#004987" />
                  <AppText className="font-montserratMedium">
                    Manage Health Profile
                  </AppText>
                </Pressable>
              </View>
            </Pressable>
          </Pressable>
        </Modal> */}
        <View className="flex-row justify-between items-center">
          <AppText className="text-sm font-montserratMedium">
            managing patients
          </AppText>
          <ChevronDown color="#004987" />
        </View>
        {patients && patients.length > 0 && (
          <Pressable
            actionTag="manage patient item"
            onPress={() => {
              setBottomSheetVisible(true);
            }}
            key={patients[0]._id.toString()}
            className="flex-row items-center mt-2 justify-start"
          >
            <View>
              {patients[0]?.profilePicture ? (
                <CacheImage
                  uri={imageKit({
                    imagePath: patients[0]?.profilePicture,
                    transform: ["w-100"],
                  })}
                  preview={{
                    uri: imageKit({
                      imagePath: patients[0]?.profilePicture,
                      transform: ["w-30", "bl-6"],
                    }),
                  }}
                  tint="dark"
                  transitionDuration={300}
                  style={{
                    borderWidth: 0.1,
                    borderRadius: 20,
                    width: 40,
                    height: 40,
                  }}
                />
              ) : (
                <LogoSimple width={45} height={45} />
              )}
            </View>
            <View>
              <View className="flex-row">
                <AppText className="text-md font-montserratBold ml-4">
                  {patients[0]?.firstname} {patients[0]?.lastname}
                </AppText>
                <AppText className="text-sm font-montserratLight ml-4 mt-0.5">
                  {patients[0].username}
                </AppText>
              </View>
              <View>
                <AppText className="text-md font-montserratMedium ml-4">
                  patient profile
                </AppText>
              </View>
            </View>
          </Pressable>
        )}
      </View>
      <ModalBottomSheet
        // Make sure to set a unique key for each modal
        visible={bottomSheetVisible}
        onClose={setBottomSheetVisible}
      >
        {/* Content of the modal */}
        <View className="my-4 mx-2">
          <AppText className="font-montserratMedium text-xl">
            Choose Patient
          </AppText>
          <AppText className="font-montserratLight text-sm my-2">
            Choose a patient to provide support. The entire app will be
            configured as per the default patient selection.
          </AppText>
          {patients?.map((user) => (
            <Pressable
              key={user._id.toString()}
              onPress={() => {
                navigation.navigate("PatientUserHealthProfile", {
                  userOrPatientId: user._id.toString(),
                });
                setOpen(true);
                setSelectedPatient(user._id.toString());
                setBottomSheetVisible(false);
              }}
              className="flex-row items-center mt-2 justify-start"
            >
              <View>
                {user?.profilePicture ? (
                  <CacheImage
                    uri={imageKit({
                      imagePath: user?.profilePicture,
                      transform: ["w-100"],
                    })}
                    preview={{
                      uri: imageKit({
                        imagePath: user?.profilePicture,
                        transform: ["w-30", "bl-6"],
                      }),
                    }}
                    tint="dark"
                    transitionDuration={300}
                    style={{
                      borderRadius: 30,
                      width: 45,
                      height: 45,
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  />
                ) : (
                  <View
                    style={{
                      borderRadius: 30,
                      width: 45,
                      height: 45,
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <LogoSimple width={35} height={35} />
                    {/* Adjust width and height to fit inside the container */}
                  </View>
                )}
              </View>
              <View>
                <View className="flex-1 flex-row justify-end">
                  <View>
                    <AppText className="text-md font-montserratBold ml-4">
                      {user?.firstname} {user?.lastname}
                    </AppText>
                    <AppText className="text-sm font-montserratLight ml-4 mt-0.5">
                      {user.username}
                    </AppText>
                  </View>
                </View>
              </View>
            </Pressable>
          ))}
        </View>
      </ModalBottomSheet>
    </>
  );
}
