import AppText from "@components/common/AppText";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Modal, View } from "react-native";
import React, { useState } from "react";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { ChevronDown, HeartHandshake, User } from "lucide-react-native";
import LogoSimple from "@assets/svg/LogoOutline";
import { Image as CacheImage } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import ModalBottomSheet from "@components/common/ModalBottomSheet";
import { UserAccount } from "../../../../shared/types/user";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import DeleteIcon from "@assets/svg/DeleteIcon";
import Pressable from "@components/common/Pressable";

type Props = {
  caregivers?: UserAccount[];
};

export default function PatientCaregivers({ caregivers }: Props) {
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();
  const { refetch } = useSession();
  const [open, setOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<string>();
  const [bottomSheetVisible, setBottomSheetVisible] = useState(false);

  const patientUserProfile = (patientId: string) => {
    setOpen(false);
    navigation.navigate("CreateProfileScreen", {
      userOrPatientId: patientId,
    });
  };

  const revokeCareGiverAccess = trpc.user.revokeCareGiverAccess.useMutation();

  return (
    <>
      <View
        className="p-3"
        style={{
          shadowColor: "#004987",
          shadowOffset: { width: 1, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 1,
          elevation: 4,
        }}
      >
        <Modal
          animationType="fade"
          transparent
          visible={open}
          onRequestClose={() => setOpen(false)}
        >
          <Pressable
            onPress={() => setOpen(false)}
            className="flex-1 bg-neutral-500/50 justify-end"
            actionTag="categivers modal"
          >
            <Pressable
              className="bg-white flex-[0.5] rounded-xl p-4"
              actionTag="caregiver options"
            >
              <AppText className="font-montserratMedium">
                Choose Options
              </AppText>
              <View className="gap-3 mt-2">
                <Pressable
                  className="flex-row"
                  actionTag="select patient"
                  style={{ gap: 8 }}
                  onPress={() =>
                    selectedPatient && patientUserProfile(selectedPatient)
                  }
                >
                  <User color="#004987" />
                  <AppText className="font-montserratMedium">
                    Caregivers
                  </AppText>
                </Pressable>
              </View>
            </Pressable>
          </Pressable>
        </Modal>
        <View className="flex-row justify-between items-center">
          <AppText className="text-sm font-montserratMedium">
            manage caregivers
          </AppText>
          <ChevronDown color="#004987" />
        </View>
        {caregivers && caregivers.length > 0 && (
          <Pressable
            actionTag="caregiver item"
            onPress={() => {
              setBottomSheetVisible(true);
            }}
            key={caregivers[0]._id.toString()}
            className="flex-row items-center mt-2 justify-start"
          >
            <View>
              {caregivers[0]?.profilePicture ? (
                <CacheImage
                  uri={imageKit({
                    imagePath: caregivers[0]?.profilePicture,
                    transform: ["w-100"],
                  })}
                  preview={{
                    uri: imageKit({
                      imagePath: caregivers[0]?.profilePicture,
                      transform: ["w-30", "bl-6"],
                    }),
                  }}
                  tint="dark"
                  transitionDuration={300}
                  style={{
                    borderWidth: 0.1,
                    borderRadius: 20,
                    width: 40,
                    height: 40,
                  }}
                />
              ) : (
                <LogoSimple width={45} height={45} />
              )}
            </View>
            <View>
              <View className="flex-row">
                <AppText className="text-md font-montserratBold ml-4">
                  {caregivers[0]?.firstname} {caregivers[0]?.lastname}
                </AppText>
                <AppText className="text-sm font-montserratLight ml-4 mt-0.5">
                  {caregivers[0]?.username}
                </AppText>
              </View>
              <View></View>
            </View>
          </Pressable>
        )}
      </View>
      <ModalBottomSheet
        // Make sure to set a unique key for each modal
        visible={bottomSheetVisible}
        onClose={setBottomSheetVisible}
      >
        {/* Content of the modal */}
        <View className="my-4 mx-2">
          <AppText className="font-montserratMedium text-xl">
            Choose Caregiver
          </AppText>
          <AppText className="font-montserratLight text-sm my-2">
            Tap on caregivers to revoke access to stop managing your profile on
            your behalf
          </AppText>
          {caregivers?.map((user) => {
            return (
              <Pressable
                actionTag="caregiver item"
                key={user._id.toString()}
                onPress={async () => {
                  try {
                    await revokeCareGiverAccess.mutateAsync({
                      caregiverId: user._id.toString(),
                    });
                    refetch();
                    alert("Caregiver access revoked");
                  } catch (e) {
                    alert(e);
                  }
                }}
                className="flex flex-row items-center mt-2 justify-start"
              >
                <View>
                  {user?.profilePicture ? (
                    <CacheImage
                      uri={imageKit({
                        imagePath: user?.profilePicture,
                        transform: ["w-100"],
                      })}
                      preview={{
                        uri: imageKit({
                          imagePath: user?.profilePicture,
                          transform: ["w-30", "bl-6"],
                        }),
                      }}
                      tint="dark"
                      transitionDuration={300}
                      style={{
                        borderRadius: 30,
                        width: 45,
                        height: 45,
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    />
                  ) : (
                    <View
                      style={{
                        borderRadius: 30,
                        width: 45,
                        height: 45,
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <LogoSimple width={35} height={35} />
                      {/* Adjust width and height to fit inside the container */}
                    </View>
                  )}
                </View>
                <View className="flex-1 flex-row justify-space-between">
                  <View>
                    <AppText className="text-md font-montserratBold ml-4">
                      {user?.firstname} {user?.lastname}
                    </AppText>
                    <AppText className="text-sm font-montserratLight ml-4 mt-0.5">
                      {user?.username}
                    </AppText>
                  </View>
                  <DeleteIcon style={{ position: "absolute", right: 0 }} />
                </View>
              </Pressable>
            );
          })}
        </View>
      </ModalBottomSheet>
    </>
  );
}
