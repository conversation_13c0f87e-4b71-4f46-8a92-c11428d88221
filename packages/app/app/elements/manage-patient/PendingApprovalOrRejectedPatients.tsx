import _ from 'lodash';
import AppText from '@components/common/AppText';
import { styled } from 'nativewind';
import { StyleSheet, View } from 'react-native';

import { CaregiverApprovalStatus } from '../../../../shared/enums/user';
import { UserAccount } from '../../../../shared/types/user';
import { RouterOutput } from '../../../../shared';
import { useMemo } from 'react';
import Pressable from '@components/common/Pressable';

const Wrapper = styled(
  Pressable,
  'flex flex-row items-center py-4 px-4 bg-white rounded-xl my-6'
);

function getDisplayName(user: UserAccount) {
  const fullName = `${user.firstname} ${user.lastname}`;
  return _.truncate(fullName, {
    length: 15,
    omission: '...',
  });
}

type Props = {
  pendingApprovalOrRejectPatients?: RouterOutput['user']['getCareGiverPatients'];
  userId: string | undefined;
};

export default function PendingApprovalOrRejectedPatients(props: Props) {
  return props.pendingApprovalOrRejectPatients?.map((patient) => {
    const managedByMap = _.keyBy(patient.managedBy, 'user');
    const getStatus = () => {
      if (props.userId) {
        return managedByMap[props.userId]?.approvalStatus ===
          CaregiverApprovalStatus.PENDING_APPROVAL
          ? 'Awaiting patient approval'
          : 'Patient rejected';
      }
      return '';
    };
    return (
      <Wrapper
        key={patient._id.toString()}
        {...props}
        style={[
          styles.container,
          {
            borderTopStartRadius: 0,
            borderTopEndRadius: 0,
            borderBottomStartRadius: 0,
            borderBottomEndRadius: 0,
          },
        ]}
      >
        <View className='flex flex-row items-center justify-between'>
          <AppText
            style={styles.view}
            className='flex-1 text-sm font-montserratMedium'
          >
            {getStatus()}
          </AppText>
          <AppText
            style={[styles.view, styles.name]}
            className='font-montserratBold text-sm bold'
          >
            {getDisplayName(patient)}
          </AppText>
        </View>
      </Wrapper>
    );
  });
}

const styles = StyleSheet.create({
  container: {
    shadowColor: '#004987',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    padding: 0,
    margin: 0,
    top: 0,
    marginTop: -23,
  },
  view: {
    padding: 0,
    margin: 0,
  },
  name: {
    textAlign: 'right',
  },
});
