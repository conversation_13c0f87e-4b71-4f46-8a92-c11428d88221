import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import Divider from "@components/common/Divider";
import { trpc } from "@providers/RootProvider";
import clsx from "clsx";
import _ from "lodash";
import React, { useState } from "react";
import { Dimensions, FlatList, Modal, View } from "react-native";
import Pressable from "@components/common/Pressable";
import AppCheckBox from "@components/common/AppCheckbox";

export default function TagsFilter({
  name,
  setShowModal,
  showModal,
  tags,
  selectedTags,
  setSelectedTags,
}: {
  name: string;
  showModal: boolean;
  setShowModal: (v: boolean) => void;
  tags: any[];
  selectedTags: any[];
  setSelectedTags: (v: any[]) => void;
}) {
  const [selectedAllTags, setSelectedAllTags] = useState<any[]>(
    selectedTags ?? []
  );

  return (
    <>
      <Modal
        animationType="fade"
        transparent
        visible={showModal}
        onRequestClose={() => setShowModal(false)}
      >
        <View className="flex flex-1 justify-end bg-black/20 mb-[-200px]">
          <Pressable
            actionTag="select disease tags"
            className="flex-1"
            onPress={() => setShowModal(false)}
          />
          {/* Content */}
          <View className="flex-[1] bg-white rounded-t-2xl z-10">
            <AppText className="font-montserratSemiBold text-base text-primary-dark m-4">
              Select Tags
            </AppText>
            {/* <Divider className="border-neutral-300" /> */}

            {/* {!!selectedTags.length && (
              <Divider className="border-neutral-300 mt-2" />
            )} */}

            <View className="my-4 ml-4">
              {/* <AppText className="text-lg font-bold mb-2">Select Tags</AppText> */}
              <FlatList
                data={tags}
                keyExtractor={(item) => item}
                renderItem={({ item }) => (
                  <Pressable
                    onPress={() => {
                      setSelectedAllTags((prev: any[]) =>
                        prev.includes(item)
                          ? prev.filter((tag) => tag !== item)
                          : [...prev, item]
                      );
                    }}
                  >
                    <View className="flex-row mb-4">
                      <AppCheckBox
                        active={selectedAllTags.includes(item)}
                        pointerEvents="none"
                      />
                      <View className="ml-2">
                        <AppText className="text-base">{item}</AppText>
                      </View>
                    </View>
                  </Pressable>
                )}
              />
            </View>

            {/* Footer */}
            <View className="flex-row p-4" style={{ gap: 8 }}>
              <AppButton
                title="Reset"
                variant="outline"
                className="rounded-lg px-4 py-2 m-[4px]"
                onPress={() => {
                  setSelectedAllTags([]);
                }}
              />
              <AppButton
                title="Apply"
                // variant="accent"
                className="rounded-lg px-4 py-2 m-[4px]"
                onPress={() => {
                  setShowModal(false);
                  setSelectedTags(selectedAllTags);
                }}
              />
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}
