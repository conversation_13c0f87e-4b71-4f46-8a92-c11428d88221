import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import Divider from "@components/common/Divider";
import { trpc } from "@providers/RootProvider";
import clsx from "clsx";
import React, { useState } from "react";
import { Dimensions, FlatList, Modal, Platform, View } from "react-native";
import Animated, { Layout } from "react-native-reanimated";
import { RouterOutput } from "../../../../shared";
import { Controller, useFormContext } from "react-hook-form";
import Pressable from "@components/common/Pressable";
import { ChevronLeft, ChevronRight } from "lucide-react-native";
import theme from "@constants/theme";

const springAnim = Layout.springify().duration(1000);

type SelectedDisease =
  keyof RouterOutput["lib"]["healthProfileOptions"]["diseaseTypes"];

const { width: WIDTH } = Dimensions.get("screen");
const COLLAPSED_WIDTH = 100;
const ROW_WIDTH = WIDTH - COLLAPSED_WIDTH * 2;

const widthSelector = (selected: boolean, tabIdx: number) => {
  if (selected) return WIDTH;
  return 0;

  return ROW_WIDTH;
};

export default function DiseaseTagsFilter({
  name,
  setShowModal,
  showModal,
}: {
  name: string;
  showModal: boolean;
  setShowModal: (v: boolean) => void;
}) {
  const [selectedContainerIndex, setselectedContainerIndex] = useState(0);

  const { data: healthProfileOptions } =
    trpc.lib.healthProfileOptions.useQuery();

  const [selectedDisease, setSelectedDisease] = useState<SelectedDisease>(
    "" as SelectedDisease
  );
  const [diagnosisIndex, setDiagnosisIndex] = useState<number>();
  const methods = useFormContext();

  const selectedTags = methods.watch(name);

  const onBreadCrumbSelect = (tag: string, idx: number) => {
    methods.setValue(
      name,
      selectedTags.filter((_, i) => i <= idx)
    );
    setselectedContainerIndex(idx);
  };

  if (!healthProfileOptions) return null;
  return (
    <>
      <Modal
        animationType="fade"
        transparent
        visible={showModal}
        onRequestClose={() => setShowModal(false)}
      >
        <View className="flex flex-1 justify-end bg-black/20">
          <Pressable
            actionTag="select disease tags"
            className="flex-1"
            onPress={() => setShowModal(false)}
          />
          {/* Content */}
          <View className="flex-[3] bg-white rounded-t-2xl z-10">
            <AppText className="font-montserratSemiBold text-primary-dark m-4">
              Select Tags
            </AppText>
            <Divider className="border-neutral-300" />

            <View className="flex flex-row gap-2 mt-2 ml-2 flex-wrap">
              {selectedTags?.filter(Boolean)?.map((tag, idx) => (
                <Pressable
                  className="items-center flex-row"
                  key={tag}
                  onPress={() => onBreadCrumbSelect(tag, idx)}
                >
                  <AppText className="text-sm font-montserratMedium">
                    {tag}{" "}
                  </AppText>
                  {idx < selectedTags.length - 1 && (
                    <ChevronRight
                      color={theme.accent}
                      style={{ fontSize: 24 }}
                    />
                  )}
                </Pressable>
              ))}

              {!selectedTags?.filter(Boolean)?.length && (
                <AppText className="font-montserratSemiBold text-primary">
                  Select tags to apply filter
                </AppText>
              )}
            </View>

            {!!selectedTags.length && (
              <Divider className="border-neutral-300 mt-2" />
            )}

            {/* Filter Content */}
            <Controller
              name={name}
              render={({ field: { value, onChange } }) => {
                const [disease, diagnosis, subDiagnosis] = (value as
                  | string[]
                  | undefined) ?? [undefined, undefined, undefined];

                const { diseaseTypes, diagnosisTypes } = healthProfileOptions;

                return (
                  <View className="flex-row flex-1 divide-x divide-neutral-300">
                    <Animated.View
                      style={{
                        width: widthSelector(selectedContainerIndex === 0, 0),
                      }}
                      layout={springAnim}
                    >
                      <FlatList
                        data={Object.entries(diseaseTypes)}
                        keyExtractor={([key, value]) => value}
                        renderItem={({ item: [key, value] }) => {
                          return (
                            <Pressable
                              key={key}
                              className={clsx(
                                "p-3",
                                disease === value &&
                                  "border-l-accent border-l-2"
                              )}
                              onPress={() => {
                                onChange([value]);
                                setSelectedDisease(key as SelectedDisease);
                                setDiagnosisIndex(undefined);
                                setselectedContainerIndex(1);
                              }}
                            >
                              <AppText numberOfLines={1}>{value}</AppText>
                            </Pressable>
                          );
                        }}
                      />
                    </Animated.View>
                    <Animated.View
                      style={{
                        width: widthSelector(selectedContainerIndex === 1, 1),
                      }}
                      layout={springAnim}
                    >
                      <FlatList
                        data={
                          (disease &&
                            selectedDisease &&
                            diagnosisTypes[selectedDisease]) ||
                          []
                        }
                        keyExtractor={({ name }) => name}
                        renderItem={({ item: { name }, index: idx }) => {
                          return (
                            <Pressable
                              className={clsx(
                                "p-3",
                                diagnosis === name &&
                                  "border-l-accent border-l-2"
                              )}
                              onPress={() => {
                                onChange([disease, name]);
                                setDiagnosisIndex(idx);
                                setselectedContainerIndex(2);
                              }}
                            >
                              <AppText numberOfLines={3}>{name}</AppText>
                            </Pressable>
                          );
                        }}
                      />
                    </Animated.View>
                    <Animated.View
                      style={{
                        width: widthSelector(selectedContainerIndex === 2, 2),
                      }}
                      layout={springAnim}
                    >
                      <FlatList
                        data={
                          (disease &&
                            selectedDisease &&
                            diagnosisIndex !== undefined &&
                            diagnosisTypes[selectedDisease][diagnosisIndex]
                              .subTypes) ||
                          []
                        }
                        keyExtractor={(_) => _}
                        renderItem={({ item }) => {
                          return (
                            <Pressable
                              onPress={() => {
                                onChange([disease, diagnosis, item]);
                              }}
                              className={clsx(
                                "p-3",
                                subDiagnosis === item &&
                                  "border-l-accent border-l-2"
                              )}
                            >
                              <AppText numberOfLines={3}>{item}</AppText>
                            </Pressable>
                          );
                        }}
                      />
                    </Animated.View>
                  </View>
                );
              }}
            />

            {/* Footer */}
            <View
              className="flex-row p-4 border-t border-neutral-300"
              style={{ gap: 8 }}
            >
              <AppButton
                title="Reset"
                onPress={() => {
                  setselectedContainerIndex(0);
                  methods.setValue(name, []);
                }}
              />
              <AppButton
                title="Apply"
                variant="accent"
                onPress={() => {
                  setShowModal(false);
                }}
              />
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}
