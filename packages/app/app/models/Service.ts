export interface Category {
  _id?: string;
  title: string;
  description: string;
  logo: string;
}

export interface ServiceCategory {
  categories: Category[];
}

export interface StateFilterOptions {
  id: string;
  name: string;
}

export interface FilterOptions {
  states: StateFilterOptions[];
}

export interface ServiceListing {
  _id: string;
  organization: string;
  description: string;
  date: string;
  category: string;
  location: null;
  mobilePhone: string;
  link: string;
  contact: string;
  title: string;
  address: null;
  zipCode: null;
  recurring: null;
  email: string;
  subCategory: string;
  state: string;
  nationalFlag: null;
  targeting: {
    id: string;
    serviceEventId: string;
    diseases: [];
    stages: [];
    roles: [];
    phases: [];
    hrStatuses: [];
    her2Statuses: [];
  };
  logo: string;
}
export interface ServiceListings {
  filter: {
    state: string;
    category: string;
  };
  items: ServiceListing[];
}
