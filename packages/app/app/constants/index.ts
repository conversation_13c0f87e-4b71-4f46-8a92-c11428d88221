import { Platform } from "react-native";

export const localhost =
  process.env.EXPO_PUBLIC_IPV4_ADDRESS ||
  (Platform.OS === "android" ? "********" : "127.0.0.1");

interface EnvConfig {
  EXPO_PUBLIC_API_URL: string;
  EXPO_PUBLIC_S3_BUCKET_URL: string;
  EXPO_PUBLIC_COUNTRY_CODE: string;
  EXPO_PUBLIC_MIXPANEL: string;
  EXPO_PUBLIC_IMAGEKIT_PUBLIC_KEY: string;
  STORE_URL: string;
  EXPO_PUBLIC_RABBLE_INV_BASE_URL: string;
  EXPO_PUBLIC_MIXPANEL_V2?: string;
  EXPO_PUBLIC_MIXPANEL_PROJECT_ID: string;
  EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_USERNAME: string;
  EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_PASSWORD: string;
  EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_AUTH_HEADER: string;
}

export const ENV: EnvConfig = {
  EXPO_PUBLIC_RABBLE_INV_BASE_URL:
    process.env.EXPO_PUBLIC_RABBLE_INV_BASE_URL ||
    "https://dev-admin.getrabble.co",
  EXPO_PUBLIC_API_URL:
    process.env.EXPO_PUBLIC_API_URL || `http://${localhost}:8080/api/v1`,

  EXPO_PUBLIC_COUNTRY_CODE: process.env.EXPO_PUBLIC_COUNTRY_CODE || "+1",

  EXPO_PUBLIC_MIXPANEL:
    process.env.EXPO_PUBLIC_MIXPANEL || "05e7ba37828e82ce320242acbf5a0902",

  EXPO_PUBLIC_MIXPANEL_V2:
    process.env.EXPO_PUBLIC_MIXPANEL_V2 || "a8c8cadc9f1d25834b5d0b44c58fcbbd",

  EXPO_PUBLIC_MIXPANEL_PROJECT_ID:
    process.env.EXPO_PUBLIC_MIXPANEL_PROJECT_ID || "3647872",
  EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_USERNAME:
    process.env.EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_USERNAME ||
    "mixpanel-api.131338.mp-service-account",
  EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_PASSWORD:
    process.env.EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_PASSWORD ||
    "imn71rlbbTY6KtB1mQIJMjMvnAjRcbQI",
  EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_AUTH_HEADER:
    process.env.EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_AUTH_HEADER ||
    "Basic bWl4cGFuZWwtYXBpLjEzMTMzOC5tcC1zZXJ2aWNlLWFjY291bnQ6aW1uNzFybGJiVFk2S3RCMW1RSUpNak12bkFqUmNiUUk=",

  EXPO_PUBLIC_S3_BUCKET_URL:
    process.env.EXPO_PUBLIC_S3_BUCKET_URL ||
    "https://rabblehealth-v2.s3.us-east-2.amazonaws.com/",
  STORE_URL:
    Platform.OS === "android"
      ? "https://play.google.com/store/apps/details?id=com.rabblehealth.app"
      : "https://apps.apple.com/in/app/myrabble/id6463632712",
  EXPO_PUBLIC_IMAGEKIT_PUBLIC_KEY:
    process.env.EXPO_PUBLIC_IMAGEKIT_PUBLIC_KEY || "public_cM9qonChJZG",
};

export const MIXPANEL_TOKEN = ENV.EXPO_PUBLIC_MIXPANEL;
export const MIXPANEL_TOKEN_V2 = ENV.EXPO_PUBLIC_MIXPANEL_V2;
export const PROJECTID = ENV;

export default {
  countryCode: ENV.EXPO_PUBLIC_COUNTRY_CODE,
  bucketBaseUrl: ENV.EXPO_PUBLIC_S3_BUCKET_URL,
};

export const config = {
  BREAST_CANCER_LABEL: "breast",
};

export const s3Paths = {
  post: "posts/",
  profilePicture: "avatar/",
};
