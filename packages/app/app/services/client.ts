import { ENV } from "@constants/index";
import axios from "axios";

const baseURL = ENV.EXPO_PUBLIC_API_URL;
export const client = axios.create({
  baseURL,
});

// intercept clients
// interceptAuthHeaderToAxiosInstance(client, "x-auth-token");

export default client;

export const multipartHeader = { "Content-Type": "multipart/form-data" };

// Add a response interceptor
client.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Log error here
    console.error("Request failed:", error.response.data);
    return Promise.reject(error);
  }
);
