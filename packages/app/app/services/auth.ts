import { useMutation } from "react-query";

import client, { connectClient } from "./client";
import errorHandler from "@utils/errorhandler";
import { User } from "@models/User";
import constants, { MIXPANEL_TOKEN } from "@constants/index";

interface CreateUserResponse {
  metadata: {
    createdAt: string;
    updatedAt: string;
    createdBy: null;
    updatedBy: null;
  };
  id: number;
  fullName: string;
  firstName: string;
  lastName: string;
  email: string | null;
  title: string | null;
  corporatePhone: string | null;
  mobilePhone: string | null;
  enabled: boolean;
  expired: boolean;
  credentialsExpired: boolean;
  locked: boolean;
  expirationDate: string | null;
  credentialsExpirationDate: string | null;
  useMultiFactorAuth: boolean;
  latitude: string | null;
  longitude: string | null;
  birthDate: string | null;
  avatar: string | null;
  race: string | null;
  sex: string | null;
  pronounce: string | null;
  isPublic: boolean;
  dontSendRegistrationEmail: boolean;
  passwordPlain: string | null;
  userAlias: string | null;
  organizationAuthor: string | null;
  roles: {
    id: number;
    name: "PATIENT";
  }[];
  roleNames: ["PATIENT"];
  token: string;
  permissions: null;
  userName: string;
  consents: {
    id: number;
    name: string;
    description: string;
  }[];
}

interface CreateUserByEmailParams {
  email: string;
  firstName: string;
  lastName: string;
  passwordPlain: string;
  signupRole: "PATIENT";
  userName: string;
}

export default function useCreateUser() {
  return useMutation({
    mutationKey: "create-user",
    async mutationFn(body: CreateUserByEmailParams) {
      try {
        const {
          data: {
            firstName,
            id,
            lastName,
            roleNames,
            token,
            userName,
            email,
            corporatePhone,
            consents,
          },
        } = await client.post<CreateUserResponse>("/signup", body);

        const user: User = {
          firstName,
          id,
          lastName,
          roleNames,
          token,
          userName,
          email,
          phoneNumber: corporatePhone,
          consents,
        };

        user.connectToken = await loginConnectAndFetchToken({
          externalId: id.toString(),
          firstname: firstName,
          lastname: lastName,
          username: userName,
        });

        return user;
      } catch (ex) {
        errorHandler(ex);
      }
    },
  });
}

interface CreateOtpFromMobileNumberParams {
  phoneOrEmail: string;
  type: "email" | "phone";
}

export function useRequestOtp() {
  return useMutation((params: CreateOtpFromMobileNumberParams) => {
    const { phoneOrEmail, type } = params;

    return client.get(
      `/signup/${
        type === "email"
          ? `email/${phoneOrEmail}`
          : `requestOtp/${constants.countryCode}${phoneOrEmail}`
      }`
    );
  });
}

export function useRequestLoginOtp() {
  return useMutation((params: CreateOtpFromMobileNumberParams) => {
    const { phoneOrEmail, type } = params;

    return client.get(
      `/login/${
        type === "email"
          ? `email/${phoneOrEmail}`
          : `requestOtp/${constants.countryCode}${phoneOrEmail}`
      }`
    );
  });
}

interface LoginFromMobileNumberParams {
  phoneOrEmail: string;
  type: "email" | "phone";
}

export function useLoginOtp() {
  return useMutation((params: LoginFromMobileNumberParams) => {
    const { phoneOrEmail, type } = params;

    return client.get(
      `/login/${
        type === "email"
          ? `email/${phoneOrEmail}`
          : `requestOtp/${constants.countryCode}${phoneOrEmail}`
      }`
    );
  });
}

interface validateOtpParams {
  emailOrphoneNo: string;
  otp: string;
}

export function useValidateOtp() {
  return useMutation({
    mutationKey: "validate-otp",
    async mutationFn(body: validateOtpParams) {
      try {
        return await client.post<CreateUserResponse>(
          "/login/validateOtp",
          body
        );
      } catch (ex) {
        return errorHandler(ex);
      }
    },
  });
}

interface CreateUserByMobileParams {
  otp: string;
  phoneNo: string;
  firstName: string;
  lastName: string;
  signupRole: "PATIENT";
  userName: string;
}

export function useCreateUserwithMobileNumber() {
  return useMutation({
    mutationKey: "create-user-mobile-number",
    async mutationFn(body: CreateUserByMobileParams) {
      try {
        const {
          data: {
            firstName,
            id,
            lastName,
            roleNames,
            token,
            userName,
            avatar,
            email,
            corporatePhone,
            consents,
          },
        } = await client.post<CreateUserResponse>("/signup/verifyOtp/", body);
        const user: User = {
          firstName,
          id,
          lastName,
          roleNames,
          token,
          userName,
          email,
          phoneNumber: corporatePhone,
          consents,
        };

        user.connectToken = await loginConnectAndFetchToken({
          externalId: id.toString(),
          firstname: firstName,
          lastname: lastName,
          username: userName,
        });

        return user;
      } catch (ex) {
        errorHandler(ex);
      }
    },
  });
}

interface CreateUserByMobileOrEmailParams {
  otp: string;
  emailOrphoneNo: string;
  firstName: string;
  lastName: string;
  signupRole: "PATIENT";
  userName: string;
  type: "email" | "phone";
  tncAccepted: boolean;
  privacyAccepted: boolean;
  marketingConsentAccepted: boolean;
  caregiverConsentAccepted: boolean;
}

export function useCreateUserwithEmailOrMobileNumber() {
  return useMutation({
    async mutationFn(body: CreateUserByMobileOrEmailParams) {
      const {
        data: {
          firstName,
          id,
          lastName,
          roleNames,
          token,
          userName,
          avatar,
          email,
          corporatePhone,
          consents,
        },
      } = await client.post<CreateUserResponse>("/signup/verifyOtp/", body);
      const user: User = {
        firstName,
        id,
        lastName,
        roleNames,
        token,
        userName,
        email,
        phoneNumber: corporatePhone,
        consents,
      };

      user.connectToken = await loginConnectAndFetchToken({
        externalId: id.toString(),
        firstname: firstName,
        lastname: lastName,
        username: userName,
      });
      return user;
    },
  });
}

interface LoginWithEmailParams {
  email: string;
  passwordPlain: string;
}

export interface LoginResponse {
  id: number;
  fullName: string | null;
  firstName: string;
  lastName: string;
  email: string | null;
  title: string | null;
  corporatePhone: string | null;
  mobilePhone: string | null;
  birthDate: Date | null;
  race: string | null;
  sex: string | null;
  pronounce: string | null;
  token: string;
  roleNames: ["PATIENT"];
  userName: string;
  avatar?: { downloadUrl: string };
  consents: [];
}

export function useLoginWithEmail() {
  return useMutation({
    mutationKey: "login-user",
    async mutationFn(body: LoginWithEmailParams) {
      try {
        const {
          data: {
            firstName,
            id,
            lastName,
            roleNames,
            token,
            userName,
            email,
            corporatePhone,
            avatar,
            consents,
          },
        } = await client.post<LoginResponse>("/signup/login", body);
        const user: User = {
          firstName,
          id,
          lastName,
          roleNames,
          token,
          userName,
          email,
          phoneNumber: corporatePhone,
          avatar: { downloadUrl: avatar?.downloadUrl },
          consents,
        };

        user.connectToken = await loginConnectAndFetchToken({
          externalId: id.toString(),
          firstname: firstName,
          lastname: lastName,
          username: userName,
        });

        return user;
      } catch (ex) {
        errorHandler(ex);
      }
    },
  });
}

interface LoginWithEmailOrPhoneParams {
  emailOrphoneNo: string;
  otp: string;
}

export function useLoginWithEmailOrPhone() {
  return useMutation({
    async mutationFn(body: LoginWithEmailOrPhoneParams) {
      try {
        const {
          data: {
            firstName,
            id,
            lastName,
            roleNames,
            token,
            userName,
            email,
            corporatePhone,
            mobilePhone,
            avatar,
            consents,
          },
        } = await client.post<LoginResponse>("/signup/login/otp", body);
        const user: User = {
          firstName,
          id,
          lastName,
          roleNames,
          token,
          userName,
          email,
          phoneNumber: mobilePhone || corporatePhone,
          avatar: { downloadUrl: avatar?.downloadUrl },
          consents,
        };

        user.connectToken = await loginConnectAndFetchToken({
          externalId: id.toString(),
          firstname: firstName,
          lastname: lastName,
          username: userName,
        });

        return user;
      } catch (ex) {
        errorHandler(ex);
      }
    },
  });
}

interface VerifyUserParams {
  userName: string;
}

interface VerifyuserNameStatusResponse {
  response: boolean;
}
export function useVerifyUserNameStatus() {
  return useMutation({
    mutationFn({ userName }: VerifyUserParams) {
      if (!userName) throw new Error("username must not be empty");
      return client.get<VerifyuserNameStatusResponse>(
        `/signup/verifyUserName/${userName}`
      );
    },
  });
}

interface LoginWithOtpParams {
  phoneNo: string;
  otp: string;
}

export function useLoginWithOtp() {
  return useMutation({
    mutationKey: "login-with-otp",
    async mutationFn(body: LoginWithOtpParams) {
      try {
        const {
          data: {
            firstName,
            id,
            lastName,
            roleNames,
            token,
            userName,
            email,
            corporatePhone,
            consents,
          },
        } = await client.post<LoginResponse>("/signup/login/otp", body);

        const user: User = {
          firstName,
          id,
          lastName,
          roleNames,
          token,
          userName,
          email,
          phoneNumber: corporatePhone,
          consents,
        };

        const connectToken = await loginConnectAndFetchToken({
          externalId: id.toString(),
          firstname: firstName,
          lastname: lastName,
          username: userName,
        });

        user.connectToken = connectToken;

        return user;
      } catch (ex) {
        errorHandler(ex);
      }
    },
  });
}

interface LoginConnectParams {
  externalId: string;
  email?: string | null;
  firstname: string;
  lastname: string;
  username: string;
  profilePicture?: string;
}

export async function loginConnectAndFetchToken(params: LoginConnectParams) {
  try {
    const { data } = await connectClient.post("/auth/login", params);

    return data?.token as string;
  } catch (ex) {
    errorHandler(ex);
  }
}
