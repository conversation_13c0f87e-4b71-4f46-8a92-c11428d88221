import errorHandler from "@utils/errorhandler";
import client, { connectClient } from "./client";
import {
  InfiniteData,
  QueryFunctionContext,
  useInfiniteQuery,
  useMutation,
  useQuery,
} from "react-query";
import { Pagination } from "@models/index";
import { Comment, Post, PostTypes } from "@models/Posts";
import { queryClient } from "@providers/RootProvider";
import usePersistedUser from "@hooks/persistUser";

const fetchPostsQueryKey = "fetch-posts";
interface CreatePostParams {
  caption?: string;
  image?: string;
  postType: PostTypes;
  rabbleGroup?: string;
}

function createPost({
  caption,
  image,
  postType,
  rabbleGroup,
}: CreatePostParams) {
  if (caption || image) {
    const formdata = new FormData();

    if (caption) formdata.append("title", caption);

    if (image && typeof image === "string") {
      const uri = image;
      formdata.append("image", {
        uri,
        type: "image/jpeg",
        name: "image.jpeg",
      } as any);
    }

    formdata.append("postType", postType);
    if (rabbleGroup) {
      formdata.append("rabbleGroup", rabbleGroup);
    }

    return connectClient.post("/post", formdata, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  }
}

interface FetchPostsApiResponse {
  posts: Post[];
  pagination: Pagination;
}

const fetchPosts = async ({
  pageParam = 0,
  postId,
  rabbleGroup,
  postType,
}: QueryFunctionContext & {
  postId?: string;
  rabbleGroup?: string;
  postType?: PostTypes;
}) => {
  try {
    return (
      await connectClient.get<FetchPostsApiResponse>("/post", {
        params: {
          pageNumber: pageParam,
          pageSize: 30,
          postId,
          rabbleGroup,
          postType,
        },
      })
    ).data;
  } catch (error) {
    throw new Error("Error fetching posts");
  }
};

interface GetPostParams {
  postId?: string;
  rabbleGroup?: string;
  postType?: PostTypes;
}

export function useGetPosts(params?: GetPostParams) {
  const postId = params?.postId;
  const rabbleGroup = params?.rabbleGroup;
  const postType = params?.postType;

  return useInfiniteQuery(
    [fetchPostsQueryKey, postId, rabbleGroup, postType],
    (params) => {
      return fetchPosts({
        ...params,
        postId,
        ...(rabbleGroup && { rabbleGroup }),
        postType,
      });
    },
    {
      getNextPageParam({ pagination }) {
        return pagination.nextPage || undefined;
      },
    }
  );
}

export function useGetCommentsById(postId: string) {
  return useQuery(
    ["get-comments-by-post", postId],
    () => {
      try {
        return connectClient.get<Comment[]>(`/comment/${postId}`);
      } catch (ex) {
        errorHandler(ex);
      }
    },
    {
      select(response) {
        if (response) return response.data;
        return response;
      },
    }
  );
}

interface CreateCommentParams {
  postId: string;
  comment: string;
}

export function useCreateComment() {
  return useMutation(
    ({ postId, comment }: CreateCommentParams) =>
      connectClient.post<Comment>(`/comment/${postId}`, { comment }),
    {
      onSettled(data) {
        queryClient.invalidateQueries([
          "get-comments-by-post",
          data?.data.post,
        ]);
      },
    }
  );
}

interface LikePostParams {
  status: boolean;
  postId: string;
  rabbleGroup?: string;
  postType?: PostTypes;
}

export function useLikePost() {
  const { persistedUser } = usePersistedUser();
  const { data: user } = persistedUser;

  return useMutation({
    mutationKey: "like-post",
    mutationFn: ({ postId, status, postType, rabbleGroup }: LikePostParams) => {
      const prevPostsKey = [fetchPostsQueryKey, null, rabbleGroup, postType];

      queryClient.cancelQueries(prevPostsKey);

      const prevPosts = queryClient.getQueryData(prevPostsKey) as
        | InfiniteData<FetchPostsApiResponse>
        | undefined;

      let pageIdx = -1;
      let postIndex = -1;

      prevPosts?.pages.forEach((page, _pageIdx) => {
        page.posts.forEach((post, _postIdx) => {
          if (post._id === postId) {
            pageIdx = _pageIdx;
            postIndex = _postIdx;
            return true;
          }
          return false;
        });
      });

      // @ts-ignore
      const updated: InfiniteData<FetchPostsApiResponse> | undefined = {
        ...prevPosts,
        pages:
          prevPosts?.pages.map((page, _pageIdx) => {
            if (_pageIdx === pageIdx) {
              return {
                ...page,
                posts: page.posts.map((post, _postIdx) => {
                  let recentLikes = [...post.recentLikes];
                  if (post.isLiked) {
                    recentLikes = recentLikes.filter((_, idx) => !!idx);
                  } else {
                    recentLikes = [
                      {
                        _id: Date.now().toString(),
                        createdAt: new Date().toISOString(),
                        post: postId,
                        userDocument: [
                          {
                            _id: "",
                            createdAt: "",
                            email: "",
                            externalId: "1",
                            firstname: "",
                            lastname: "",
                            username: user?.userName!,
                            profilePicture: user?.avatar?.downloadUrl,
                          },
                        ],
                      },
                      ...post.recentLikes,
                    ];
                  }
                  if (_postIdx === postIndex) {
                    return {
                      ...post,
                      isLiked: !post.isLiked,
                      recentLikes,
                    };
                  }
                  return post;
                }),
              };
            }
            return page;
          }) || [],
      };

      if (pageIdx !== -1 && postIndex !== -1) {
        queryClient.setQueryData(prevPostsKey, updated);
      }

      return connectClient.put(`/like/${postId}`, { status });
    },
    onSettled() {
      queryClient.invalidateQueries([fetchPostsQueryKey], { fetching: false });
    },
  });
}

export default function useCreatePost() {
  return useMutation(
    async (body: CreatePostParams) => {
      return createPost(body);
    },
    {
      onSettled() {
        queryClient.invalidateQueries({
          queryKey: fetchPostsQueryKey,
          exact: false,
        });
      },
    }
  );
}

interface ReportPostParams {
  postId: string;
}

export function useReportPost() {
  return useMutation(
    ({ postId }: ReportPostParams) =>
      connectClient.post(`/post/report/${postId}`),
    {
      onSettled() {
        queryClient.invalidateQueries("report-post");
      },
    }
  );
}

//TODO: Invalidate Get posts//
export function useDeletePost() {
  return useMutation(
    ({ postId }: ReportPostParams) => connectClient.delete(`/post/${postId}`),
    {
      onSettled() {
        queryClient.invalidateQueries("delete-post");
      },
    }
  );
}

interface ReportCommentParams {
  commentId: string;
}

export function useReportComment() {
  return useMutation(
    ({ commentId }: ReportCommentParams) =>
      connectClient.post(`/comment/report/${commentId}`),
    {
      onSettled() {
        queryClient.invalidateQueries("report-comment");
      },
    }
  );
}

//TODO: Invalidate Get posts//
export function useDeleteComment() {
  return useMutation(
    ({ commentId }: ReportCommentParams) =>
      connectClient.delete(`/comment/${commentId}`),
    {
      onSettled() {
        queryClient.invalidateQueries("delete-comment");
      },
    }
  );
}

interface LikeCommentParams {
  commentId: string;
  status: boolean;
  postId: string;
}

export function useLikeComment() {
  return useMutation(
    ({ commentId, status }: LikeCommentParams) =>
      connectClient.put(`/like-comment/${commentId}`, { status }),
    {
      onSettled() {
        queryClient.invalidateQueries("get-comments-by-post");
      },
      async onMutate({ commentId, postId, status }) {
        await queryClient.cancelQueries({
          queryKey: ["get-comments-by-post", postId],
        });
        const previousComments = queryClient.getQueryData<Comment[]>([
          "get-comments-by-post",
          postId,
        ]);
        queryClient.setQueryData<Comment[] | undefined>(
          ["get-comments-by-post", postId],
          (oldData) => {
            if (!oldData) return oldData;

            return {
              ...oldData,
              // @ts-ignore
              data: (oldData?.data || []).map((data: Comment) => {
                if (data._id === commentId) {
                  data.isLiked = status;
                }
                return data;
              }),
            };
          }
        );
        return { previousComments };
      },
    }
  );
}
