import { useMutation, useQuery } from "react-query";
import client, { multipartHeader } from "./client";
import AsyncStorage from "@utils/asyncStorage";
import { localUserKey } from "@hooks/persistUser";
import { User } from "@models/User";
import { LoginResponse, loginConnectAndFetchToken } from "./auth";
import errorHandler from "@utils/errorhandler";
import { queryClient } from "@providers/RootProvider";

interface UpdateProfilePayload {
  avatar?: {
    id: number;
  };
  birthDate?: string;
  corporatePhone?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  id: number;
  mobilePhone?: string;
  zipCode?: string;
  city?: string;
  race?: {
    id: number;
    name: string;
  };
  sex?: {
    id: number;
    name: string;
  };
  state?: {
    id: number;
    name: string;
  };
}

export async function updateProfile(body: UpdateProfilePayload) {
  try {
    const {
      data: {
        id,
        firstName,
        lastName,
        roleNames,
        userName,
        email,
        mobilePhone,
        avatar,
        token,
      },
    } = await client.post<LoginResponse>("/user-profile/update", body);

    const currentUser = await AsyncStorage.retrieveData<User>(localUserKey);

    const user: User = {
      ...currentUser,
      firstName,
      id,
      lastName,
      roleNames,
      userName,
      email,
      phoneNumber: mobilePhone,
      token: token,
      avatar: { downloadUrl: avatar?.downloadUrl },
    };

    return user;
  } catch (ex) {
    errorHandler(ex);
  }
}

interface UpdateProfileResonse {
  fileType: string;
  fileName: string;
  documentUid: string;
  fileSize: number;
  downloadUrl: string;
  id: number;
}
async function updateProfileImage({
  body,
  userId,
}: {
  body: globalThis.FormData;
  userId: number;
}) {
  const {
    data: { id, downloadUrl },
  } = await client.post<UpdateProfileResonse>(
    "/documents/profile/avatar",
    body,
    { headers: multipartHeader }
  );

  // update the profile with the profile image id
  await updateProfile({
    avatar: { id },
    id: userId,
  });

  const profilePictureUrl = downloadUrl;
  // update the user object
  let user = await AsyncStorage.retrieveData<User>(localUserKey);

  if (user) user = { ...user, avatar: { downloadUrl: profilePictureUrl } };

  if (user)
    user.connectToken = await loginConnectAndFetchToken({
      externalId: user?.id.toString(),
      profilePicture: profilePictureUrl,
      firstname: user?.firstName,
      lastname: user?.lastName,
      username: user?.userName as string,
    });
  await AsyncStorage.storeData(localUserKey, user);
  // user?.avatar =
}

export function useUpdateProfile() {
  return useMutation(updateProfile, {
    async onSettled(user) {
      if (user) {
        await AsyncStorage.storeData(localUserKey, user);
        queryClient.cancelQueries();
        queryClient.invalidateQueries();
      }
    },
  });
}

export default function useUpdateProfileImage() {
  return useMutation(updateProfileImage, {
    onSettled() {
      queryClient.invalidateQueries(localUserKey);
    },
  });
}

// HEALTH PROFILE
interface HealthProfileOptions {
  patientBiopsyReports: {
    id: number;
    name: string;
  }[];
  hrStatuses: {
    id: number;
    name: string;
  }[];
  patientProfileRoles: {
    id: number;
    name: string;
  }[];
  phases: {
    id: number;
    name: string;
  }[];
  diseases: {
    id: number;
    name: string;
    diseaseTitle: {
      id: number;
      diseaseTitleName: string;
    }[];
  }[];
  diseaseStages: {
    id: number;
    name: string;
  }[];
  patientLearnAbout: {
    id: number;
    name: string;
  }[];
  her2Statuses: {
    id: number;
    name: string;
  }[];
}

export function useGetHealthProfileOptions() {
  return useQuery(
    "health-profile-options",
    () => client.get<HealthProfileOptions>("/patient-profile/options"),
    {
      select({ data }) {
        return data;
      },
    }
  );
}

interface PropfileOption {
  id: number;
  name: string;
}

interface DiagnosisOption {
  id: number;
  name: string;
  diseaseTitle?: [
    {
      id: number;
      diseaseTitleName: string;
    }
  ];
}

interface PatientProfileResponse {
  id: number;
  patientId: number;
  patientProfileHer2Statuses: PropfileOption[] | null;
  her2Statuses: PropfileOption | null;
  diagnosis: DiagnosisOption | null;
  stage: PropfileOption | null;
  hrStatus: PropfileOption | null;
  her2Status: PropfileOption | null;
  phase: null;
  patientProfileRoles: PropfileOption | null;
  patientLearnAbout: PropfileOption | null;
  patientBiopsyReport: PropfileOption | null;
  diagnoseDate: null | string;
  healthInsurance: PropfileOption | null;
  helpOnPersonalisedExperience?: boolean;
}

export function useGetPatientProfileByUserIdMutation() {
  return useMutation(({ userId }: { userId: number }) =>
    client.get<PatientProfileResponse>(`/patient-profile/self/${userId}`)
  );
}

const getUserProfileId = "use-get-patient-profile";
export function useGetPatientProfileByUserId(
  userId: number | undefined | null
) {
  return useQuery(
    getUserProfileId,
    () => client.get<PatientProfileResponse>(`/patient-profile/self/${userId}`),
    {
      enabled: !!userId,
      select({ data }) {
        return data;
      },
    }
  );
}

interface PatientUpdateProfileParams {
  id: number;
  patientId: number;
  diagnoseDate?: string;
  diagnosis?: {
    id: number;
    diseaseTitle?: [
      {
        id: number;
      }
    ];
  };
  patientProfileHer2Statuses?: {
    id: number;
  }[];
  hrStatus?: {
    id: number;
  };
  stage?: {
    id: number;
  };
  patientBiopsyReport?: {
    id: number;
  };
  patientLearnAbout?: {
    id: number;
  };
  patientProfileRoles?: {
    id: number;
  };
  helpOnPersonalisedExperience: boolean;
}

export function useUpdateHealthProfile() {
  return useMutation(
    (body: PatientUpdateProfileParams) =>
      client.put("/patient-profile/v1", body),
    {
      onSettled() {
        queryClient.invalidateQueries(getUserProfileId);
      },
    }
  );
}

interface UserProfileResponse {
  id: number;
  fullName: string;
  firstName: string;
  lastName: string;
  email: string;
  title: null;
  corporatePhone: string;
  mobilePhone: string;
  birthDate: string;
  // avatar: {
  //   downloadUrl: "https://rabblehealth-v2.s3.us-east-2.amazonaws.com/avatar/8320d38b-1699-4600-b9af-cc1c4144d58a.jpg";
  // };
  race: PropfileOption;
  sex: PropfileOption;
  userName: string;
  state: PropfileOption;
  zipCode: string;
  city: string;
  roles: PropfileOption[];
  roleNames: ["PATIENT"];
}

export function useGetUserProfile(userId?: number) {
  return useQuery(
    "get-user-profile",
    () => client.get<UserProfileResponse>(`/user-profile/self/${userId}`),
    {
      select({ data }) {
        return data;
      },
      enabled: !!userId,
    }
  );
}

// interface UpdateProfilePayload {
//   patientId: number;
//   fullName: string;
//   email?: string;
//   mobilePhone?: string;
//   zipCode?: string;
//   race?: {
//     id: number;
//     name: string;
//   };
//   sex?: {
//     id: number;
//     name: string;
//   };
// }

// export async function createPatientProfile(body: ) {
//   try {
//     const {
//       data: {
//         patientId,
//         fullName,
//         email,
//         mobilePhone,
//         zipCode,
//         race,
//         sex,
//       },
//     } = await client.post<LoginResponse>("/patient-profile/v1", body);

//     const currentUser = await AsyncStorage.retrieveData<User>(userLocalKey);

//     const user: User = {
//       ...currentUser,
//       firstName,
//       id,
//       lastName,
//       roleNames,
//       userName,
//       email,
//       phoneNumber: mobilePhone,
//       token: token,
//       avatar: { downloadUrl: avatar?.downloadUrl },
//     };

//     return user;
//   } catch (ex) {
//     errorHandler(ex);
//   }
// }
