import client from "./client";
import { useQuery } from "react-query";
import {
  FilterOptions,
  ServiceCategory,
  ServiceListing,
  ServiceListings,
} from "@models/Service";
import { queryClient } from "@providers/RootProvider";
import errorHandler from "@utils/errorhandler";

const fetchServiceCategoryQueryKey = "fetch-service-categories";
const filterOptionsQueryKey = "filter-options";

export default function useGetServiceCategories() {
  return useQuery(
    fetchServiceCategoryQueryKey,
    () => client.get<ServiceCategory>("/admin/service-events/categories"),
    {
      select({ data }) {
        return data;
      },
    }
  );
}

export function useGetStateFilterOptions() {
  return useQuery(
    filterOptionsQueryKey,
    () => client.get<FilterOptions>("/admin/service-events/options"),
    {
      select({ data }) {
        return data;
      },
    }
  );
}

interface ServiceListingParams {
  state?: string;
  category?: string;
}

export function useGetServiceListings({
  category,
  state,
}: ServiceListingParams) {
  return useQuery(
    ["get-service-listings", category, state],
    () =>
      client.post<ServiceListings>("/admin/service-events/filter", {
        filter: {
          excludeIds: [0],
          ...(state && { state }),
          ...(category && { category }),
        },
        page: 0,
        size: 999,
      }),
    {
      select({ data }) {
        return data;
      },
    }
  );
}

export function useGetServiceDetails(serviceListingId: string) {
  return useQuery(
    ["get-service-details", serviceListingId],
    () => {
      try {
        return client.get<ServiceListing>(
          `/admin/service-events/${serviceListingId}`
        );
      } catch (ex) {
        errorHandler(ex);
      }
    },
    {
      select(response) {
        if (response) return response.data;
        return response;
      },
    }
  );
}
