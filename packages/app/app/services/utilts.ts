import { useQuery } from "react-query";
import client from "./client";

interface ProfileOptionsResponse {
  gender: {
    id: number;
    name: string;
  }[];
  pronounce: {
    id: number;
    name: string;
  }[];
  race: {
    id: number;
    name: string;
  }[];
}

export default function useGetProfileOptions() {
  return useQuery(
    "profile-options",
    () => client.get<ProfileOptionsResponse>("/user-profile/options"),
    {
      select({ data }) {
        return data;
      },
    }
  );
}
