/**
 * Utility functions for tag management in the mobile app
 */

export interface Tag {
  _id: string;
  tag: string;
  scope?: string[];
}

/**
 * Filters tags based on search text (case-insensitive)
 */
export const filterTagsBySearch = (tags: Tag[], searchText: string): Tag[] => {
  if (!searchText.trim()) return tags;

  const search = searchText.toLowerCase();
  return tags.filter((tag) => tag.tag.toLowerCase().includes(search));
};

/**
 * Checks if a tag name already exists in the tags array
 */
export const tagExists = (tags: Tag[], tagName: string): boolean => {
  return tags.some((tag) => tag.tag.toLowerCase() === tagName.toLowerCase());
};

/**
 * Converts tag objects to simple string array (for form values)
 */
export const tagsToStringArray = (tags: Tag[]): string[] => {
  return tags.map((tag) => tag.tag);
};

/**
 * Validates tag name format
 */
export const isValidTagName = (tagName: string): boolean => {
  const trimmed = tagName.trim();
  return trimmed.length > 0 && trimmed.length <= 50 && !/[<>]/.test(trimmed);
};

/**
 * Sanitizes tag name by trimming and removing invalid characters
 */
export const sanitizeTagName = (tagName: string): string => {
  return tagName.trim().replace(/[<>]/g, "");
};

/**
 * Groups tags by their scope
 */
export const groupTagsByScope = (tags: Tag[]): Record<string, Tag[]> => {
  const grouped: Record<string, Tag[]> = {};

  tags.forEach((tag) => {
    if (tag.scope && tag.scope.length > 0) {
      tag.scope.forEach((scope) => {
        if (!grouped[scope]) {
          grouped[scope] = [];
        }
        grouped[scope].push(tag);
      });
    } else {
      if (!grouped["unscoped"]) {
        grouped["unscoped"] = [];
      }
      grouped["unscoped"].push(tag);
    }
  });

  return grouped;
};

/**
 * Gets unique scope values from tags array
 */
export const getUniqueScopeValues = (tags: Tag[]): string[] => {
  const scopes = new Set<string>();

  tags.forEach((tag) => {
    if (tag.scope && tag.scope.length > 0) {
      tag.scope.forEach((scope) => scopes.add(scope));
    }
  });

  return Array.from(scopes).sort();
};

/**
 * Default scope for rabble group tags
 */
export const RABBLE_GROUP_TAG_SCOPE = ["user-created"];

/**
 * Creates a new tag object with default scope for rabble groups
 */
export const createRabbleGroupTag = (tagName: string): Omit<Tag, "_id"> => {
  return {
    tag: sanitizeTagName(tagName),
    scope: RABBLE_GROUP_TAG_SCOPE,
  };
};
