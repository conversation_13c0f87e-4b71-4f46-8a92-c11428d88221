import ExpoAsyncStorage from "@react-native-async-storage/async-storage";

class AsyncStorage {
  private static PREFIX = "RABBLE-";

  // store data in AsyncStorage
  static async storeData<T>(key: string, data: T): Promise<void> {
    try {
      const jsonData = JSON.stringify(data);
      return await ExpoAsyncStorage.setItem(this.PREFIX + key, jsonData);
    } catch (error) {
      console.error("Error storing data:", error);
    }
  }

  // retrieve data from AsyncStorage
  static async retrieveData<T>(key: string): Promise<T | null> {
    try {
      const jsonData = await ExpoAsyncStorage.getItem(this.PREFIX + key);
      return jsonData ? JSON.parse(jsonData) : null;
    } catch (error) {
      console.error("Error retrieving data:", error);
      return null;
    }
  }

  // remove data from AsyncStorage
  static async removeData(key: string): Promise<void> {
    try {
      await ExpoAsyncStorage.removeItem(this.PREFIX + key);
    } catch (error) {
      console.error("Error removing data:", error);
    }
  }

  // clear all data from AsyncStorage
  static async clearAllData(): Promise<void> {
    try {
      await ExpoAsyncStorage.clear();
    } catch (error) {
      console.error("Error clearing data:", error);
    }
  }
}

export default AsyncStorage;
