function isUpdatedAtToday(updatedAt: Date | string): boolean {
 // Parse the updatedAt string into a Date object
  const updatedAtDate = new Date(updatedAt);

  // Get today's date
  const today = new Date();

  // Compare year, month, and day
  return (
    updatedAtDate.getFullYear() === today.getFullYear() &&
    updatedAtDate.getMonth() === today.getMonth() &&
    updatedAtDate.getDate() === today.getDate()
  );
}

export default isUpdatedAtToday;

