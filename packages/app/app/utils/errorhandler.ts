import { TRPCClientError } from '@trpc/client';
import { AxiosError } from 'axios';
import * as Sentry from 'sentry-expo';

export default function errorHandler(err: unknown) {
  __DEV__ && console.log(err);
  let message = 'something went wrong';

  if (err instanceof Error) {
    Sentry.Native.captureException(err);
    if (err instanceof AxiosError) {
      __DEV__ && console.log(err.response?.data.message);
      message = err.response?.data?.message;
    }

    if (err instanceof TRPCClientError) {
      __DEV__ && console.log(err);
      message = err.message;
    }
  }

  return { message };
}
