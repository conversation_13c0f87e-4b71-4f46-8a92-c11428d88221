import { User } from "@models/User";
import { AxiosInstance } from "axios";
import * as yup from "yup";
import AsyncStorage from "./asyncStorage";
import { localUserKey } from "@hooks/persistUser";
import { Platform } from "react-native";
import constants, { ENV } from "@constants/index";
import { Options } from "@components/common/AppSelect";

export const emailRegex = /\S+@\S+\.\S+/;

export const formatStringToUsPhoneNumberFormat = (data: string) => {
  // Remove all non-digit characters from the string
  let digitsOnly = data;
  if (digitsOnly?.includes(constants.countryCode)) {
    digitsOnly = data.substring(constants.countryCode.length);
  }
  // Apply the desired formatting
  const formattedNumber = digitsOnly?.replace(
    /(\d{3})(\d{3})(\d{4})/,
    "($1) $2-$3"
  );

  return formattedNumber;
};

export const formatUsPhoneNumberToPlainString = (data: string) => {
  // Remove all non-digit characters from the formatted string
  let digitsOnly = data?.replace(/\D/g, "");
  if (digitsOnly?.includes(constants.countryCode)) {
    digitsOnly = digitsOnly.substring(constants.countryCode.length);
  }
  return digitsOnly;
};

export const US_PHONE_REGX =
  /^(?:\+?1[-.\s]?)?\(?(\d{3})\)?[-.\s]?(\d{3})[-.\s]?(\d{4})$/;

export const phoneSchema = yup
  .string()
  .test("is-valid-phone", "Invalid phone number", (value) => {
    if (!value) return true; // Allow empty value
    return US_PHONE_REGX.test(value);
  })
  .test("is-valid-length", "Phone number must be 10 digits", (value) => {
    if (!value) return true;
    const cleanedValue = value?.replace(/\D/g, ""); // Remove all non-numeric characters
    return cleanedValue.length === 10;
  });

export const selectOptionSchema = yup.object({
  label: yup.string().required("This is a required field"),
  value: yup.string().required("This is a required field"),
});

export const interceptAuthHeaderToAxiosInstance = (
  client: AxiosInstance,
  headerName: "Authorization" | "x-auth-token"
) => {
  client.interceptors.request.use(
    async (config) => {
      const user = await AsyncStorage.retrieveData<User | null | undefined>(
        localUserKey
      );

      if (user) {
        const token =
          headerName === "Authorization"
            ? `Bearer ${user.token}`
            : user.connectToken;

        config.headers[headerName] = token;
      }

      return config;
    },
    (err) => Promise.reject(err)
  );
};

export function concatCountryCodeToPhoneNumber(ph: string) {
  return ` ${ENV.EXPO_PUBLIC_COUNTRY_CODE}${ph}`;
}

export const selectSchema = yup
  .array()
  .min(1, "This is a required field")
  .of(
    yup
      .object({
        label: yup.string().required(),
        value: yup.mixed().required(),
      })
      .required()
  );

interface SelectSchemaDependencyValidationProps {
  schema: yup.Schema;
  fieldToTarget: string;
  fieldTargetValue: string;
  inverse?: boolean;
}

export const selectSchemaDependencyValidation = ({
  fieldTargetValue,
  fieldToTarget,
  schema,
  inverse,
}: SelectSchemaDependencyValidationProps) => {
  return schema.when(fieldToTarget, {
    is: (diagnosisValue: Options[]) => {
      const res = inverse
        ? diagnosisValue?.[0]?.label?.toLowerCase() !==
          fieldTargetValue.toLowerCase()
        : diagnosisValue?.[0]?.label?.toLowerCase() ===
          fieldTargetValue.toLowerCase();
      return res;
    },
    then: (schema) => schema.required("This is a required field"),
  });
};

export const pdfPreview = (pdfUrl: string) =>
  Platform.OS === "ios"
    ? pdfUrl
    : `http://docs.google.com/gview?url=${pdfUrl}&embedded=true`;

interface ImageKitParams {
  imagePath: string;
  /** transform is an array with strings
   * 1. width: w-100
   * 2. blur: bl-2
   */
  transform: string[];
}

export const imageKit = ({ imagePath, transform }: ImageKitParams) => {
  const mediaUrl = `https://ik.imagekit.io/rabblehealth${
    transform.length ? `/tr:${transform.join(",")}` : ""
  }/${imagePath}`;
  return mediaUrl;
};

export function getTimeDifference(createdAt: Date | undefined): string {
  const now = new Date();
  const createdDate = new Date(createdAt || now);

  const timeDifference = now.getTime() - createdDate.getTime();
  const seconds = Math.floor(timeDifference / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days >= 7) {
    // If more than a week, show the full date
    const month = createdDate.toLocaleString("default", { month: "short" });
    const day = createdDate.getDate();
    return `${month} ${day}`;
  } else if (hours >= 24) {
    // If more than a day, show the difference in days
    return `${days} d`;
  } else if (minutes >= 60) {
    // If more than an hour, show the difference in hours
    return `${hours} hr${hours > 1 ? "s" : ""}`;
  } else {
    // Otherwise, show the difference in minutes
    return `${minutes} min${minutes > 1 ? "s" : ""}`;
  }
}

export const formatDate = (date: Date) => {
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  }; // 12-hour format with am/pm
  const formattedTime = date.toLocaleString("en-US", timeOptions).toLowerCase();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Get month and pad with leading zero
  const day = String(date.getDate()).padStart(2, "0"); // Get day and pad with leading zero

  return `${formattedTime}, ${month}/${day}`;
};

interface RichTextSegment {
  text: string;
  isBold: boolean;
}

export function parseRichText(
  input: string
): Array<RichTextSegment[] | string> {
  // Split the input by line breaks
  const lines = input.split("\n");

  // Regular expression to match bold text
  const boldRegex = /\*\*(.*?)\*\*/g;

  // Initialize an array to hold the results
  let result: Array<RichTextSegment[] | string> = [];

  for (const line of lines) {
    // If the line is empty (new line), add it as a string
    if (line.trim() === "") {
      result.push("");
      continue;
    }

    const lineSegments: RichTextSegment[] = [];
    let currentIndex = 0;

    // Check for bold segments in the line
    let match;
    while ((match = boldRegex.exec(line)) !== null) {
      // Add text before the bold segment if there is any
      if (currentIndex < match.index) {
        lineSegments.push({
          text: line.substring(currentIndex, match.index).trim(),
          isBold: false,
        });
      }

      // Add the bold segment
      lineSegments.push({
        text: match[1].trim(),
        isBold: true,
      });

      // Update currentIndex to the end of the current match
      currentIndex = boldRegex.lastIndex;
    }

    // Add any remaining text after the last bold segment
    if (currentIndex < line.length) {
      lineSegments.push({
        text: line.substring(currentIndex).trim(),
        isBold: false,
      });
    }

    // Add the structured segments for this line
    result.push(lineSegments.filter((segment) => !!segment.text));
  }

  result = result.filter(Boolean);
  return result;
}
