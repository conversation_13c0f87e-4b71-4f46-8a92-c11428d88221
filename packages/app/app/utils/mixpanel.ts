import { <PERSON><PERSON><PERSON>, MIXPANEL_TOKEN, MIXPANEL_TOKEN_V2 } from "@constants/index";
import { rawTrpcClient } from "@providers/RootProvider";
import AsyncStorage from "@utils/asyncStorage";
import axios from "axios";
import type { NotificationTypes } from "../../../shared/enums/notification";

import { createOrReadAppInstanceId } from "@hooks/useAppInstanceId";
import _ from "lodash";
import type { JwtUser } from "../../../shared/types/user";

const service_account_username = "mixpanel-api.131338.mp-service-account";
const service_account_password = "imn71rlbbTY6KtB1mQIJMjMvnAjRcbQI";

// Define types for event-specific properties

interface CreateUserProfile {
  $token: string;
  $distinct_id: string | number;
  $set: {
    first_name: string;
    last_name: string;
    phone?: string;
    email?: string;
    signup_type: "email" | "mobile";
  };
}

interface PostCreatedEvent {
  caption?: string;
  username: string;
  createdAt: string;
  postId: string;
}

interface AppOpenEvent {
  firstname?: string;
  lastname?: string;
  email?: string | null;
  phone?: string | null;
  isGuestUser: boolean;
}

interface UtmLinkOpenEvent {
  utm_id?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  url: string;
}

interface ScreenTrackingEvent {
  screen: string;
  trackedAt: string;
}

interface LikePostEvent {
  username: string;
  createdAt: string;
  postId: string;
}

interface CommentPostEvent {
  username: string;
  createdAt: string;
  postId: string;
  comment: string;
}

interface ServiceEventDetail {
  serviceName: string;
  serviceDescription: string;
  email?: string;
  mobile?: string;
  website?: string;
  category?: string;
  state?: string;
}

interface NotificationAction {
  notificationType: NotificationTypes;
  content?: string;
  action: "accepted" | "rejected";
}
type ServiceEmailEvent = Record<string, string>;
type ServicePhoneNumberEvent = Record<string, string>;
type ServiceWebsiteEvent = Record<string, string>;
type Action = Record<string, string>;
type OnboardingAction = Record<string, string>;
interface MixpanelEventTypes {
  CREATE_POST: PostCreatedEvent;
  APP_OPEN: AppOpenEvent;
  SCREEN_TRACKING: ScreenTrackingEvent;
  LIKE_POST: LikePostEvent;
  COMMENT_POST: CommentPostEvent;
  SERVICE_DETAIL: ServiceEventDetail;
  NOTIFICATION_ACTION: NotificationAction;
  SERVICE_EMAIL_EVENT: ServiceEmailEvent;
  SERVICE_PHONE_EVENT: ServicePhoneNumberEvent;
  SERVICE_WEBSITE_EVENT: ServiceWebsiteEvent;
  ACTION: Action;
  ONBOARDING_ACTION: OnboardingAction;
  HOME_SCREEN_EVENT: Record<string, string>;
  LEARN_SCREEN_EVENT: Record<string, string>;
  RABBLE_SCREEN_EVENT: Record<string, string>;
  SERVICE_SCREEN_EVENT: Record<string, string>;
  APP_OPEN_FROM_DEEP_LINK: UtmLinkOpenEvent;
  APP_OPEN_FROM_DOWNLOAD_LINK: UtmLinkOpenEvent;
}

class MixpanelTracker {
  private token: string;
  private baseUrl = "https://api.mixpanel.com/track";
  private engageUrl = "https://api.mixpanel.com/engage#profile-set";
  private importUrl = "https://api.mixpanel.com/import?strict=1";

  constructor(token: string) {
    this.token = token;
  }

  async trackEvent<T extends keyof MixpanelEventTypes>(
    event: T | string,
    properties: MixpanelEventTypes[T],
    distinct_id?: string | number,
    version?: "v1" | "v2"
  ): Promise<any> {
    const appInstanceId = await createOrReadAppInstanceId();

    const data = {
      event,
      properties: {
        token: this.token,
        distinct_id: distinct_id || appInstanceId,
        $user_id: distinct_id || appInstanceId,
        ...properties,
        $device_id: appInstanceId,
      },
    };

    const dataV2 = {
      event,
      properties: {
        token: MIXPANEL_TOKEN_V2,
        distinct_id: distinct_id || appInstanceId,
        $user_id: distinct_id || appInstanceId,
        ...properties,
        $device_id: appInstanceId,
      },
    };

    // Create a unique key based on event name, user ID, and timestamp
    const timestamp = new Date().toISOString();
    const analyticsKey = `${event}_${distinct_id || "anonymous"}_${timestamp}`;

    // Create a promise for saving analytics data to database using TRPC
    const analyticsPromise = rawTrpcClient.analytics.createAnalytics
      .mutate({
        key: analyticsKey,
        value: {
          event,
          ...properties,
          distinct_id: distinct_id || "anonymous",
          timestamp,
          version: version || "v1",
        },
      })
      .catch((error) => {
        console.error("Failed to save analytics to database:", error);
        // Return null to avoid disrupting the Promise.all
        return null;
      });

    if (version === "v2") {
      // Run all requests in parallel
      return Promise.all([
        analyticsPromise,
        axios.post(this.baseUrl, [dataV2], {
          headers: { accept: "text/plain", "Content-Type": "application/json" },
          params: { verbose: "2" },
        }),
        axios.post(this.baseUrl, [data], {
          headers: { accept: "text/plain", "Content-Type": "application/json" },
          params: { verbose: "2" },
        }),
      ]);
    }

    // Run both requests in parallel
    return Promise.all([
      analyticsPromise,
      axios.post(this.baseUrl, [data], {
        headers: { accept: "text/plain", "Content-Type": "application/json" },
        params: { verbose: "2" },
      }),
    ]);
  }

  createUserProfile(payload: CreateUserProfile) {
    // Create a promise for the Mixpanel API call
    const mixpanelPromise = axios.post(this.engageUrl, [payload], {
      params: { verbose: "2" },
      headers: { "Content-Type": "application/json" },
    });

    return Promise.all([mixpanelPromise]);
  }

  async mergeIdentities(deviceId: string, userId: string): Promise<void> {
    const projectId = ENV.EXPO_PUBLIC_MIXPANEL_PROJECT_ID; // Replace with your actual project ID

    const authString = ENV.EXPO_PUBLIC_MIXPANEL_SERVICE_ACCOUNT_AUTH_HEADER;

    const payload = [
      {
        event: "$merge",
        properties: {
          $distinct_ids: [deviceId, userId],
          distinct_id_before_identity: deviceId,
        },
      },
    ];

    try {
      await axios.post(
        `${this.importUrl}?strict=1&project_id=${projectId}`,
        payload,
        {
          headers: {
            Accept: "application/json",
            Authorization: authString,
            "Content-Type": "application/json",
          },
        }
      );
      console.log(`Successfully merged ${deviceId} with ${userId}`);
    } catch (error) {
      console.error("Error merging identities:", error);
    }
  }
}

const mixpanel = new MixpanelTracker(MIXPANEL_TOKEN!);

// Create action events
export const useGlobalActions = () => {
  return async ({
    actionTag,
    metadata,
    screenName,
  }: {
    actionTag?: string | undefined;
    metadata?: Record<string, string> | undefined;
    screenName?: string;
  }) => {
    const user = await AsyncStorage.retrieveData<JwtUser>("persisted-user");
    const userId = user?._id;

    mixpanel.trackEvent(
      "ACTION",
      {
        action: actionTag || "",
        screenName: screenName ?? "",
        ..._.mapValues({ ...metadata }, String),
        deviceId: user?.deviceId || "",
      },
      userId ? String(userId) : undefined
    );
  };
};

export default mixpanel;
