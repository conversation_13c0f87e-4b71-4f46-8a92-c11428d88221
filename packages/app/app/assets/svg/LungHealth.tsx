import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"


function LungHealth(props: SvgProps) {
  const { color = "#FF8E1C" } = props;
  return (
    <Svg
      width={24}
      height={23}
      viewBox="0 0 24 23"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M16.244 2.917c-.73-.292-1.383-.379-1.64.134-.335.671-.227 2.96-.46 3.374a.517.517 0 01-.584.31c-.393-.027-.393-.902-.393-.902V0h-.584v.583h-1.166V0h-.584v5.833s0 .875-.39.913a.517.517 0 01-.584-.309c-.233-.414-.125-2.704-.461-3.374-.26-.514-.898-.438-1.642-.146C3.416 4.815.333 9.57.333 15.143a14.264 14.264 0 001.835 7.064c.06.085.382.488.682.488 2.202 0 5.93-2.238 6.6-4.253.672-2.015.37-6.011 1.118-7.157A2.158 2.158 0 0112 10.284c.597.117 1.117.48 1.432 1.001.747 1.146.446 5.145 1.117 7.157.67 2.013 4.399 4.253 6.6 4.253.292 0 .622-.403.683-.488a14.264 14.264 0 001.835-7.064c0-5.573-3.083-10.328-7.423-12.226zm-4.827-1.75h1.166v.583h-1.166v-.583zm0 1.166h1.166v.584h-1.166v-.584zm0 1.167h1.166v.583h-1.166V3.5zm0 1.167h1.166v.583h-1.166v-.583zm8.458 8.525c.134.538.297 1.067.49 1.587.17.323.422.596.729.793a.583.583 0 01-.344 1.053c-.335 0-.645-.341-.875-.557a2.736 2.736 0 01-.697-1.082c-.195-.584-.344-1.167-.531-1.733-.17.331-.274.69-.31 1.059 0 .196.02.382.024.583a.584.584 0 01-1.123.245c-.175-.425-.075-1.044.02-1.458a5.86 5.86 0 01.806-1.75 5.302 5.302 0 00-1.785-1.837c.13.538.175 1.095.13 1.647.003.289-.027.576-.09.858a.584.584 0 01-.941.309c-.254-.213-.2-.496-.167-.781.102-.937.07-1.96-.583-2.713a1.127 1.127 0 00-.583-.263c-.263-.075-.528-.148-.791-.227A2.746 2.746 0 0112 8.21a2.738 2.738 0 01-1.275.7c-.262.079-.527.152-.79.228-.263.076-.461.093-.604.292a2.854 2.854 0 00-.563 1.286 6.018 6.018 0 000 1.394c.032.292.088.569-.166.782a.584.584 0 01-.945-.35 5.293 5.293 0 01.05-2.468 5.288 5.288 0 00-1.782 1.832c.374.527.644 1.121.796 1.75.058.314.085.634.079.954.016.179-.001.36-.05.533a.582.582 0 01-1.12-.175c-.023-.236 0-.481 0-.717a2.624 2.624 0 00-.292-1.006c-.277.845-.396 1.869-.988 2.566-.315.376-1.062 1.228-1.561.604-.5-.624.583-1.108.826-1.572.153-.358.27-.73.352-1.111.158-.458.278-.899.45-1.36a6.72 6.72 0 011.429-2.277 2.164 2.164 0 00-2.094.743c-.24.266-.604.394-.9.111a.583.583 0 01-.042-.802c.927-1.289 2.709-1.537 4.098-.939l.177-.117a3.796 3.796 0 01-.968-1.948c-.07-.391-.16-.925.292-1.117a.583.583 0 01.732.242c.07.175.105.362.102.551.094.661.42 1.267.922 1.709.483-.203.978-.377 1.48-.522.422-.091.833-.22 1.229-.388.26-.143.455-.383.543-.668V5.833h1.166v1.115c.096.296.306.541.584.682.389.159.79.285 1.2.376.505.145 1 .319 1.483.522.512-.45.84-1.073.925-1.75a1.303 1.303 0 01.099-.519.583.583 0 01.732-.239c.467.198.36.758.29 1.152a3.794 3.794 0 01-.961 1.91l.178.117c1.406-.607 3.138-.338 4.083.931a.597.597 0 01.018.758c-.292.344-.694.227-.948-.056a2.172 2.172 0 00-2.097-.746 7.535 7.535 0 011.706 3.106z"
        fill={color}
      />
    </Svg>
  )
}

export default LungHealth
