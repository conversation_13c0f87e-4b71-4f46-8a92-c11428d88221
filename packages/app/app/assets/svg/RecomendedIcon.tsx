import * as React from "react"
import Svg, { Path, SvgProps, Text as SvgText } from "react-native-svg"

function RecommendedIcon(props: SvgProps) {
  return (
    <Svg
      width={110}
      height={22}
      viewBox="0 0 110 22"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M0 11C0 4.925 4.925 0 11 0h94a5 5 0 015 5v12a5 5 0 01-5 5H11C4.925 22 0 17.075 0 11z"
        fill="#0B79D3"
      />
      {/* Recommended Text */}
      <SvgText
        x="88"  // Moves text towards the right side
        y="15"  // Adjusts vertical alignment
        fill="#FFF"
        fontSize="13"
        fontWeight="500"
        fontFamily="Montserrat"
        textAnchor="end"  // Aligns text to the right
        dx={18}
      >
        recommended
      </SvgText>
    </Svg>
  )
}

export default RecommendedIcon;
