import * as React from "react";
import Svg, { Path, Text as SvgText, SvgProps } from "react-native-svg";

interface QuestionBubbleProps extends SvgProps {
  message?: string; // New prop for dynamic text
}

function OnboardingW1({ message = "Hi there! I’m Belle!", ...props }: QuestionBubbleProps) {
  return (
    <Svg
      width={222}
      height={87}
      viewBox="0 0 222 87"
      fill="none"
      {...props}
    >
      <Path
        d="M1 15C1 7.268 7.268 1 15 1h192c7.732 0 14 6.268 14 14v34.833c0 7.56-6.003 13.756-13.56 13.993l-26.148.822c-.956.03-1.87.402-2.576 1.048l-20.937 19.172c-1.117 1.023-2.921.482-3.291-.986l-4.082-16.197a4 4 0 00-3.843-3.023L14.877 63.5C7.192 63.432 1 57.185 1 49.5V15z"
        stroke="#B4DDFF"
        strokeWidth={2}
      />

      <SvgText
        x="50%" 
        y="50%" 
        textAnchor="middle" 
        fill="#004987"
        fontSize="18"
        dy={-5}
      >
        {message}
      </SvgText>
    </Svg>
  );
}

export default OnboardingW1;
