import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

export default function LogoSimple(props: SvgProps) {
  return (
    <Svg width={63} height={62} viewBox="0 0 63 62" fill="none" {...props}>
      <Path
        d="M26.476 62c-.3.002-.6-.019-.896-.063-1.195-.188-1.457-.796-1.493-1.138-.126-.895.83-1.852 2.915-2.937-5.427 2.385-10.873 3.28-15.766 2.534-7.742-1.192-10.114-7.154-10.84-10.62C-.828 43.93.898 36.815 4.02 34.845c1.374-.865 5.795-2.01 9.313-2.343-.042-.054-.086-.108-.125-.161-1.473-1.872-2.623-4.027-3.077-5.762-1.389-5.255 4.905-22.487 5.174-23.218.048-.128 1.156-2.98 3.51-3.326 1.672-.248 3.453.828 5.293 3.207 2.18 2.82 18.32 24.145 19.714 39.415a27.212 27.212 0 002.429-4.232c5.254-11.566 1.454-23.155-3.854-30.14-1.58-2.086-2.903-3.07-3.823-2.858-1.92.438-3.065 6.075-3.901 10.191a.596.596 0 01-1.171-.238c1.248-6.152 2.324-10.55 4.806-11.116 1.473-.336 3.074.713 5.04 3.3 5.516 7.261 9.462 19.31 3.99 31.352A29.864 29.864 0 0143.9 44.56v.632a9.463 9.463 0 011.95-1.377c2.091-4.313 6.922-7.49 13.099-8.483 2.434-.394 3.623-.21 3.97.614.457 1.073-1.127 2.384-1.829 2.92a.6.6 0 01-.936-.332.595.595 0 01.222-.624c.67-.498 1.315-1.192 1.42-1.49-.17-.072-.777-.221-2.66.083-5.376.861-9.638 3.449-11.756 6.984h.098c.84.125 1.554.736 2.178 1.869.017.028.03.058.042.09.597 1.666.272 3.168-.834 4.015a2.459 2.459 0 01-2.867.155c-.496-.334-1.56-1.384-.866-3.914-.422.34-.876.748-1.36 1.192a13.002 13.002 0 01-1.165 4.051C38.906 58.572 30.601 62 26.476 62zm-1.194-1.392c.152.101.767.298 2.19.125 3.336-.423 10.573-3.115 14.068-10.287.325-.673.578-1.378.756-2.104-3.137 3.034-7.417 7.154-13.863 10.135-2.629 1.237-3.086 1.964-3.157 2.134l.006-.003zm-10.975-26.98c-.972.061-1.94.172-2.9.334-2.913.465-5.756 1.261-6.76 1.893-2.432 1.535-4.313 7.846-3.092 13.68.663 3.156 2.823 8.614 9.858 9.684 6.33.965 13.665-.963 20.652-5.431a42.87 42.87 0 0010.64-9.601c-.134-5.017-2.169-11.474-6.046-19.078C31.393 14.793 24.484 5.692 23.161 3.972c-1.533-1.982-2.972-2.936-4.182-2.76-1.643.245-2.557 2.543-2.566 2.564-.063.17-6.434 17.604-5.147 22.505.421 1.606 1.494 3.619 2.907 5.386.218.277.427.525.627.748 1.246-.026 2.261.096 2.81.435a1.134 1.134 0 01.598 1.106c-.101 1.136-.818 1.318-1.12 1.342-.857.074-1.909-.763-2.78-1.67zm32.434 11.113c-.172.4-.317.81-.437 1.228-.364 1.276-.227 2.274.368 2.683a1.276 1.276 0 001.467-.114c.666-.512.833-1.49.45-2.629-.424-.757-.845-1.156-1.284-1.222a1.31 1.31 0 00-.564.054zm-4.099 1.398c-.398.475-.812.943-1.242 1.404l.152-.146c.373-.364.732-.71 1.072-1.032l.018-.226zM16.093 33.643c.247.228.547.389.873.468a.782.782 0 00.053-.232 2.094 2.094 0 00-.926-.236z"
        fill="#FF8E1C"
      />
    </Svg>
  );
}
