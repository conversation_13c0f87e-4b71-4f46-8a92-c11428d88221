import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

function CalendarIcon(props: SvgProps) {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.75 2a.75.75 0 00-1.5 0v1.25H10.5a.75.75 0 000 1.5h3.75V6a.75.75 0 001.5 0V2zm-13.5 8V6A2.75 2.75 0 015 3.25h1.25V2a.75.75 0 011.5 0v4a.75.75 0 01-1.5 0V4.75H5c-.69 0-1.25.56-1.25 1.25v3.25h16.5V6c0-.69-.56-1.25-1.25-1.25h-.5a.75.75 0 010-1.5h.5A2.75 2.75 0 0121.75 6v13A2.75 2.75 0 0119 21.75H5A2.75 2.75 0 012.25 19v-9zm1.5 9v-8.25h16.5V19c0 .69-.56 1.25-1.25 1.25H5c-.69 0-1.25-.56-1.25-1.25z"
        fill="#5485AF"
      />
    </Svg>
  );
}

export default CalendarIcon;
