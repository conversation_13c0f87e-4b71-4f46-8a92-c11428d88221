import * as React from "react";
import Svg, { G, <PERSON>, Path, SvgProps, Text } from "react-native-svg";

function ProgressCircle(props: SvgProps) {
  return (
    <Svg
      width={34}
      height={34}
      viewBox="0 0 34 34"
      fill="none"
      //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Mask id="a" fill="#fff">
        <Path d="M34 17A17 17 0 1117 0v5.192A11.807 11.807 0 1028.808 17H34z" />
      </Mask>
      <Path
        d="M34 17A17 17 0 1117 0v5.192A11.807 11.807 0 1028.808 17H34z"
        stroke="#5351E8"
        strokeWidth={10}
        mask="url(#a)"
      />
      <G>
        <Text
          x="17" 
          y="18" 
          fontSize="10"
          fontWeight="bold"
          fill="#5351E8"
          textAnchor="middle" // Centers text horizontally
          alignmentBaseline="middle" // Centers text vertically
          opacity="0.6"
        >
          0/4
        </Text>
      </G>
    </Svg>
  );
}

export default ProgressCircle;
