import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"

function GroupIcon(props: SvgProps) {
  return (
    <Svg
      width={24}
      height={25}
      viewBox="0 0 24 25"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M17.888 11.156a2.876 2.876 0 002.472-2.84 2.875 2.875 0 00-2.406-2.837M19.729 14.51c1.35.202 2.294.675 2.294 1.65 0 .67-.445 1.107-1.163 1.38"
        stroke="#2CD7AE"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        clipRule="evenodd"
        d="M11.887 14.924c-3.214 0-5.96.487-5.96 2.432 0 1.944 2.729 2.445 5.96 2.445 3.214 0 5.958-.482 5.958-2.428s-2.727-2.45-5.958-2.45zM11.887 12.148a3.819 3.819 0 10-3.82-3.82 3.804 3.804 0 003.792 3.82h.028z"
        stroke="#2CD7AE"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M5.885 11.156a2.875 2.875 0 01-2.472-2.84 2.875 2.875 0 012.406-2.837M4.044 14.51c-1.351.202-2.294.675-2.294 1.65 0 .67.444 1.107 1.162 1.38"
        stroke="#2CD7AE"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  )
}

export default GroupIcon;
