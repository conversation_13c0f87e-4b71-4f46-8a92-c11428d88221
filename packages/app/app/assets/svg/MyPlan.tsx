import * as React from "react";
import Svg, { <PERSON>, <PERSON><PERSON>, <PERSON>, De<PERSON>, ClipP<PERSON> } from "react-native-svg";

function MyPlan(props: any) {
  return (
    <Svg
      width={54}
      height={53}
      viewBox="0 0 54 53"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M42.866 41.958H15.03V11.04h27.835v30.917zM11.69 7.728v3.313h-.724c-.89.055-1.503.773-1.503 1.656-.056.884.612 1.601 1.503 1.657h.724v2.208h-.724c-.946 0-1.67.718-1.67 1.656 0 .939.724 1.656 1.67 1.656h.724v2.209h-.724c-.946 0-1.67.717-1.67 1.656 0 .938.724 1.656 1.67 1.656h.724v2.209h-.724c-.946 0-1.67.717-1.67 1.656 0 .938.724 1.656 1.67 1.656h.724v2.208h-.724c-.946 0-1.67.718-1.67 1.657 0 .938.724 1.656 1.67 1.656h.724v2.208h-.724c-.946 0-1.67.718-1.67 1.656 0 .939.724 1.657 1.67 1.657h.724v3.312h34.515V7.73H11.691z"
        fill="#004987"
      />
      <Path
        d="M22.824 32.02c1.225 0 2.227.994 2.227 2.208 0 1.215-1.002 2.209-2.227 2.209-1.225 0-2.227-.994-2.227-2.209 0-1.214 1.002-2.208 2.227-2.208zm0 6.625a4.432 4.432 0 004.398-3.864 4.371 4.371 0 00-3.285-4.803v-1.27c0-.608.501-1.104 1.114-1.104h7.793c1.838 0 3.34-1.491 3.34-3.313v-6.183l1.448 1.435a1.082 1.082 0 001.559 0 1.061 1.061 0 000-1.546l-3.34-3.312a1.103 1.103 0 00-.78-.332c-.278 0-.556.11-.78.332l-3.34 3.312a1.061 1.061 0 000 1.546 1.082 1.082 0 001.56 0l1.447-1.435v6.183c0 .607-.501 1.104-1.114 1.104h-7.793c-1.837 0-3.34 1.49-3.34 3.313v1.27c-2.172.552-3.563 2.65-3.285 4.803a4.432 4.432 0 004.398 3.864z"
        fill="#004987"
      />
      <Path
        d="M39.192 30.144a1.104 1.104 0 00-.779-.331c-.278 0-.557.11-.78.33l-2.56 2.54-2.561-2.54a1.082 1.082 0 00-1.559 0 1.061 1.061 0 000 1.547l2.561 2.54-2.56 2.539a1.061 1.061 0 000 1.546 1.083 1.083 0 001.558 0l2.56-2.54 2.562 2.54a1.083 1.083 0 001.558 0 1.061 1.061 0 000-1.546l-2.56-2.54 2.56-2.54a1.061 1.061 0 000-1.545zM18.705 22.856c.223.22.501.33.78.33.278 0 .556-.11.779-.33l2.56-2.54 2.561 2.54a1.082 1.082 0 001.56 0 1.061 1.061 0 000-1.546l-2.561-2.54 2.56-2.54a1.061 1.061 0 000-1.545 1.082 1.082 0 00-1.558 0l-2.561 2.54-2.561-2.54a1.082 1.082 0 00-1.559 0 1.061 1.061 0 000 1.546l2.561 2.54-2.56 2.539a1.061 1.061 0 000 1.546z"
        fill="#004987"
      />
    </Svg>
  );
}

export default MyPlan;
