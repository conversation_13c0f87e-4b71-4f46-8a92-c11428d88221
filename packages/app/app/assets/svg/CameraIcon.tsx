import * as React from "react";
import Svg, { Rect, Path, SvgProps } from "react-native-svg";

function CameraIcon(props: SvgProps) {
  return (
    <Svg width={24} height={25} viewBox="0 0 24 25" fill="none" {...props}>
      <Rect y={0.5} width={24} height={24} rx={9} fill="#fff" />
      <Path
        d="M14.5 4.5h-5L7 7.5H4a2 2 0 00-2 2v9a2 2 0 002 2h16a2 2 0 002-2v-9a2 2 0 00-2-2h-3l-2.5-3z"
        stroke="#004987"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M12 16.5a3 3 0 100-6 3 3 0 000 6z"
        stroke="#004987"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

export default CameraIcon;
