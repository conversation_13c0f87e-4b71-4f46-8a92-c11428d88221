import * as React from 'react';
import Svg, { Ellipse, Path, SvgProps } from 'react-native-svg';

export default function ProfileAccount(props: SvgProps) {
  return (
    <Svg width={52} height={52} viewBox="0 0 52 52" fill="none" {...props}>
      <Ellipse cx={25.9276} cy={26} rx={25.9276} ry={26} fill="#F0F0F0" />
      <Path
        d="M35.378 36.5v-2.333a4.667 4.667 0 00-4.667-4.667h-9.333a4.667 4.667 0 00-4.667 4.667V36.5M26.045 24.833a4.667 4.667 0 100-9.333 4.667 4.667 0 000 9.333z"
        stroke="#004987"
        strokeWidth={2.33333}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}
