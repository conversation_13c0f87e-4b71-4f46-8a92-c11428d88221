import * as React from "react"
import Svg, {
  Re<PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  De<PERSON>,
  <PERSON>arGradient,
  Stop
} from "react-native-svg"

function MoodTrackerLogo(props) {
  return (
    <Svg
      width={82}
      height={82}
      viewBox="0 0 82 82"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Rect width={82} height={82} rx={8} fill="#F2F9FF" />
      <Mask
        id="a"
        style={{
          maskType: "luminance"
        }}
        maskUnits="userSpaceOnUse"
        x={6}
        y={11}
        width={50}
        height={50}
      >
        <Path
          d="M28.043 60.355a24.71 24.71 0 01-3.828-.733 26.64 26.64 0 01-1.795-.564c-.452-.158-1.043-.4-1.483-.587a19.371 19.371 0 01-1.29-.603c-.59-.31-1.37-.739-1.938-1.088a27.554 27.554 0 01-2.754-1.985c-.518-.425-1.195-1.014-1.653-1.503-.43-.458-1.014-1.061-1.423-1.538-.363-.425-.84-1-1.15-1.464-.348-.518-.826-1.198-1.152-1.73a25.788 25.788 0 01-.921-1.661c-.977-1.938-1.46-3.544-1.512-3.72-.238-.808-.55-1.89-.688-2.722-.097-.59-.235-1.376-.298-1.97-.07-.651-.144-1.521-.144-2.175 0-1.203.062-2.812.23-4.003.265-1.888.641-3.14.89-3.97.128-.42.61-1.933 1.511-3.72.288-.57.709-1.314 1.042-1.86.293-.48.73-1.09 1.045-1.555.373-.549.947-1.225 1.38-1.727.357-.416.875-.933 1.252-1.33.497-.524 1.232-1.151 1.792-1.606.404-.328.956-.748 1.383-1.045.474-.331 1.124-.748 1.62-1.046.399-.24.943-.538 1.358-.748.474-.24 1.112-.55 1.604-.749.789-.318 1.531-.591 2.228-.819a25.536 25.536 0 011.707-.47 26.19 26.19 0 011.99-.384 24.997 24.997 0 015.212-.235c1.07.073 1.465.129 2.121.222.53.076 1.232.221 1.757.33.561.116 1.299.318 1.847.485.564.171 1.305.44 1.857.645.812.3 1.855.802 2.616 1.216.58.314 1.352.746 1.891 1.126.42.297.993.678 1.4.992.505.391 1.18.915 1.636 1.36.391.381.927.873 1.301 1.27.43.457.985 1.084 1.377 1.574.35.437.8 1.036 1.116 1.5.35.515.81 1.209 1.102 1.76.268.504.637 1.172.879 1.69.264.567.59 1.337.808 1.923.41 1.098.825 2.611 1.064 3.759.357 1.71.427 3.231.468 4.14.046 1-.039 2.338-.125 3.335a22.706 22.706 0 01-.315 2.111c-.122.629-.3 1.466-.496 2.076a49.038 49.038 0 01-1.34 3.6c-.248.587-.663 1.332-.977 1.886-.328.58-.79 1.34-1.188 1.873-.66.887-1.6 2.026-2.332 2.856-.466.53-1.17 1.157-1.697 1.628-.393.351-.954.777-1.366 1.108-.644.519-1.584 1.105-2.29 1.537-.525.321-1.254.702-1.806.973a24.641 24.641 0 01-11.22 2.482 24.811 24.811 0 01-2.302-.146l-.001-.001z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#a)">
        <Path
          d="M48.313 40.26c2.06.55 3.594 1.213 4.546 1.94.11-.316.215-.635.313-.955-.967-.739-2.525-1.411-4.617-1.97-.076.33-.157.658-.242.984zm.456-1.982c2.12.567 3.7 1.25 4.68 1.998.086-.325.165-.652.238-.982-.991-.757-2.59-1.448-4.736-2.021-.056.336-.117.671-.182 1.005zm.335-2.018c2.167.579 3.782 1.277 4.783 2.041.06-.332.114-.667.16-1.002-1.01-.771-2.637-1.474-4.822-2.059-.035.342-.075.681-.121 1.02zm1.753-1.583c1.436.483 2.548 1.027 3.31 1.609.033-.34.06-.68.079-1.022-.766-.585-1.883-1.13-3.326-1.616-.015.344-.036.686-.063 1.029zm-3.785-6.601a29.572 29.572 0 00-.63-3.77c-.129-.796.102-1.152.704-1.088.902.21 1.72.44 2.45.686 1.641.552 2.839 1.188 3.549 1.87l.08.096.008.01c.255.394.468.877.716 1.616a23.107 23.107 0 011.077 6.366 24.135 24.135 0 01-1.302 8.54 25.068 25.068 0 01-1.588 3.65c-.591 1.03-.827 1.007-1.017.99a5.319 5.319 0 00-1.088-.777c-1.002-.56-2.419-1.064-4.211-1.481-.052-.02-.123-.044-.17-.07-.391-.336-.469-.78-.267-1.532 1.296-3.954 1.977-8.283 1.851-12.63-.025-.846-.08-1.67-.162-2.475"
          fill="#fff"
        />
        <Path
          d="M49.103 36.257c2.168.58 3.782 1.277 4.784 2.042.06-.332.114-.667.16-1.002-1.01-.771-2.637-1.474-4.822-2.059-.035.341-.076.681-.122 1.02zM48.768 38.277c2.12.567 3.7 1.25 4.68 1.998.086-.325.166-.653.238-.982-.991-.758-2.59-1.448-4.736-2.021-.056.336-.117.67-.182 1.005zM48.313 40.256c2.06.551 3.594 1.214 4.546 1.94.11-.315.215-.633.313-.954-.967-.739-2.525-1.411-4.617-1.97-.076.33-.157.658-.242.984zM50.857 34.674c1.436.483 2.549 1.027 3.31 1.608.034-.338.06-.68.08-1.021-.766-.585-1.884-1.131-3.326-1.617a30.12 30.12 0 01-.064 1.03z"
          fill="#C5E1F9"
        />
        <Path
          d="M31.98 38.685c3.105-.052 6.228.06 9.18.354.047-.333.09-.668.131-1.004-2.998-.299-6.17-.412-9.321-.36l.011 1.01m-.025-2.028c3.195-.053 6.41.062 9.449.365.035-.339.067-.68.096-1.02-3.075-.307-6.328-.422-9.561-.37l.016 1.025zm-.034-2.054c3.265-.054 6.551.063 9.656.373.023-.343.043-.686.06-1.03-3.131-.313-6.444-.43-9.737-.376l.02 1.033zm2.443-2.078c2.483.024 4.95.15 7.313.387.01-.346.017-.691.02-1.038a82.682 82.682 0 00-7.346-.388c.006.346.01.693.013 1.039zm-5.628-5.542c-.028-1.311-.045-2.558-.053-3.736.082-.803.543-1.245 1.407-1.352a88.706 88.706 0 013.788-.043 77.496 77.496 0 018.303.516c.094.015.183.035.268.058l.02.007c.696.285 1.079.706 1.28 1.41.385 1.911.623 3.997.688 6.227a44.58 44.58 0 01-.628 8.78 44.97 44.97 0 01-.85 3.914c-.3.852-.8 1.267-1.525 1.267a62.416 62.416 0 00-3.194-.297 75.615 75.615 0 00-7.486-.12 2.815 2.815 0 01-.254-.021c-.667-.212-.984-.607-1.093-1.365a350.545 350.545 0 01-.672-15.245"
          fill="#fff"
        />
        <Path
          d="M31.92 34.602c3.266-.053 6.552.064 9.658.374.022-.343.042-.686.059-1.03-3.131-.313-6.444-.431-9.737-.377.008.345.014.69.02 1.033zM31.956 36.656c3.195-.053 6.41.062 9.449.365.035-.339.067-.68.096-1.02-3.075-.307-6.328-.423-9.561-.37l.015 1.025zM31.982 38.686c3.104-.052 6.227.06 9.179.354.047-.333.09-.667.131-1.004-2.998-.298-6.17-.412-9.321-.36l.011 1.01M34.365 32.525c2.483.025 4.95.15 7.312.387.01-.345.018-.69.021-1.037a82.72 82.72 0 00-7.346-.389c.005.347.01.693.013 1.04z"
          fill="#C5E1F9"
        />
        <Path
          d="M15.511 40.68c2.315-.609 5.018-1.093 7.918-1.434-.05-.332-.098-.666-.145-1-2.946.345-5.69.837-8.04 1.454.085.328.174.654.268.978m-.51-1.968c2.382-.627 5.164-1.125 8.15-1.476a75.628 75.628 0 01-.12-1.017c-3.022.354-5.837.858-8.248 1.492.068.334.14.668.216 1m-.406-2.01c2.435-.64 5.278-1.149 8.33-1.506a80.522 80.522 0 01-.095-1.028c-3.076.36-5.943.874-8.398 1.52.05.34.104.678.163 1.015zm1.63-2.5a68.03 68.03 0 016.524-1.068c-.024-.345-.047-.69-.067-1.037a68.387 68.387 0 00-6.554 1.073c.028.345.06.688.097 1.031m-4.254-4.557c.071-1.326.23-2.602.473-3.82.227-.845.646-1.384 1.28-1.648a41.932 41.932 0 012.87-.726c2.285-.5 4.851-.892 7.557-1.164.095-.005.186-.005.271 0l.021.002c.673.137.976.487 1.015 1.172a97.06 97.06 0 00-.007 6.213 120.76 120.76 0 001.056 12.748c.044.876-.283 1.38-1 1.538a68.3 68.3 0 00-3.102.382c-2.256.333-4.334.766-6.108 1.288-.057.01-.134.023-.191.025-.567-.083-.946-.404-1.307-1.107-1.7-3.82-2.732-8.073-2.859-12.42a29.803 29.803 0 01.03-2.484"
          fill="#fff"
        />
        <Path
          d="M14.594 36.701c2.435-.64 5.278-1.15 8.33-1.507a80.522 80.522 0 01-.094-1.028c-3.076.36-5.943.875-8.398 1.52.05.34.103.678.162 1.015zM15 38.71c2.383-.626 5.165-1.125 8.15-1.476a75.628 75.628 0 01-.12-1.017c-3.02.354-5.835.858-8.247 1.492.068.334.14.668.217 1M15.512 40.678c2.314-.608 5.017-1.093 7.917-1.433-.05-.332-.098-.666-.145-1-2.945.345-5.69.837-8.04 1.454.085.328.174.654.268.978M16.225 34.198a68.003 68.003 0 016.523-1.068c-.024-.345-.047-.69-.067-1.036a68.347 68.347 0 00-6.554 1.072c.028.345.06.688.098 1.031"
          fill="#C5E1F9"
        />
        <Path
          d="M8.042 45.134c.06-.801.752-1.626 2.133-2.43-.122-.313-.24-.629-.35-.946-1.403.815-2.105 1.654-2.167 2.467.122.306.25.61.384.91m-.73-1.838c.063-.825.775-1.675 2.196-2.502a27.66 27.66 0 01-.282-.975c-1.438.836-2.159 1.697-2.222 2.53.097.318.2.634.308.947zm-.576-1.909c.064-.843.792-1.711 2.244-2.555-.075-.331-.145-.664-.208-.998-1.464.851-2.198 1.727-2.263 2.576.069.328.145.654.228.978m-.238-2.616c.304-.648.996-1.305 2.102-1.948a26.286 26.286 0 01-.133-1.019c-1.11.646-1.806 1.306-2.111 1.957.04.34.088.676.143 1.01m3.279 10.055c-.573-.921-1.167-1.872-2.01-3.895a54.73 54.73 0 01-1.136-3.64c-.45-2.405-.518-2.775-.613-4.822.03-2.67.03-2.721.226-4.159.46-2.557.469-2.606.891-3.969l.102-.318a1.95 1.95 0 01.088-.218c.34-.725 1.185-1.459 2.58-2.171l.101-.052c.042-.014.096-.037.14-.045h.01c.297.096.406.27.266 1.05a25.71 25.71 0 00-.58 6.332c.086 2.963.64 5.864 1.58 8.6a30.31 30.31 0 001.559 3.714c.372.842.382 1.362.032 1.638l-.09.046c-.531.27-.972.546-1.325.821-.842.656-1.192 1.32-1.089 1.96l-.006.042c-.125-.1-.315-.252-.727-.914"
          fill="#fff"
        />
        <Path
          d="M6.735 41.388c.064-.843.791-1.712 2.244-2.556-.076-.331-.145-.663-.209-.998-1.464.851-2.198 1.727-2.262 2.577.068.328.144.654.227.977M7.312 43.295c.062-.825.774-1.675 2.196-2.501-.1-.323-.194-.648-.282-.976-1.439.836-2.16 1.697-2.222 2.53.096.318.199.634.308.947zM8.042 45.132c.06-.8.752-1.626 2.133-2.43-.122-.313-.24-.628-.35-.946-1.403.816-2.105 1.654-2.167 2.467.122.306.25.61.384.91M6.498 38.772c.304-.649.995-1.305 2.101-1.948a26.3 26.3 0 01-.133-1.02c-1.11.646-1.805 1.306-2.11 1.958.04.339.088.676.142 1.01"
          fill="#C5E1F9"
        />
      </G>
      <Path
        d="M6.204 34.551c.105-1.355.326-2.683.654-3.974.226-.923.326-1.494.313-1.797-.04-.25-.019-.505.065-.76l-.102.317c-.663 2.45-.689 2.546-.892 3.97-.254 2.586-.263 2.687-.226 4.159.176 2.523.199 2.848.614 4.822a54.31 54.31 0 001.136 3.639c-.846-2.399-1.897-5.384-1.562-10.376"
        fill="#fff"
      />
      <Path
        d="M14.396 49.17c-2.17-.527-3.823-1.17-4.898-1.883-.126-.288-.247-.58-.362-.875 1.092.724 2.77 1.377 4.974 1.912.09.287.186.569.285.846m-.543-1.718c-2.235-.543-3.935-1.205-5.042-1.938-.103-.303-.2-.61-.29-.92 1.12.742 2.84 1.412 5.102 1.96.072.303.149.603.23.898zm-.432-1.818c-2.284-.554-4.022-1.23-5.154-1.98-.078-.317-.149-.636-.214-.959 1.141.756 2.893 1.438 5.196 1.997.053.318.11.632.172.942zm-1.948-2.351c-1.528-.466-2.735-.995-3.596-1.566a24.44 24.44 0 01-.137-.996c.866.573 2.079 1.104 3.614 1.573.034.333.074.662.12.989zm3.655-4.696c.053-1.38.18-2.75.374-4.101.066-.933-.213-1.498-.851-1.728-.946-.2-1.81-.419-2.587-.656-1.746-.532-3.054-1.151-3.878-1.822l-.09-.038h-.009c-.2.09-.408.183-.677 1.151a24.355 24.355 0 00-.694 6.515 22.938 22.938 0 001.78 8.28 22.118 22.118 0 001.776 3.368c.467.708.848 1.2 1.133 1.464.323.263.732.518 1.22.76 1.1.547 2.61 1.033 4.491 1.43.053.007.125.017.179.018.384-.066.468-.357.256-.89-1.43-3.057-2.308-6.872-2.434-11.22-.025-.845-.02-1.69.011-2.531z"
        fill="#9AC7F7"
      />
      <Path
        d="M13.42 45.634c-2.283-.554-4.022-1.23-5.153-1.98-.078-.317-.15-.636-.214-.959 1.14.756 2.893 1.438 5.195 1.997.053.318.11.632.173.942zM13.852 47.45c-2.235-.543-3.935-1.205-5.042-1.938-.103-.303-.2-.61-.29-.92 1.12.742 2.84 1.412 5.102 1.96.072.303.149.603.23.897zM14.395 49.17c-2.171-.527-3.823-1.17-4.899-1.882-.126-.288-.246-.58-.361-.876 1.092.724 2.77 1.377 4.974 1.912.09.287.186.569.285.846M11.473 43.279c-1.528-.466-2.735-.995-3.596-1.566-.052-.33-.098-.66-.137-.996.865.573 2.078 1.104 3.614 1.573.034.333.074.662.12.989z"
        fill="#C5E1F9"
      />
      <Path
        d="M33.282 49.002s3.944-3.542.902-9.162c-1.686-3.117-.802-11.629 4.657-8.987 5.46 2.642 3.98-.652 7.062.817 3.085 1.47 4.447 3.68 4.033 6.091-.417 2.411-.145 7.007 1.75 7.89 1.895.884 10.977 5.826 5.124 12.069-5.853 6.241-27.134 1.658-23.528-8.72v.002z"
        fill="#194866"
      />
      <Path
        d="M35.196 48.443s-3.417 10.928-3.232 12.37c.185 1.44 1.284 5.876 2.612 5.146 1.328-.73 3.202-4.874 3.77-8.177.567-3.305 6.322-14.13-3.15-9.339z"
        fill="url(#paint0_linear_4931_10156)"
      />
      <Path
        d="M38.649 47.217c.445 0 5.683-.557 5.683-.557l3.566 1.56s1.783-.111 1.338 2.786c-.447 2.898-5.126 14.152-6.018 14.71-.891.557-7.8 1.56-8.915 1.114-1.114-.446-3.12-.668-3.12-.668s0-8.135-.223-8.58c-.223-.446-.335-3.12 2.229-5.126 1.766-1.382 5.46-5.238 5.46-5.238z"
        fill="url(#paint1_linear_4931_10156)"
      />
      <Path
        d="M46.45 49.559s-7.467 10.029-7.578 11.477c-.112 1.449 1.226 5.684 2.674 5.238 1.449-.446 4.123-4.123 5.35-7.244 1.225-3.12 5.46-13.26-.447-9.471z"
        fill="url(#paint2_linear_4931_10156)"
      />
      <Path
        d="M44.443 39.418l-.669 7.243-4.68.557 1.114-4.569 4.235-3.231z"
        fill="#F7B68B"
      />
      <Path
        d="M39.587 45.057c.125-.048.258-.108.4-.18 1.474-.736 3.09-1.814 4.18-2.815l.275-3.203-4.234 3.463-.622 2.735h.001z"
        fill="#EC9568"
      />
      <Path
        d="M35.975 35.295s-.334 3.677.669 5.795c1.003 2.117.891 3.789 3.12 2.674 2.23-1.114 4.792-3.009 5.35-4.234.556-1.226-.335-2.898-1.784-2.117-1.448.78-1.337-.335-2.786-.112-1.448.223-4.122 0-4.569-2.006z"
        fill="#F7B68B"
      />
      <Path
        d="M23.883 62.666c.178.394.714.835 1.201.835h7.042c.486 0 .737-.44.56-.835l-4.565-10.34c-.178-.394-.715-.889-1.201-.889h-7.043c-.486 0-.738.495-.56.89l4.566 10.339z"
        fill="url(#paint3_linear_4931_10156)"
      />
      <Path
        d="M26.92 51.438h-7.043c-.486 0-.738.494-.56.888l4.566 10.34c.178.394.714.835 1.201.835"
        fill="url(#paint4_linear_4931_10156)"
      />
      <Path
        d="M29.75 55.567c-.355.009.73 1.279.244 1.4-.488.122-3.532-1.583-3.532-1.583l-.974-.122 2.39 1.903c.183.091-1.548-1.117-2.466-1.508-.527-.223-.887-.107-1.02-.151-.365-.122 3.151 2.192 3.151 2.192l-1.493-.487s-.806-.183-.411.274c.216.248.549.304 1.005.944.457.64 2.619 2.53 4.795 2.801 1.096.137 1.828.73 1.828.73l6.245 5.094s3.863-.253 3.315-2.901c-.548-2.649-4.628-2.787-7.871-3.837-2.13-.69-2.284-1.278-2.74-1.736-.457-.456-1.329-3.041-2.467-3.013z"
        fill="#F7B68B"
      />
      <Path
        d="M19.594 74.521s-2.898.446-3.566.224c-.668-.224-4.012-.335-5.237.445-1.226.78-2.118 2.674 1.671 3.454 3.789.78 8.803.112 9.472-.334.668-.446-1.449-4.235-2.34-3.79v.001z"
        fill="url(#paint5_linear_4931_10156)"
      />
      <Path
        d="M37.201 68.055s-12.035-5.238-16.938-6.24c-4.904-1.004-9.134-3.678-7.128 1.67 2.006 5.35 15.374 14.933 15.374 14.933s13.149 3.009 8.691-10.363h.001z"
        fill="url(#paint6_linear_4931_10156)"
      />
      <Path
        d="M19.148 74.298S33.301 64.046 35.975 62.82c2.674-1.226 7.243-3.566 8.358-1.114 1.114 2.451 1.337 5.795.78 8.803-.557 3.01-5.913 6.696-9.806 7.02-1.338.112-.335-5.125-.335-5.125L21.6 77.864l-2.452-3.566z"
        fill="url(#paint7_linear_4931_10156)"
      />
      <Path
        d="M21.71 76.414s19.614-7.243 20.617-8.246c1.003-1.004.111 3.9-.224 5.125"
        stroke="#024F83"
        strokeWidth={0.186606}
        strokeMiterlimit={10}
      />
      <Path
        d="M19.371 63.709s4.904 2.563 6.24 5.46"
        stroke="#00497D"
        strokeWidth={0.186606}
        strokeMiterlimit={10}
      />
      <Path
        d="M31.963 75.413s-2.229-2.006-3.677-2.229c-1.449-.223-3.343-.891-3.79.668-.445 1.56 1.672 4.346 4.681 5.015 3.01.668 3.876 1.356 5.46.78 1.227-.446 1.56-2.006 1.338-2.786-.223-.78-2.674-1.003-4.012-1.449v.001z"
        fill="url(#paint8_linear_4931_10156)"
      />
      <Path
        d="M20.265 55.465c0-.335 2.004.372 2.004.372s.25.412.298.742c.074.52.668 1.412.668 1.412-.892.78-1.263.891-1.263.891s-.481.149-.816.149c-.334 0-1.114-1.895-1.226-2.23-.111-.334.335-1.225.335-1.337v.001z"
        fill="#F7B68B"
      />
      <Path
        d="M54.185 71.893c.004.022.009.044.009.067 0 .63-1.291 1.14-2.883 1.14s-2.883-.51-2.883-1.14c0-.023.005-.045.009-.067h-.03v6.167a6.674 6.674 0 005.854 0v-6.167h-.076z"
        fill="#7EB3FF"
      />
      <Path
        d="M51.31 73.099c1.593 0 2.884-.511 2.884-1.14 0-.63-1.291-1.14-2.883-1.14-1.593 0-2.883.51-2.883 1.14 0 .629 1.29 1.14 2.883 1.14zM53.791 73.5h1.021c.3 0 .543.243.543.543v1.618c0 .3-.242.543-.543.543h-1.021"
        stroke="#0561FC"
        strokeWidth={0.186606}
        strokeMiterlimit={10}
      />
      <Path
        d="M79.963 60.45L63.87 53.548 56.73 70.205l16.091 6.9 7.143-16.656z"
        fill="#fff"
      />
      <Path
        d="M72.468 74.567L59.906 69.18l4.52-10.541"
        stroke="#0561FC"
        strokeWidth={0.186606}
        strokeMiterlimit={10}
      />
      <Path
        d="M72.468 74.565l-12.562-5.386s2.349-1.406 3.363-1.505c.553-.054 2.184 1.122 2.908.89 1.053-.338 1.7-2.657 4.003-1.888 2.912.97 5.607-.461 5.607-.461"
        fill="#7EB3FF"
      />
      <Path
        d="M61.904 70.046l4.52-10.542M64.156 71.071l4.52-10.542M66.41 72.094l4.52-10.541M68.662 73.119l4.52-10.543M70.914 74.143l4.52-10.541"
        stroke="#C5E1F9"
        strokeWidth={0.186606}
        strokeMiterlimit={10}
      />
      <Path
        d="M76.494 60.541l.766 1.916M77.836 61.117l-1.916.766"
        stroke="#0561FC"
        strokeWidth={0.186606}
        strokeMiterlimit={10}
      />
      <Path
        d="M74.597 30.723l-17.388 2.058 2.13 17.998 17.388-2.058-2.13-17.998z"
        fill="#fff"
      />
      <Path
        d="M73.253 32.69a.391.391 0 11-.782.012.391.391 0 01.782-.012zM71.993 32.856a.39.39 0 11-.782.013.39.39 0 01.782-.013zM70.733 33.02a.391.391 0 11-.782.012.391.391 0 01.782-.012z"
        stroke="#0561FC"
        strokeWidth={0.186606}
        strokeMiterlimit={10}
      />
      <Path
        d="M62.562 44.096l-.761.101.506 3.807.762-.102-.507-3.806zM64.61 40.258l-.762.101.972 7.31.762-.102-.973-7.309zM67.751 44.67l-.76.102.34 2.564.761-.101-.34-2.565z"
        fill="#0063CB"
      />
      <Path
        d="M69.553 38.98l-.762.102 1.053 7.916.762-.101-1.053-7.916zM72.318 40.555l-.761.101.8 6.01.76-.102-.799-6.01z"
        fill="#9AC7F7"
      />
      <Path
        d="M69.855 41.254l-.761.102.75 5.643.762-.101-.75-5.644zM72.549 42.282l-.762.1.57 4.285.762-.102-.57-4.283z"
        fill="#0063CB"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_4931_10156"
          x1={38.1697}
          y1={65.0501}
          x2={31.0588}
          y2={49.4966}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#638FB6" />
          <Stop offset={1} stopColor="#B2BFCD" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_4931_10156"
          x1={44.1267}
          y1={65.8742}
          x2={39.6811}
          y2={46.6156}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#8BB3D6" />
          <Stop offset={1} stopColor="#CBD4DE" />
        </LinearGradient>
        <LinearGradient
          id="paint2_linear_4931_10156"
          x1={46.5234}
          y1={65.3813}
          x2={41.0781}
          y2={49.6831}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#638FB6" />
          <Stop offset={1} stopColor="#CBD4DE" />
        </LinearGradient>
        <LinearGradient
          id="paint3_linear_4931_10156"
          x1={27.0997}
          y1={63.065}
          x2={25.6459}
          y2={54.3879}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#6FA9DB" />
          <Stop offset={1} stopColor="#A5DBF6" />
        </LinearGradient>
        <LinearGradient
          id="paint4_linear_4931_10156"
          x1={23.7136}
          y1={63.065}
          x2={21.2933}
          y2={54.8581}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#6FA9DB" />
          <Stop offset={1} stopColor="#A5DBF6" />
        </LinearGradient>
        <LinearGradient
          id="paint5_linear_4931_10156"
          x1={11.8541}
          y1={75.8365}
          x2={14.0272}
          y2={82.3159}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#00497C" />
          <Stop offset={1} stopColor="#1D9AD4" />
        </LinearGradient>
        <LinearGradient
          id="paint6_linear_4931_10156"
          x1={35.0441}
          y1={75.2895}
          x2={33.4029}
          y2={58.5435}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#00497C" />
          <Stop offset={1} stopColor="#1D9AD4" />
        </LinearGradient>
        <LinearGradient
          id="paint7_linear_4931_10156"
          x1={17.2979}
          y1={60.5799}
          x2={18.2513}
          y2={75.9586}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#00497C" />
          <Stop offset={1} stopColor="#1D9AD4" />
        </LinearGradient>
        <LinearGradient
          id="paint8_linear_4931_10156"
          x1={26.4324}
          y1={74.948}
          x2={31.1701}
          y2={83.5143}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#00497C" />
          <Stop offset={1} stopColor="#1D9AD4" />
        </LinearGradient>
      </Defs>
    </Svg>
  )
}

export default MoodTrackerLogo;
