import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

export default function Time(props: SvgProps) {
  return (
    <Svg width={20} height={21} viewBox="0 0 20 21" fill="none" {...props}>
      <Path
        d="M17.5 8.921V5.588a1.667 1.667 0 00-1.667-1.667H4.167A1.667 1.667 0 002.5 5.588v11.667c0 .916.75 1.666 1.667 1.666H10M13.333 2.255V5.59M6.667 2.255V5.59M2.5 8.921h15"
        stroke="#004987"
        strokeWidth={1.66667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M17.742 12.839a2.025 2.025 0 00-2.209-.434c-.25.1-.475.25-.666.442l-.284.283-.291-.283a2.025 2.025 0 00-2.867 0c-.792.783-.833 2.108.167 3.117l2.991 2.958 3-2.958c1-1.009.95-2.334.159-3.117v-.008z"
        stroke="#004987"
        strokeWidth={1.66667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}
