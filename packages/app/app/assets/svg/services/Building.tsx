import * as React from "react";
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON>, SvgProps } from "react-native-svg";

export default function Building(props: SvgProps) {
  return (
    <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
      <G
        clipPath="url(#clip0_2842_1385)"
        stroke="#64748B"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <Path d="M4 14.667v-12a1.333 1.333 0 011.333-1.333h5.334A1.333 1.333 0 0112 2.667v12H4zM4 8H2.667a1.333 1.333 0 00-1.334 1.333v4a1.333 1.333 0 001.334 1.334H4M12 6h1.333a1.333 1.333 0 011.334 1.333v6a1.333 1.333 0 01-1.334 1.334H12M6.667 4h2.666M6.667 6.667h2.666M6.667 9.334h2.666M6.667 12h2.666" />
      </G>
      <Defs>
        <ClipPath id="clip0_2842_1385">
          <Path fill="#fff" d="M0 0H16V16H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
