import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

export default function Globe(props: SvgProps) {
  return (
    <Svg width={20} height={21} viewBox="0 0 20 21" fill="none" {...props}>
      <Path
        d="M10 18.922a8.333 8.333 0 100-16.666 8.333 8.333 0 000 16.666zM1.667 10.589h16.666"
        stroke="#004987"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M10 2.255a12.75 12.75 0 013.333 8.334A12.75 12.75 0 0110 18.922a12.75 12.75 0 01-3.333-8.333A12.75 12.75 0 0110 2.255z"
        stroke="#004987"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}
