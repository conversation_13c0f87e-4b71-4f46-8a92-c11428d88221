import * as React from "react";
import Svg, { G, Rect, Path, Defs, SvgProps } from "react-native-svg";

export default function Location(props: SvgProps) {
  return (
    <Svg width={57} height={57} viewBox="0 0 57 57" fill="none" {...props}>
      <G filter="url(#filter0_d_988_5641)">
        <Rect
          x={5.38434}
          y={4.58887}
          width={46}
          height={46}
          rx={23}
          fill="#EEFEFF"
        />
        <Path
          d="M35.718 25.755c0 5.5-7.334 11-7.334 11s-7.333-5.5-7.333-11a7.333 7.333 0 1114.667 0z"
          stroke="#5CCAD2"
          strokeWidth={1.83333}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M28.384 28.506a2.75 2.75 0 100-5.5 2.75 2.75 0 000 5.5z"
          stroke="#5CCAD2"
          strokeWidth={1.83333}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </G>
      <Defs></Defs>
    </Svg>
  );
}
