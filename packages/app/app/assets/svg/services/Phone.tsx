import * as React from "react";
import Svg, { <PERSON>, <PERSON><PERSON>, <PERSON>, Defs, SvgProps } from "react-native-svg";
/* SVGR has dropped some elements not supported by react-native-svg: filter */

export default function Phone(props: SvgProps) {
  return (
    <Svg width={53} height={53} viewBox="0 0 53 53" fill="none" {...props}>
      <G filter="url(#filter0_d_988_5646)">
        <Rect
          x={5.38434}
          y={4.58887}
          width={42.2313}
          height={42.2675}
          rx={21.1157}
          fill="#EEFEFF"
        />
        <Path
          d="M35.615 30.266v2.75a1.833 1.833 0 01-1.998 1.833 18.141 18.141 0 01-7.911-2.814 17.876 17.876 0 01-5.5-5.5 18.142 18.142 0 01-2.814-7.948 1.834 1.834 0 011.824-1.998h2.75a1.833 1.833 0 011.833 1.577c.116.88.332 1.744.642 2.575a1.833 1.833 0 01-.413 1.935l-1.164 1.164a14.667 14.667 0 005.5 5.5l1.164-1.164a1.834 1.834 0 011.935-.413c.831.31 1.695.526 2.575.642a1.833 1.833 0 011.577 1.86z"
          stroke="#5CCAD2"
          strokeWidth={1.83333}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </G>
      <Defs></Defs>
    </Svg>
  );
}
