import * as React from "react";
import Svg, { G, Rect, Path, Defs, SvgProps } from "react-native-svg";
/* SVGR has dropped some elements not supported by react-native-svg: filter */

export default function Email(props: SvgProps) {
  return (
    <Svg width={56} height={56} viewBox="0 0 56 56" fill="none" {...props}>
      <G filter="url(#filter0_d_988_5639)">
        <Rect
          x={5.38434}
          y={4.58887}
          width={45}
          height={45}
          rx={22.5}
          fill="#EEFEFF"
        />
        <Path
          d="M34.884 20.088h-14a1.75 1.75 0 00-1.75 1.75v10.5c0 .967.784 1.75 1.75 1.75h14a1.75 1.75 0 001.75-1.75v-10.5a1.75 1.75 0 00-1.75-1.75z"
          stroke="#5CCAD2"
          strokeWidth={1.75}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M36.634 22.713l-7.849 4.988a1.698 1.698 0 01-1.802 0l-7.849-4.988"
          stroke="#5CCAD2"
          strokeWidth={1.75}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </G>
      <Defs></Defs>
    </Svg>
  );
}
