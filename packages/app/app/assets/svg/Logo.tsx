import * as React from "react";
import { Image, StyleSheet } from "react-native";
import Svg, { G, Path, Defs, ClipPath, SvgProps } from "react-native-svg";

const styles = StyleSheet.create({
  logo: {
    width: 390,
    height: 180,
  },
});

export default function Logo(props: SvgProps) {
  return (
    <Image
      style={styles.logo}
      resizeMode="contain"
      source={require("../../assets/img/logo-large.png")}
    />
  );
}
