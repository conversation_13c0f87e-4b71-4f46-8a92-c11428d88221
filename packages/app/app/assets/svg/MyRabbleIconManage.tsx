import * as React from "react"
import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-native-svg"

function MyRabbleIconManage(props: any) {
    return (
        <Svg
          width={111}
          height={68}
          viewBox="0 0 111 68"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          {...props}
        >
          <G clipPath="url(#clip0_4931_10947)" fill={props.fill}>
            <Path d="M24.748 50.278c-.204 0-.445.148-.723.352-.685.538-1.426 1.225-2.297 2.06 0 0-.87-4.844-1.76-4.844-1.5 0-.833 5.272-2.352 5.272-.815-.223-.204-2.989-1.482-2.989-.297 0-.445.223-.519.52-.482.352-1.111.872-2.112.872-1.537 0-2.297-2.654-3.278-2.654-1.76 0-2.335 2.32-3.28 2.32-.851 0-1.037-3.063-2.556-3.063-1.89 0-2 3.936-2.612 3.936-.185 0-.24-.112-.24-.297 0-.167.055-.409.055-.65 0-.242-.055-.483-.222-.687-.185-.204-.352-.279-.5-.279-.556 0-.89 1.04-.89 1.486 0 1.225.52 2.45 1.871 2.45 1.39 0 1.63-4.251 2.409-4.251.315 0 .778 2.989 2.519 2.989 1.482 0 2.112-2.395 3.02-2.395.815 0 1.13 2.376 3.556 2.376.852 0 1.649-.278 2.223-.668.26 1.3 1.149 2.524 1.945 2.524 2.149 0 2.204-4.102 2.204-4.102l.686 3.193s-3.964 3.545-3.964 10.488c0 1.82.907 3.453 2.612 3.453 2.667 0 3.612-5.495 3.612-8.187 0-2.728-.63-5.494-.63-5.494.63-.724 1.241-1.355 2.242-2.024.574-.39.796-1.02.796-1.318.037-.13-.074-.39-.333-.39zm-5.799 15.741c-.666 0-1.24-.575-1.24-1.8 0-5.792 3.093-9.06 3.093-9.06s.574 2.674.574 4.604c0 1.875-.556 6.256-2.427 6.256zM34.974 43.282c3.427 0 5.465 1.782 5.465 4.79 0 2.376-1.445 3.898-3.78 4.659-.314.111-.333.26-.018.408 1.26.613 2.093 1.838 2.65 3.175l1.87 4.418c.074.185 0 .278-.148.278h-1.334c-.13 0-.204-.055-.26-.185l-1.666-3.862c-1-2.431-2.52-3.527-5.076-3.527h-2.964v7.351c0 .149-.074.242-.24.242H28.23c-.148 0-.24-.075-.24-.242V43.505c0-.149.073-.241.24-.241h6.743v.018zm-1.575 8.614c3.372 0 5.298-1.374 5.298-3.732 0-2.097-1.408-3.322-3.723-3.322h-5.28v7.072H33.4v-.018z" />
            <Path d="M46.22 48.275c-1.02 0-1.964.13-2.798.39l-.963.279a.41.41 0 00-.241.185.53.53 0 00-.019.297l.278 1.021a.41.41 0 00.186.242.408.408 0 00.296 0l.908-.26c.63-.205 1.407-.297 2.297-.297 2.13 0 3.316 1.17 3.316 3.323v.204h-3.724c-2.927.018-4.668 1.503-4.668 3.935 0 2.413 1.593 3.861 4.26 3.861 1.909 0 3.28-.557 4.187-1.726.148-.167.222-.167.222-.167s.037.018.056.111l.074 1.17c.037.222.185.352.389.352h.797c.24 0 .37-.13.37-.37v-7.408c.019-3.174-2-5.142-5.224-5.142zm-.742 11.343c-1.556 0-2.427-.78-2.427-2.154 0-1.355.964-2.097 2.724-2.097h3.742v.204c-.019 2.562-1.482 4.047-4.039 4.047zM59.518 48.126c-1.464 0-2.63.52-3.483 1.503-.11.112-.185.112-.203.093-.019 0-.038-.074.018-.204.352-.984.426-1.764.426-2.933v-3.527c0-.26-.167-.427-.426-.427h-2.297c-.26 0-.426.167-.426.427v16.354c0 .204.111.353.315.464l2.26.928c1 .427 2.093.65 3.279.65 3.76 0 6.076-2.562 6.076-6.683.018-4.102-2.112-6.645-5.54-6.645zm-.408 2.858c1.779 0 2.853 1.411 2.853 3.787s-1.111 3.806-2.982 3.806a5.358 5.358 0 01-2.056-.427l-.686-.316v-3.156c0-2.357 1.056-3.694 2.871-3.694zM72.984 47.977c-1.444 0-2.611.483-3.5 1.448.314-.947.37-1.708.37-2.84v-3.397c0-.334-.222-.557-.556-.557h-2.297c-.333 0-.556.223-.556.557v16.076c0 .185.074.426.408.594l2.26.928c1.019.445 2.13.668 3.334.668 3.835 0 6.224-2.487 6.224-6.701 0-4.177-2.185-6.776-5.687-6.776zm-.407 3.137c1.704 0 2.704 1.355 2.704 3.639 0 2.339-1.037 3.545-2.834 3.545-.685 0-1.426-.148-2-.408l-.612-.279v-2.933c0-1.076.278-3.564 2.742-3.564zM83.97 57.203c0 .278.112.483.334.483h.148c.204-.056.353 0 .445.185l1.056 2.673c.074.205.019.353-.148.446a3.958 3.958 0 01-1.5.316c-2.76 0-4.428-1.523-4.428-4.103V42.965c0-.204.13-.334.333-.334h3.427c.204 0 .334.13.334.334v14.238zM95.457 61.028c-.908.204-1.778.278-2.445.278-4.224 0-6.502-2.562-6.502-6.609 0-4.084 2.334-6.627 6.39-6.627 3.224 0 5.206 1.708 5.206 4.363 0 2.06-1.445 3.248-4.409 3.657l-3.02.445c.482.91 1.13 1.281 2.335 1.281.537 0 1.278-.111 1.648-.186l1.538-.334c.204-.055.334.019.39.242l.722 2.673c.055.241-.056.352-.26.408l-1.593.409zm-2.371-7.537c.796-.075 1.186-.446 1.186-1.003 0-.612-.52-.928-1.538-.928-1.111 0-2.241.724-2.316 2.34l2.668-.41zM96.863 41.944a1.207 1.207 0 00-1.297-.594l-1.185.241.24 1.188c.075.316.242.594.52.761.203.13.425.205.666.205.056 0 .093 0 .148-.019-.055.056-.11.111-.148.167-.13.204-.166.427-.13.65a.965.965 0 00.371.557.917.917 0 00.482.148c.055 0 .11 0 .185-.018a.894.894 0 00.685-.706c.167.242.426.39.723.39.055 0 .111 0 .185-.019a.963.963 0 00.556-.37.907.907 0 00.13-.65.88.88 0 00-.593-.65c.593-.167.981-.78.852-1.392l-.241-1.188-1.186.24a1.219 1.219 0 00-.963 1.059zM107.756 37.414l-.204 1.838c-.111 1.003-1.019 1.745-2.019 1.634l-1.834-.204.204-1.838c.111-1.003 1.019-1.745 2.019-1.634l1.834.204zM106.107 42.722a1.305 1.305 0 01-1.426 1.151 1.307 1.307 0 01-1.149-1.429l.13-1.3 1.297.13a1.324 1.324 0 011.148 1.448zM99.994 36.578l-.204 1.838a1.847 1.847 0 001.63 2.023l1.834.205.204-1.838a1.847 1.847 0 00-1.63-2.024l-1.834-.204zM100.476 42.11a1.306 1.306 0 001.148 1.428 1.306 1.306 0 001.427-1.15l.129-1.3-1.296-.13c-.686-.074-1.334.446-1.408 1.151zM89.565 40.217a1.298 1.298 0 00-.222-.632 1.407 1.407 0 00-.871-.575l-1.352-.26-.26 1.355c-.13.724.315 1.43 1.038 1.597-.334.11-.612.39-.686.76a.991.991 0 00.797 1.152c.037 0 .055.018.092.018.223.019.463-.037.649-.167.13-.093.222-.204.296-.316.056.409.37.743.797.817.037 0 .055.019.092.019.223.018.463-.037.649-.167a.911.911 0 00.407-.631c.056-.26 0-.52-.148-.743-.056-.093-.13-.148-.204-.223h.056c.315.038.63-.055.908-.241.296-.204.5-.52.574-.872l.26-1.356-1.353-.26a1.43 1.43 0 00-1.52.725z" />
            <Path d="M100.067 48.31a1.521 1.521 0 00-.11-.706 1.524 1.524 0 00-.816-.78l-1.37-.52-.52 1.374a1.47 1.47 0 00.779 1.857c-.37.037-.723.278-.871.668-.204.538.055 1.15.611 1.355.037.019.056.019.093.037.24.075.481.056.704-.056.148-.074.278-.166.37-.278-.018.427.241.854.667 1.021.037.019.056.019.093.037.24.074.481.056.704-.056a1.06 1.06 0 00.556-.575c.092-.26.092-.557-.019-.798a.838.838 0 00-.185-.26c.018 0 .037.018.055.018.334.093.667.075.982-.074.352-.167.63-.445.778-.816l.519-1.374-1.371-.52a1.376 1.376 0 00-1.649.446zM91.103 45.805a1.188 1.188 0 00-.538-.465 1.446 1.446 0 00-1.111-.074l-1.39.464.464 1.393a1.446 1.446 0 001.778.928c-.26.278-.37.687-.24 1.058.184.557.777.854 1.333.65.037-.019.055-.019.093-.038.222-.092.407-.278.518-.5.074-.15.111-.298.111-.465.26.334.723.501 1.167.353.037-.019.056-.019.093-.037.222-.093.407-.279.519-.502.13-.26.148-.538.055-.798a1.088 1.088 0 00-.537-.612c-.093-.056-.204-.074-.315-.093.019 0 .037-.019.056-.019.315-.13.555-.37.722-.687.167-.352.204-.742.074-1.113l-.463-1.392-1.39.464c-.648.26-1.037.853-1 1.485zM105.432 57.075c0 .05-.049.098-.098.098h-1.331v3.755c0 .05-.049.099-.099.099h-.936a.106.106 0 01-.099-.1v-3.754h-1.331c-.049 0-.099-.049-.099-.098v-.79c0-.05.05-.1.099-.1h3.796c.049 0 .098.05.098.1v.79zM110.952 60.928c0 .05-.049.099-.098.099h-.937c-.049 0-.098-.05-.098-.1v-1.432c0-.494 0-1.235.098-1.729 0-.099-.049-.099-.098-.05-.05.198-.148.347-.198.544l-.394 1.038c-.049.197-.148.444-.246.691 0 .05-.05.099-.099.099h-.739c-.05 0-.099 0-.099-.099-.099-.247-.148-.494-.246-.691l-.395-1.038c-.049-.197-.148-.346-.197-.543-.049-.099-.099-.05-.099.05.05.493.099 1.185.099 1.728v1.433c0 .05-.049.099-.099.099h-.936c-.049 0-.099-.05-.099-.1v-4.643c0-.05.05-.099.099-.099h1.38c.049 0 .099 0 .*************.098.345.148.444l.345 1.037c.098.297.246.741.295 1.038 0 .*************** 0 .099-.297.197-.741.296-1.038l.345-1.037.148-.445c0-.05.049-.099.098-.099h1.381c.049 0 .098.05.098.1v4.643h.049z" />
          </G>
          <G clipPath="url(#clip1_4931_10947)">
            <Path
              d="M50.964 33.532c-.16.002-.32-.01-.48-.033-.64-.1-.78-.426-.8-.61-.066-.477.445-.99 1.562-1.57-2.907 1.276-5.822 1.754-8.442 1.356-4.146-.638-5.416-3.827-5.805-5.681-.655-3.127.27-6.933 1.94-7.987.736-.462 3.103-1.074 4.988-1.253-.023-.029-.047-.057-.068-.086-.788-1.001-1.404-2.154-1.647-3.082-.744-2.811 2.626-12.028 2.77-12.419.026-.068.62-1.594 1.88-1.78.895-.132 1.849.444 2.834 1.716 1.168 1.509 9.81 12.915 10.556 21.083.5-.714.936-1.472 1.3-2.264 2.814-6.186.78-12.385-2.063-16.12-.846-1.117-1.554-1.643-2.047-1.53-1.028.234-1.641 3.25-2.089 5.451a.319.319 0 01-.377.249.32.32 0 01-.25-.376c.669-3.291 1.245-5.643 2.574-5.946.788-.18 1.646.381 2.698 1.765 2.954 3.884 5.067 10.329 2.137 16.77a15.97 15.97 0 01-1.841 3.018v.338a5.069 5.069 0 011.044-.736c1.12-2.307 3.706-4.007 7.014-4.538 1.304-.21 1.94-.111 2.126.329.244.574-.603 1.275-.98 1.562a.32.32 0 01-.382-.512c.359-.266.704-.637.76-.797-.09-.038-.416-.118-1.423.045-2.88.46-5.162 1.845-6.296 3.736h.053c.45.067.832.393 1.166 1a.271.271 0 01.023.047c.32.891.145 1.695-.447 2.148a1.317 1.317 0 01-1.535.083c-.266-.179-.835-.74-.464-2.094-.226.182-.469.4-.728.638a6.948 6.948 0 01-.624 2.167c-1.981 4.08-6.428 5.913-8.637 5.913zm-.64-.744c.082.054.412.16 1.173.067 1.786-.227 5.662-1.666 7.533-5.502.174-.36.31-.738.405-1.126-1.68 1.623-3.971 3.826-7.423 5.42-1.408.663-1.652 1.052-1.69 1.142l.002-.001zm-5.876-14.431c-.52.032-1.039.092-1.553.178-1.56.25-3.082.675-3.62 1.013-1.302.82-2.31 4.196-1.655 7.317.355 1.688 1.511 4.608 5.278 5.18 3.39.517 7.318-.515 11.059-2.905a22.946 22.946 0 005.697-5.136c-.072-2.683-1.161-6.137-3.237-10.204-2.82-5.518-6.52-10.386-7.228-11.306-.82-1.06-1.592-1.57-2.24-1.477-.88.131-1.368 1.36-1.373 1.372-.034.09-3.446 9.416-2.756 12.038.225.859.8 1.935 1.556 2.88.117.149.229.281.336.4.667-.014 1.21.052 1.505.234a.607.607 0 01.32.591c-.054.608-.438.705-.6.718-.459.04-1.022-.409-1.489-.893zM61.815 24.3c-.092.214-.17.433-.233.657-.196.682-.122 1.216.196 1.435a.683.683 0 00.785-.06c.357-.275.447-.798.242-1.407-.227-.405-.453-.619-.688-.654a.699.699 0 00-.302.029zm-2.194.748a18.95 18.95 0 01-.666.75l.082-.078c.2-.194.392-.38.574-.551l.01-.121zm-14.217-6.684c.132.122.293.208.468.25a.416.416 0 00.028-.124 1.121 1.121 0 00-.495-.126z"
              fill={props.fill}
            />
          </G>
          <Defs>
            <ClipPath id="clip0_4931_10947">
              <Path
                fill="#fff"
                transform="translate(0 36.58)"
                d="M0 0H110.952V31.1121H0z"
              />
            </ClipPath>
            <ClipPath id="clip1_4931_10947">
              <Path
                fill="#fff"
                transform="translate(36.416)"
                d="M0 0H34.418V33.8464H0z"
              />
            </ClipPath>
          </Defs>
        </Svg>
      )
}

export default MyRabbleIconManage;

