import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"

function OnboardingW4(props: SvgProps) {
  return (
    <Svg
    //   xmlns="http://www.w3.org/2000/svg"
      width={225}
      height={229}
      viewBox="0 0 225 229"
      fill="none"
      {...props}
    >
      <Path
        d="M174.514 73.395a1.913 1.913 0 01-.923-.703 2.054 2.054 0 01-.054-2.272c.216-.338.526-.6.888-.749l.309-.454-.003-.14c-.117-4.719-14.741 2.364-16.613 5.993a16.22 16.22 0 00-1.636 5.149 65.636 65.636 0 013.987-26.526 62.154 62.154 0 012.702-6.2 64.2 64.2 0 012.693-4.822c5.249-8.493 12.353-15.565 20.731-20.637 3.151 1.495 6.165 2.13 8.867-4.272.48-1.139 1.678-1.807 2.285-2.875a14.875 14.875 0 01-.96-.287l-.299-.103-.037-.013a1.892 1.892 0 01-.872-.64 2.01 2.01 0 01-.398-1.033 2.027 2.027 0 01.203-1.092c.167-.336.423-.615.739-.802l.589-.35c.3-.18.603-.354.902-.536a.82.82 0 00.095-.052l1.03-.611a5.801 5.801 0 00-1.038-2.353c-1.794-2.364-5.367-2.682-7.864-1.17-2.502 1.509-3.967 4.456-4.405 7.433a23.75 23.75 0 00.373 7.684c-.189.117-.381.227-.57.344a61.822 61.822 0 00-9.691 7.474c1.924-3.687 1.329-20.066.184-23.949-1.375-4.664-7.503-5.36-9.666-1.033l-.062.125c.261.333.511.675.748 1.026.298.444.461.972.466 1.513a2.775 2.775 0 01-.434 1.524c-.289.45-.701.8-1.183 1.001a2.48 2.48 0 01-1.519.13l-.052-.012c-6.966 4.851.913 28.038 5.96 25.599a65.963 65.963 0 00-8.936 14.211 63.894 63.894 0 00-3.454 9.382l.026-.116c1.734-7.11-8.198-22.76-12.511-23.544-2.484-.451-4.713-1.483-5.267 1.084l-.027.123a17.733 17.733 0 012.219 2.67 2.776 2.776 0 01.031 3.036 2.6 2.6 0 01-1.183 1.002 2.477 2.477 0 01-1.518.13l-.052-.012-.107-.026c-4.767 6.489 5.943 24.433 16.534 24.407l.005.002a68.373 68.373 0 00-.536 13.588l14.064 5.71c.112-.142.22-.292.326-.437a16.837 16.837 0 01-3.802-1.822c1.542-.912 3.088-1.834 4.63-2.746.032-.016.064-.033.095-.053.784-.468 1.572-.93 2.356-1.399h.001a27.096 27.096 0 001.634-6.499zm-3.775-39.59l.011-.008-.015.018.004-.01zm-14.045 28.032l-.015-.334c.081-.203.158-.408.231-.62.022-.058.033-.12.055-.178-.09.382-.185.76-.27 1.144l-.001-.012z"
        fill="#F1F2F2"
      />
      <Path
        d="M152.361 76.036c-.335-41.533-32.7-75.1-72.59-75.123C39.539.89 7.083 35.32 7.083 77.27v137.566c0 3.552 1.353 6.958 3.76 9.469 2.408 2.512 5.673 3.923 9.077 3.923h132.442c40.117 0 72.639-34.58 72.639-76.43 0-41.849-32.522-75.762-72.639-75.762z"
        fill="#F1F5F9"
      />
      <Path
        d="M111.288 161.56c1.149-.187 2.106-.412 2.801-.659.696-.247 1.114-.51 1.224-.77.111-.261-.088-.513-.583-.738-.495-.225-1.273-.418-2.281-.565l1.581-11.019.202-1.409c.208-1.449-8.956-1.97-15.074-.856l-6.574 1.196 3.107 3.378.344 1.248 2.211 8.029c-1.548.38-2.38.829-2.34 1.261.04.433.95.819 2.557 1.086 1.608.267 3.801.395 6.165.362 2.364-.034 4.733-.227 6.66-.544z"
        fill="#A0616A"
      />
      <Path
        opacity={0.1}
        d="M152.361 75.382c-.335-41.533-32.7-75.1-72.59-75.122-21.81 0-41.329 10.105-54.642 26.054 12.605-10.14 28.08-15.628 43.997-15.602 39.889.022 72.254 33.59 72.589 75.122 40.118 0 72.64 33.926 72.64 75.776.024 18.254-6.293 35.9-17.781 49.669 8.848-7.069 16.015-16.17 20.945-26.596 4.93-10.427 7.49-21.899 7.481-33.525 0-41.85-32.522-75.776-72.639-75.776z"
        fill="#000"
      />
      <Path
        d="M84.596 121.865c-2.96-9.842-9.692-18.869-19.002-22.196-9.311-3.327-22.944 1.741-29.944 9.018-12.803 13.312-13.455 62.094-4.659 76.545 1.75-.096 7.787-.168 9.558-.237l2.505-8.71v8.624c13.822-.446 29.207 11.414 42.61.122 1.291-10.231 1.892-53.324-1.068-63.166z"
        fill="#2F2E41"
      />
      <Path
        d="M64.417 152.746c11.028 0 19.967-9.326 19.967-20.83 0-11.504-8.94-20.83-19.967-20.83-11.028 0-19.968 9.326-19.968 20.83 0 11.504 8.94 20.83 19.968 20.83z"
        fill="#D2976B"
      />
      <Path
        d="M78.137 104.792c-7.627-4.851-17.793-4.991-25.54-.352-7.748 4.64-12.759 13.867-12.59 23.185 11.16.535 22.738 11.47 34.775.699l2.81-7.174 1.656 7.179c3.623 0 7.252-.005 10.888-.015.405-9.309-4.371-18.672-11.999-23.522z"
        fill="#2F2E41"
      />
      <Path
        d="M82.595 118.916c4.582-9.141 12.738-16.783 22.466-18.34a19.987 19.987 0 017.674.404c14.323 3.354 23.284 18.248 20.484 33.28-.555 2.982-1.204 6.067-1.667 9.492a8.837 8.837 0 01-1.148 3.3 8.468 8.468 0 01-2.315 2.547 8.066 8.066 0 01-3.099 1.37 7.906 7.906 0 01-3.365-.035l-.053-.012-2.451-.547c-7.284-1.609-15.619.209-23.522.11-11.779-.148-20.057-12.142-16.301-23.789.9-2.68 2.003-5.281 3.297-7.78z"
        fill="#E6E7E8"
      />
      <Path
        d="M100.752 153.361c11.028 0 19.968-9.326 19.968-20.83 0-11.504-8.94-20.83-19.968-20.83s-19.968 9.326-19.968 20.83c0 11.504 8.94 20.83 19.968 20.83z"
        fill="#A0616A"
      />
      <Path
        d="M91.847 103.303c8.329-3.367 18.358-1.629 25.197 4.366 6.839 5.996 10.207 16.003 8.461 25.142-11.076-1.532-24.325 7.095-34.345-5.728l-1.55-7.579-2.847 6.761a4618.26 4618.26 0 01-10.714-2.024c1.18-9.237 7.468-17.571 15.798-20.938z"
        fill="#E6E7E8"
      />
      <Path
        d="M148.624 154.598a7.991 7.991 0 00-3.171-1.601 7.787 7.787 0 00-3.522-.052 7.969 7.969 0 00-3.213 1.508 8.397 8.397 0 00-2.299 2.784l-56.362 20.657 5.592 12.4 54.565-21.214a7.86 7.86 0 005.795.384c1.911-.617 3.54-1.943 4.576-3.728a8.832 8.832 0 001.048-5.958c-.361-2.053-1.432-3.896-3.009-5.18z"
        fill="#D2976B"
      />
      <Path
        d="M88.396 168.219l-8.326 3.758-4.45 2.011-2.904 1.308a9.226 9.226 0 00-3.382 1.757c-1.714 1.397-2.913 3.368-3.396 5.58a10.25 10.25 0 00.743 6.564c.964 2.033 2.57 3.658 4.548 4.601a9.099 9.099 0 006.313.572l17.04-2.031c.485-.059.952-.227 1.369-.491A3.468 3.468 0 0097 190.809c.563-.858 5.823-3.444 5.569-4.447l2.251-1.077 3.267-1.068 12.266-5.886-1.855-7.155 1.716-8.784c-.257-.99-28.088 6.33-28.977 5.892a3.28 3.28 0 00-1.414-.358 3.274 3.274 0 00-1.427.293z"
        fill="#3F3D56"
      />
      <Path
        d="M112.263 227.177H34.415c-3.23 0-6.327-1.338-8.61-3.72-2.284-2.383-3.567-5.614-3.567-8.983v-29.898c0-3.369 1.283-6.6 3.566-8.982 2.284-2.383 5.382-3.721 8.611-3.721l9.959-14.638h34.76l33.129 14.638c1.599 0 3.183.328 4.66.967a12.154 12.154 0 013.951 2.754 12.746 12.746 0 012.64 4.121c.611 1.541.926 3.193.926 4.861v29.898c0 1.668-.315 3.32-.926 4.861a12.76 12.76 0 01-2.64 4.122 12.166 12.166 0 01-3.951 2.753 11.73 11.73 0 01-4.66.967z"
        fill="#3F3D56"
      />
      <Path
        d="M140.133 227.177H70.505c-1.599 0-3.182-.328-4.66-.967a12.158 12.158 0 01-3.95-2.753 12.762 12.762 0 01-2.64-4.122 13.179 13.179 0 01-.927-4.861v-29.898c0-3.369 1.283-6.6 3.567-8.982 2.283-2.383 5.38-3.721 8.61-3.721l18.335-14.638H123.6l16.532 14.638c3.229 0 6.327 1.338 8.61 3.721 2.284 2.382 3.567 5.613 3.567 8.982v29.898c0 3.369-1.283 6.6-3.567 8.983-2.283 2.382-5.381 3.72-8.61 3.72z"
        fill="#FF8E1C"
      />
      <Path
        d="M77.568 223.47a7.948 7.948 0 01-2.502 2.383 7.577 7.577 0 01-3.234 1.054 7.52 7.52 0 01-3.358-.471 7.801 7.801 0 01-2.85-1.91l-48.637 1.641-6.218.21c-6.397.215-10.081-7.505-6.06-12.699l4.319-5.581 15.45 2.528 5.587.255 35.945 1.643a7.668 7.668 0 015.242-2.01 7.687 7.687 0 015.198 2.128 8.328 8.328 0 012.532 5.193 8.467 8.467 0 01-1.414 5.636z"
        fill="#D2976B"
      />
      <Path
        d="M34.666 160.236s-9.27-2.454-13.665 5.44c-3.518 6.319-15.484 30.22-17.969 40.03-12.12 30.472 16.061 18.337 15.273 21.212l9.428-16.145c4.096-2.136-3.789.621-4.828-.911l7.227-8.194 8.194-4.273 3.006-33.458-6.666-3.701z"
        fill="#3F3D56"
      />
      <Path
        d="M68.653 224.744a7.955 7.955 0 002.503 2.382 7.592 7.592 0 003.233 1.055 7.535 7.535 0 003.358-.472 7.786 7.786 0 002.851-1.909l48.636 1.64 6.219.21c6.396.216 10.081-7.504 6.06-12.699l-4.319-5.581-15.451 2.528-5.586.255-35.946 1.644a7.674 7.674 0 00-5.241-2.011 7.683 7.683 0 00-5.199 2.129c-1.424 1.349-2.325 3.197-2.531 5.193a8.468 8.468 0 001.413 5.636z"
        fill="#A0616A"
      />
      <Path
        d="M111.555 161.509s9.271-2.454 13.665 5.44c3.518 6.32 15.485 30.221 17.969 40.03 12.121 30.473-16.061 18.338-15.272 21.212l-9.428-16.144c-4.097-2.137 3.789.621 4.827-.911l-7.227-8.194-8.193-4.274-3.006-33.458 6.665-3.701zM176.405 128.26c-.095 0-.19-.006-.284-.02-.38-.061-.463-.257-.474-.368-.04-.288.263-.597.925-.947-1.723.769-3.452 1.058-5.005.817-2.458-.384-3.211-2.307-3.441-3.426-.389-1.885.159-4.18 1.15-4.816.436-.279 1.84-.648 2.957-.756-.013-.017-.028-.034-.04-.052-.468-.604-.833-1.299-.977-1.858-.441-1.696 1.557-7.254 1.643-7.49.015-.041.367-.961 1.114-1.073.531-.08 1.096.267 1.68 1.035.692.909 5.816 7.788 6.259 12.714.297-.431.555-.888.771-1.365 1.668-3.731.462-7.47-1.224-9.722-.501-.674-.921-.991-1.213-.923-.61.142-.973 1.96-1.239 3.288a.19.19 0 01-.224.15.191.191 0 01-.147-.227c.396-1.985.737-3.403 1.525-3.586.468-.108.976.23 1.6 1.065 1.752 2.342 3.004 6.229 1.267 10.113a9.596 9.596 0 01-1.092 1.821v.204c.188-.174.396-.324.62-.445.663-1.391 2.197-2.416 4.158-2.736.773-.127 1.15-.068 1.26.198.145.346-.357.769-.58.942a.189.189 0 01-.266-.039.19.19 0 01-.035-.143.183.183 0 01.025-.071.187.187 0 01.049-.056c.213-.16.417-.384.451-.48-.054-.023-.247-.071-.844.027-1.707.277-3.06 1.112-3.733 2.253h.032c.266.04.493.237.691.602.005.01.01.019.013.029.19.538.086 1.022-.264 1.295a.77.77 0 01-.911.05c-.157-.107-.495-.446-.275-1.262-.134.11-.278.241-.431.385a4.265 4.265 0 01-.37 1.306c-1.175 2.461-3.811 3.567-5.121 3.567zm-.379-.449c.048.032.244.096.695.04 1.059-.136 3.357-1.005 4.466-3.318.103-.217.184-.445.24-.679-.995.979-2.354 2.308-4.401 3.269-.834.399-.979.634-1.002.689l.002-.001zm-3.484-8.703a8.627 8.627 0 00-.921.107c-.924.15-1.827.407-2.146.611-.772.495-1.369 2.531-.981 4.413.21 1.018.896 2.778 3.129 3.124 2.009.311 4.338-.311 6.556-1.752a13.662 13.662 0 003.378-3.098c-.043-1.618-.688-3.701-1.919-6.153-1.672-3.328-3.865-6.264-4.285-6.819-.487-.639-.944-.947-1.328-.89-.522.079-.812.82-.815.827-.019.055-2.042 5.679-1.633 7.259.133.519.474 1.168.922 1.738.069.089.136.169.199.241.396-.008.718.031.893.141a.36.36 0 01.189.356c-.032.367-.259.425-.355.433-.272.024-.606-.246-.883-.538zm10.296 3.584c-.054.129-.1.262-.138.396-.116.412-.072.734.116.866a.4.4 0 00.466-.037c.212-.165.265-.48.143-.848-.134-.244-.268-.373-.407-.394a.406.406 0 00-.18.017zm-1.301.451a11.1 11.1 0 01-.394.453l.048-.047c.119-.117.232-.229.341-.333l.005-.073zm-8.428-4.031a.609.609 0 00.277.151.273.273 0 00.017-.075.659.659 0 00-.294-.076z"
        fill="#FF8E1C"
      />
    </Svg>
  )
}

export default OnboardingW4;
