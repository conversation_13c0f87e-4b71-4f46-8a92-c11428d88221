import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"

function HeartHealth(props: SvgProps) {
  return (
    <Svg
      width={18}
      height={25}
      viewBox="0 0 18 25"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M.576 6.425c.242.54.442 1.1.598 1.673.018.115.064.426.152.849a4.152 4.152 0 012.249-.93l.291-.04c-.005-.85.095-1.698.298-2.522a7.72 7.72 0 01.519-1.399c-.099-.573-.628-.955-1.181-.852a1.012 1.012 0 00-.613.375c-.134.17-.213.302-.478 1.438-.027.109-.05.217-.07.326-.426-.906-.525-1.009-.68-1.142a.997.997 0 00-1.435.16c-.293.376-.302.91-.023 1.296.03.055.134.248.373.768z"
        fill="#E81448"
      />
      <Path
        d="M16.516 13.282c-.732-2.453-1.992-4.257-2.48-4.758a5.155 5.155 0 00-.938-.704c-.1.082-.199.17-.292.26-.292.272-.61 1.03-.823 1.979a.588.588 0 01-.565.468.668.668 0 01-.134 0 .605.605 0 01-.436-.726v-.002c.193-.87.558-2.03 1.167-2.607a7.15 7.15 0 012.547-1.571 1.18 1.18 0 00.67-.604c.298-.592.075-1.322-.497-1.63a1.13 1.13 0 00-.923-.068 9.454 9.454 0 00-3.345 2.054c-1.208 1.115-1.75 3.079-1.963 4.148a.584.584 0 01-.69.47.604.604 0 01-.454-.715c.292-1.486.945-3.528 2.334-4.806a11.302 11.302 0 011.872-1.414 3.26 3.26 0 00-.624-.604c.035-.145.073-.302.117-.44.175-.605.25-.81.271-.862a.77.77 0 00-.283-1.027.711.711 0 00-.898.157c-.096.112-.166.194-.487 1.296-.035.121-.067.242-.097.363h-.064v-.11c0-.633.02-.851.027-.905a.756.756 0 00-.549-.905.722.722 0 00-.822.415c-.062.136-.108.233-.114 1.384v.402l-.123.054a12.452 12.452 0 01-.157-.861.741.741 0 00-.742-.742.734.734 0 00-.69.555c-.032.148-.056.25.175 1.377.043.202.092.403.149.604a5.97 5.97 0 00-1.365 2.54c-.312 1.185-.26 2.179-.292 3.324 0 .057-.096.027-.096.027-.391.007-.78.042-1.167.106-1.038.178-1.56.48-2.144 1.18-.583.702-1.324 1.865-1.408 3.98A6.328 6.328 0 001.27 18.25a20.046 20.046 0 001.69 2.154c.413.393.846.763 1.297 1.108a14.598 14.598 0 005.367 2.514c1.887.38 4.553.103 5.477-.55.925-.652 1.727-2.631 1.896-3.978.27-2.084.107-4.204-.481-6.217z"
        fill="#E81448"
      />
    </Svg>
  )
}

export default HeartHealth;
