import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

function GlobeIcon(props: SvgProps) {
  return (
    <Svg width={22} height={23} viewBox="0 0 22 23" fill="none" {...props}>
      <Path
        d="M19.745 14.25h-4.162a1.834 1.834 0 00-1.833 1.833v4.162M6.417 3.563v1.521a2.75 2.75 0 002.75 2.75A1.833 1.833 0 0111 9.668a1.833 1.833 0 003.667 0c0-1.009.825-1.834 1.833-1.834h2.906M10.083 20.622V17a1.833 1.833 0 00-1.833-1.834 1.833 1.833 0 01-1.833-1.833v-.917a1.833 1.833 0 00-1.834-1.833H1.88"
        stroke="#191D23"
        strokeWidth={1.83333}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M11 20.667a9.167 9.167 0 100-18.333 9.167 9.167 0 000 18.333z"
        stroke="#191D23"
        strokeWidth={1.83333}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

export default GlobeIcon;
