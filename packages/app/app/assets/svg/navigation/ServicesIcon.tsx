import * as React from 'react';
import Svg, { Path, Circle, SvgProps } from 'react-native-svg';

export default function ServicesIcon(props: SvgProps) {
  return (
    <Svg width={22} height={22} viewBox="0 0 22 22" fill="none" {...props}>
      <Path
        d="M5.745 13.781l2.993-3.89 3.414 2.682 2.93-3.78"
        stroke="#04004F"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Circle
        cx={18.4954}
        cy={3.20003}
        r={1.9222}
        stroke="#04004F"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M13.425 2.12H6.157c-3.011 0-4.879 2.133-4.879 5.144v8.083c0 3.011 1.831 5.135 4.879 5.135h8.604c3.012 0 4.879-2.124 4.879-5.135v-7.04"
        stroke="#04004F"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}
