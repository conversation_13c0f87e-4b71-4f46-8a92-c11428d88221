import * as React from "react"
import Svg, { <PERSON>, <PERSON>, <PERSON>, Defs, Ellipse } from "react-native-svg"
/* SVGR has dropped some elements not supported by react-native-svg: filter */

type Props = {

}

function LearnIcon(props: Props) {
  return (
    <Svg
    width={21}
    height={22}
    viewBox="0 0 21 22"
    fill="none"
    {...props}
  >
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.066 5.64a.6.6 0 00-.6.6v12.248a.6.6 0 00.6.6H7.67c.617 0 1.194.213 1.608.571.41.356.621.817.622 1.277v.002a.6.6 0 101.2 0c0-.46.21-.923.621-1.279.414-.358.992-.57 1.609-.57h6.604a.6.6 0 00.6-.6V6.238a.6.6 0 00-.6-.6h-5.66c-1.135 0-2.237.39-3.062 1.104-.278.24-.517.512-.712.807a3.97 3.97 0 00-.713-.807C8.963 6.029 7.86 5.64 6.727 5.64H1.065zM11.1 18.62a3.697 3.697 0 012.23-.732h6.004V6.84h-5.06c-.868 0-1.686.299-2.276.81-.588.509-.898 1.179-.898 1.856v9.113zm-1.2 0V9.505c0-.677-.31-1.347-.898-1.856-.59-.511-1.408-.81-2.276-.81h-5.06v11.048H7.67c.811 0 1.604.256 2.23.731z"
      fill="#04004F"
    />
    <Ellipse
      cx={10.4999}
      cy={2.75192}
      rx={1.81339}
      ry={1.75192}
      stroke="#04004F"
      strokeWidth={1.2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
  )
}

export default LearnIcon;

