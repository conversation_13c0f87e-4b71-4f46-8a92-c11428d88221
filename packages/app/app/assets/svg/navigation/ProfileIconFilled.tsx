import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export default function ProfileIconFilled(props: SvgProps) {
  return (
    <Svg width={17} height={20} viewBox="0 0 17 20" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.794 5.291A5.274 5.274 0 018.5 10.583a5.275 5.275 0 01-5.294-5.292A5.274 5.274 0 018.5 0a5.273 5.273 0 015.294 5.291zM8.5 20c-4.338 0-8-.705-8-3.425 0-2.721 3.685-3.401 8-3.401 4.339 0 8 .705 8 3.425 0 2.721-3.685 3.401-8 3.401z"
        fill="#FF8E1C"
      />
    </Svg>
  );
}
