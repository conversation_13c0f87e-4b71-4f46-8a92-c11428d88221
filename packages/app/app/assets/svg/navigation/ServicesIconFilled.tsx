import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export default function ServicesIconFilled(props: SvgProps) {
  return (
    <Svg width={22} height={21} viewBox="0 0 22 21" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.439 2.53A2.532 2.532 0 0118.969 0 2.532 2.532 0 0121.5 2.53a2.532 2.532 0 01-2.53 2.53 2.532 2.532 0 01-2.531-2.53zm-4.043 10.867l3.035-3.915-.042.02a.786.786 0 00.084-.787.774.774 0 00-.64-.462.807.807 0 00-.736.326l-2.54 3.286-2.909-2.289a.83.83 0 00-.599-.168.813.813 0 00-.525.314l-3.106 4.044-.065.094a.784.784 0 00.221.998.882.882 0 00.483.157.744.744 0 00.62-.315l2.635-3.392 2.992 2.248.095.062a.785.785 0 00.997-.221zm2.226-11.528a4.977 4.977 0 00-.063.788 4.267 4.267 0 004.263 4.272c.263 0 .515-.03.777-.072v8.472c0 3.56-2.1 5.671-5.67 5.671H6.171C2.6 21 .5 18.89.5 15.329V7.56c0-3.57 2.1-5.69 5.671-5.69h8.451z"
        fill="#FF8E1C"
      />
    </Svg>
  );
}
