import * as React from "react"
import Svg, { <PERSON>, <PERSON>, Circle, Defs, Ellipse } from "react-native-svg"
/* SVGR has dropped some elements not supported by react-native-svg: filter */

type Props = {

}

function LearnIconFilled(props: Props) {
  return (
    <Svg
    width={21}
    height={21}
    viewBox="0 0 21 21"
    fill="none"
    {...props}
  >
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.893 7.155a3.76 3.76 0 00-.564-.655A4.174 4.174 0 006.5 5.418h-6v13.854h7a3.13 3.13 0 012.121.812c.**************.217.223l.055-13.152zm1.145 13.339c.1-.134.214-.26.34-.377a3.127 3.127 0 012.122-.814h7V5.418h-6c-1.061 0-2.079.39-2.829 1.084a3.768 3.768 0 00-.579.679l-.054 13.313z"
      fill="#FF8E1C"
    />
    <Path
      d="M10.5 0a2.383 2.383 0 00-2.382 2.382 2.383 2.383 0 004.764 0A2.383 2.383 0 0010.5 0z"
      fill="#FF8E1C"
    />
  </Svg>
  )
}

export default LearnIconFilled;

