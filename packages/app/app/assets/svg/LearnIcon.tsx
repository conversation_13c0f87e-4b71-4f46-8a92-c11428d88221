import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"

function LearnIcon(props: SvgProps) {
  return (
    <Svg
      width={31}
      height={29}
      viewBox="0 0 31 29"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M4.23 23.22h20.54v1.735H4.23V23.22zM4.23 5.306h2.416v16.76H4.229V5.305zM19.334 19.754h5.437v2.311h-5.438v-2.311zM19.334 9.93h5.437v8.668h-5.438V9.929zM19.334 6.462h5.437v2.312h-5.438V6.462zM15.709 11.663a.593.593 0 01-.605-.578c0-.318.272-.578.605-.578.332 0 .604.26.604.578a.593.593 0 01-.604.578zm0 9.246a.593.593 0 01-.605-.578c0-.317.272-.578.605-.578.332 0 .604.26.604.578a.593.593 0 01-.604.578zm2.416-11.558h-4.833v12.714h4.833V9.351zM10.875 9.93H9.063V8.195h1.812v1.733zm1.208-2.89H7.854v15.025h4.23V7.04z"
        fill="#E81448"
      />
    </Svg>
  )
}

export default LearnIcon
