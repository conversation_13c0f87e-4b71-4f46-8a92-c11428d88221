import * as React from "react";
import Svg, { Path, Text as SvgText, SvgProps } from "react-native-svg";

function MissingQuestionPrompts(props: SvgProps) {
  const message = "Not quite. Let’s correct the question we missed!";
  const maxLineWidth = 160; // Define a max width for the text box
  const fontSize = 18;
  const lineHeight = fontSize * 1.5;
  const textPadding = 5;

  // Split the message into lines that fit within maxLineWidth
  const lines = React.useMemo(() => {
    const words = message.split(" ");
    const lines: string[] = [];
    let currentLine = "";

    words.forEach((word) => {
      if ((currentLine + word).length * fontSize * 0.6 < maxLineWidth) {
        currentLine += word + " ";
      } else {
        lines.push(currentLine.trim());
        currentLine = word + " ";
      }
    });

    if (currentLine) lines.push(currentLine.trim());
    return lines;
  }, [message, maxLineWidth]);

  const bubbleHeight = Math.max(68, textPadding * 2 + lines.length * lineHeight); // Dynamically calculate height

  // Calculate starting vertical position to center the text vertically
  const totalTextHeight = lines.length * lineHeight;
  const startY = (bubbleHeight - totalTextHeight) / 2 + textPadding;

  return (
    <Svg width={260} height={bubbleHeight} viewBox={`0 0 260 ${bubbleHeight}`} fill="none" {...props}>
      <Path
        d="M1 15.89c0-7.731 6.268-14 14-14h191.524c7.903 0 14.24 6.538 13.993 14.438l-1.562 50c-.237 7.558-6.432 13.563-13.994 13.563H68.914a4 4 0 00-2.23.68L43.175 96.363c-1.612 1.084-3.667-.536-2.99-2.357l3.246-8.721c.972-2.614-.96-5.395-3.75-5.395H15c-7.732 0-14-6.268-14-14v-50z"
        stroke="#B4DDFF"
        strokeWidth={2}
      />
      {lines.map((line, index) => (
        <SvgText
          key={index}
          x="50%" // Position the text horizontally in the center
          y={startY + index * lineHeight} // Adjust vertical position for each line
          textAnchor="middle" // Center the text horizontally
          fill="#004987" // Text color
          fontSize={fontSize} // Font size
          fontWeight="500" // Font weight
          fontFamily="Montserrat"
          fontStyle="normal"
          fontFeatureSettings="'liga' off, 'clig' off"
        >
          {line}
        </SvgText>
      ))}
    </Svg>
  );
}

export default MissingQuestionPrompts;
