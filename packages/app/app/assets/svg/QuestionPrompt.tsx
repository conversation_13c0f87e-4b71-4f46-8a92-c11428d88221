import * as React from "react";
import Svg, { Path, Text as SvgText, SvgProps } from "react-native-svg";

interface QuestionPromptProps extends SvgProps {
  message?: string; 
  bubbleWidth?: number; 
  dx?: number;
}

const QuestionPrompt = ({
  message = "Hi there! I’m Belle!",
  bubbleWidth = 240, 
  dx = 0,
  ...props
}: QuestionPromptProps) => {
  const fontSize = 18;
  const lineHeight = fontSize * 1.5;
  const textPadding = 5;

  const lines = React.useMemo(() => {
    const words = message.split(" ");
    const maxTextWidth = bubbleWidth - textPadding * 2; 
    const lines: string[] = [];
    let currentLine = "";

    words.forEach((word) => {
      if ((currentLine + word).length * fontSize * 0.6 < maxTextWidth) {
        currentLine += word + " ";
      } else {
        lines.push(currentLine.trim());
        currentLine = word + " ";
      }
    });
    if (currentLine) lines.push(currentLine.trim());
    return lines;
  }, [message, bubbleWidth]);

  const bubbleHeight = Math.max(68, 20 + lines.length * lineHeight);

  return (
    <Svg
      width={bubbleWidth}
      height={bubbleHeight}
      viewBox={`0 0 ${bubbleWidth} ${bubbleHeight}`}
      fill="none"
      {...props}
    >
      <Path
        d={`M20 1H${bubbleWidth - 5}a4 4 0 014 4v${bubbleHeight - 10}a4 4 0 01-4 4H20a4 4 0 01-4-4V43.016a2 2 0 00-2.71-1.87L2.702 45.823c-.92.35-1.738-.7-1.176-1.507L17.184 21.87a3 3 0 00.54-1.716V5a4 4 0 014-4z`}
        stroke="#B4DDFF"
        strokeWidth={2}
      />
      {lines.map((line, index) => (
        <SvgText
          key={index}
          x="50%"
          y={30 + index * lineHeight}
          textAnchor="middle"
          fill="#004987"
          fontSize={fontSize}
          fontWeight="400"
          fontFamily="Montserrat"
          fontStyle="normal"
          fontFeatureSettings="'liga' off, 'clig' off"
          dx={dx}
        >
          {line}
        </SvgText>
      ))}
    </Svg>
  );
};

export default QuestionPrompt;
