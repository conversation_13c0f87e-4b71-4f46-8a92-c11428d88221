import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

function LockIcon(props: SvgProps) {
  return (
    <Svg width={23} height={24} viewBox="0 0 23 24" fill="none" {...props}>
      <Path
        d="M18.208 11.042H4.792a1.917 1.917 0 00-1.917 1.917v6.708c0 1.058.858 1.917 1.917 1.917h13.416a1.917 1.917 0 001.917-1.917v-6.708a1.917 1.917 0 00-1.917-1.917zM6.708 11.042V7.209a4.792 4.792 0 119.584 0v3.833"
        stroke="#191D23"
        strokeWidth={1.91667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

export default LockIcon;
