import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
  width?: number;
  height?: number;
}

function ButterFlyIcon({ width = 200, height=134, ...props}: IconProps) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox={`0 0 136 ${height}`}
      fill="none"
      // xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M57.155 134a12.65 12.65 0 01-1.934-.136c-2.58-.405-3.147-1.72-3.224-2.461-.271-1.932 1.792-4 6.293-6.345-11.716 5.154-23.471 7.086-34.034 5.476-16.714-2.577-21.833-15.462-23.4-22.955-2.644-12.633 1.083-28.011 7.821-32.27 2.967-1.868 12.51-4.342 20.106-5.063-.09-.116-.187-.232-.27-.348-3.18-4.046-5.662-8.704-6.643-12.453C18.872 46.087 32.459 8.843 33.04 7.265c.103-.277 2.495-6.443 7.577-7.19 3.61-.535 7.454 1.791 11.426 6.932 4.707 6.095 39.547 52.184 42.558 85.188a58.849 58.849 0 005.243-9.148c11.342-24.997 3.14-50.045-8.318-65.14-3.412-4.51-6.268-6.635-8.254-6.178-4.146.947-6.616 13.13-8.422 22.027a1.29 1.29 0 01-2.527-.516c2.695-13.297 5.016-22.8 10.375-24.023 3.179-.728 6.635 1.54 10.878 7.132 11.91 15.693 20.428 41.733 8.615 67.76a64.548 64.548 0 01-7.422 12.196v1.366a20.416 20.416 0 014.21-2.976c4.515-9.323 14.941-16.19 28.276-18.335 5.256-.85 7.822-.451 8.57 1.327.987 2.319-2.431 5.154-3.946 6.313a1.295 1.295 0 01-1.806-.264 1.289 1.289 0 01.265-1.804c1.444-1.076 2.837-2.577 3.063-3.221-.368-.155-1.677-.477-5.739.18-11.607 1.862-20.809 7.454-25.381 15.095h.213c1.812.27 3.353 1.591 4.701 4.04.036.06.067.125.09.193 1.29 3.601.587 6.848-1.799 8.677a5.302 5.302 0 01-6.19.335c-1.07-.721-3.366-2.989-1.87-8.458-.91.734-1.89 1.617-2.934 2.577a28.137 28.137 0 01-2.515 8.755C83.986 126.591 66.06 134 57.156 134zm-2.579-3.009c.329.219 1.657.644 4.727.271 7.202-.915 22.826-6.733 30.37-22.233a23.148 23.148 0 001.632-4.548c-6.77 6.558-16.01 15.461-29.926 21.904-5.674 2.673-6.661 4.245-6.816 4.613l.013-.007zm-23.69-58.31a60.214 60.214 0 00-6.262.721c-6.287 1.005-12.426 2.726-14.592 4.091C4.781 80.811.72 94.45 3.357 107.058c1.431 6.822 6.093 18.618 21.279 20.931 13.664 2.087 29.5-2.081 44.583-11.738A92.577 92.577 0 0092.19 95.5c-.29-10.843-4.682-24.797-13.051-41.231C67.769 31.97 52.854 12.303 49.998 8.585 46.69 4.301 43.582 2.24 40.97 2.62c-3.546.528-5.52 5.495-5.539 5.54-.135.367-13.89 38.05-11.11 48.64.909 3.473 3.224 7.821 6.274 11.642.47.599.922 1.134 1.354 1.617 2.689-.058 4.881.206 6.068.94a2.45 2.45 0 011.29 2.39c-.22 2.455-1.767 2.848-2.419 2.9-1.85.16-4.12-1.65-6.003-3.608zM100.9 96.698c-.369.864-.683 1.75-.941 2.654-.787 2.758-.49 4.916.793 5.799a2.75 2.75 0 003.166-.245c1.438-1.108 1.799-3.221.974-5.682-.916-1.637-1.825-2.5-2.773-2.642a2.823 2.823 0 00-1.219.116zm-8.847 3.022a76.357 76.357 0 01-2.682 3.034l.329-.316c.806-.786 1.58-1.533 2.315-2.229l.038-.49zM34.741 72.713c.532.492 1.18.84 1.883 1.012.064-.161.103-.33.116-.503a4.516 4.516 0 00-1.999-.509z"
        fill="#FF8E1C"
      />
    </Svg>
  );
}

export default ButterFlyIcon;
