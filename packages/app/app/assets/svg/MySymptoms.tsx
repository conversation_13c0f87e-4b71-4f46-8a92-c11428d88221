import * as React from "react";
import Svg, { <PERSON>, <PERSON> } from "react-native-svg";

function MySymptpms(props: any) {
  return (
    <Svg
      width={52}
      height={52}
      viewBox="0 0 52 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G fill="#004987">
        <Path d="M43.9 20.894a2.159 2.159 0 00-2.483-1.765l-4.726.783s-1.97-5.2-1.97-5.249c-1.005-3.075-5.314-3.075-8.64-3.075-3.327 0-7.61 0-8.592 3.075 0 .048-1.987 5.25-1.987 5.25l-4.725-.784a2.159 2.159 0 00-2.503 1.74 2.148 2.148 0 001.75 2.488l.04.006 6.48 1.074a2.16 2.16 0 002.381-1.363l1.329-3.537.426.188V50.23H25V30.91h2.16v19.322h4.32V19.73a8.81 8.81 0 00.454-.199l1.334 3.543a2.16 2.16 0 002.02 1.395 2 2 0 00.356-.032l6.48-1.074a2.148 2.148 0 001.777-2.469zM30.4 6.221c0 2.372-1.934 4.294-4.32 4.294s-4.32-1.922-4.32-4.294c0-2.37 1.934-4.293 4.32-4.293S30.4 3.85 30.4 6.22z" />
      </G>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.727 21.364c0-4.05 3.307-7.333 7.388-7.333 4.081 0 7.389 3.283 7.389 7.333 0 4.05-3.308 7.332-7.389 7.332-4.08 0-7.388-3.283-7.388-7.332z"
        fill="#fff"
        fillOpacity={0.898039}
      />
      <Path
        d="M43.007 32.347l-3.766-3.736a2.09 2.09 0 00-1.867-.568l-1.326-1.316a8.812 8.812 0 001.868-5.44c0-4.931-4.067-8.967-9.037-8.967-4.969 0-9.035 4.036-9.035 8.967 0 4.932 4.066 8.968 9.035 8.968a9.033 9.033 0 005.482-1.854l1.326 1.316c-.12.658.06 1.345.572 1.853l3.765 3.736c.422.42.964.628 1.506.628a2.13 2.13 0 001.506-.627c.784-.838.784-2.153-.03-2.96zM28.85 28.432c-3.976 0-7.229-3.229-7.229-7.174 0-3.946 3.253-7.174 7.229-7.174 3.975 0 7.229 3.228 7.229 7.174 0 3.945-3.254 7.174-7.23 7.174z"
        fill="#004987"
        stroke="#fff"
        strokeWidth={0.1}
      />
    </Svg>
  );
}

export default MySymptpms;
