import * as React from "react"
import Svg, { <PERSON>, <PERSON>, Defs, SvgProps, Rect } from "react-native-svg"

function CalenderMarkIcon(props: SvgProps) {
  return (
    <Svg
      width={35}
      height={35}
      viewBox="0 0 35 35"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Rect width={35} height={35} rx={6} fill="#E1F1FF" />
      <Path
        d="M17.706 8.756a1.093 1.093 0 000-2.187h-.031a1.082 1.082 0 00-1.063 1.094c.001.603.49 1.092 1.094 1.093zm-.341-1.424a.454.454 0 01.32-.138h.021a.469.469 0 11-.469.469v-.005a.454.454 0 01.128-.325v-.001zM12.942 15.186l-1.029-1.028-.442.442 1.47 1.47 2.721-2.72-.442-.442-2.278 2.278zM17.394 14.066h6.56v.625h-6.56v-.625zM12.942 18.624l-1.029-1.029-.442.442 1.47 1.47 2.721-2.72-.442-.441-2.278 2.278zM17.394 17.815h6.56v.625h-6.56v-.625zM17.394 21.564h6.56v.625h-6.56v-.625zM17.394 25.314h6.56v.624h-6.56v-.624zM12.082 23.126h2.5v-2.499h-2.5v2.5zm.625-1.874h1.25v1.25h-1.25v-1.25zM12.082 26.876h2.5v-2.499h-2.5v2.5zm.625-1.874h1.25v1.25h-1.25v-1.25z"
        fill="#0B79D3"
      />
      <Path
        d="M27.078 8.437c0-.69-.56-1.25-1.25-1.25h-4.686v-.625c0-.862-.7-1.561-1.562-1.562h-3.749c-.862 0-1.56.7-1.562 1.562v.625H9.583c-.69 0-1.25.56-1.25 1.25V28.75c0 .69.56 1.25 1.25 1.25H25.83c.69 0 1.25-.56 1.25-1.25V8.437zm-13.433-.625h1.25v-1.25c0-.518.419-.937.936-.937h3.75c.517 0 .937.42.937.937v1.25h1.25v2.506h-8.123V7.812zm12.184 21.563H9.583a.625.625 0 01-.625-.625V8.437c0-.346.28-.625.625-.625h3.437v3.13h9.372v-3.13h3.437c.345 0 .625.28.625.625V28.75c0 .346-.28.625-.625.625z"
        fill="#0B79D3"
      />
    </Svg>
  )
}

export default CalenderMarkIcon;
