import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"

function RiskMeter(props: SvgProps) {
  return (
    <Svg
      width={83}
      height={49}
      viewBox="0 0 83 49"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M81 44.26H2c0-21.815 17.685-39.5 39.5-39.5S81 22.444 81 44.26z"
        fill="#fff"
        stroke="#0B79D3"
        strokeWidth={3}
      />
      <Path
        stroke="#0B79D3"
        strokeWidth={0.5}
        strokeLinecap="round"
        d="M16.353 16.2797L41.02 43.9068"
      />
      <Path
        stroke="#0B79D3"
        strokeWidth={0.5}
        strokeLinecap="round"
        d="M67.0131 16.6131L41.3533 44.2467"
      />
      <Path
        d="M46 43.76a4.5 4.5 0 01-9 0c0-2.485-6-43.5-6-43.5s15 41.015 15 43.5z"
        fill="#5351E8"
      />
      <Path d="M44 43.76a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" fill="#fff" />
    </Svg>
  )
}

export default RiskMeter
