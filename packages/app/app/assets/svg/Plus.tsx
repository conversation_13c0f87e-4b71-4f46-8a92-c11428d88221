import * as React from "react";
import Svg, { <PERSON>, <PERSON>, Defs, <PERSON>lip<PERSON><PERSON>, SvgProps } from "react-native-svg";

function Plus(props: SvgProps) {
  return (
    <Svg width={19} height={19} viewBox="0 0 19 19" fill="none" {...props}>
      <G
        clipPath="url(#clip0_3041_11053)"
        stroke="#004987"
        strokeWidth={1.58333}
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <Path d="M9.5 17.416a7.917 7.917 0 100-15.833 7.917 7.917 0 000 15.833zM6.333 9.5h6.333M9.5 6.333v6.333" />
      </G>
      <Defs>
        <ClipPath id="clip0_3041_11053">
          <Path fill="#fff" d="M0 0H19V19H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default Plus;
