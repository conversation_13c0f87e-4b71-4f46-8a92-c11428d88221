import * as React from "react"
import Svg, { Rect, <PERSON>, Ellipse, SvgProps } from "react-native-svg"

function AirQualityBookIcon(props: SvgProps) {
  return (
    <Svg
      width={28}
      height={28}
      viewBox="0 0 28 28"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Rect width={28} height={28} rx={5} fill="#E81448" fillOpacity={0.15} />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.6 7.64a.6.6 0 00-.6.6v12.248a.6.6 0 00.6.6h6.604c.617 0 1.195.213 1.608.571.41.356.621.817.622 1.277v.002a.6.6 0 101.2 0c0-.46.21-.923.622-1.279.413-.358.99-.57 1.608-.57h6.604a.6.6 0 00.6-.6V8.238a.6.6 0 00-.6-.6h-5.66c-1.135 0-2.237.39-3.062 1.104a3.97 3.97 0 00-.712.807 3.97 3.97 0 00-.713-.807c-.824-.714-1.926-1.103-3.06-1.103H4.6zm10.034 12.98a3.697 3.697 0 012.23-.732h6.004V8.84h-5.06c-.868 0-1.685.299-2.276.81-.588.509-.898 1.179-.898 1.856v9.113zm-1.2 0v-9.114c0-.677-.31-1.347-.898-1.856-.59-.511-1.408-.81-2.276-.81H5.2v11.048h6.004c.811 0 1.604.256 2.23.731z"
        fill="#E81448"
      />
      <Ellipse
        cx={14.0342}
        cy={4.75192}
        rx={1.81339}
        ry={1.75192}
        stroke="#E81448"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  )
}

export default AirQualityBookIcon;
