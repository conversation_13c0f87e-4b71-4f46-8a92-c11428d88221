import * as React from "react"
import Svg, { Path } from "react-native-svg"

function InviteAndEditIcon(props: any) {
  return (
    <Svg
      width={24}
      height={25}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path transform="translate(0 .5)" fill="#fff" d="M0 0H24V24H0z" />
      <Path
        d="M14 19.5a6 6 0 10-12 0M8 13.5a4 4 0 100-8 4 4 0 000 8zM19 8.5v6M22 11.5h-6"
        stroke="#004987"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  )
}

export default InviteAndEditIcon;
