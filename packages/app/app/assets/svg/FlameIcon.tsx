import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

function FlameIcon(props: SvgProps) {
  return (
    <Svg
      width={15}
      height={22}
      viewBox="0 0 15 22"
      fill="none"
      //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M13.733 11.285c.435 1.824-1.422 3.609-3.24 2.855-1.54-.555-2.133-2.34-1.066-3.886 2.41-3.213.632-8.25-3.359-9.994C7.886 3.71 5.12 6.843 3.5 8.19 1.92 9.501.853 10.888.536 11.681c-1.62 3.927.79 7.694 2.45 8.606-.75-1.705-1.422-4.917 1.502-7.971 0 0-.83 3.252.987 5.473 1.818 2.26 1.818 3.926 1.818 3.926a7.268 7.268 0 006.717-4.521c.79-1.705.87-4.362-.277-5.91z"
        fill="#F89021"
      />
    </Svg>
  );
}

export default FlameIcon;
