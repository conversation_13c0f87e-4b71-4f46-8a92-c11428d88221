import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

export function ImgSelect(props: SvgProps) {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <Path
        d="M6.753 13.5a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5z"
        fill="#5CCAD2"
      />
      <Path
        d="M21.003 19.5a3 3 0 01-3 3h-15a3 3 0 01-3-3v-12A3 3 0 013 4.5a3 3 0 013-3h15a3 3 0 013 3v12a3 3 0 01-2.997 3zM21 3H6a1.5 1.5 0 00-1.5 1.5h13.503a3 3 0 013 3V18a1.5 1.5 0 001.497-1.5v-12A1.5 1.5 0 0021 3zM3.003 6a1.5 1.5 0 00-1.5 1.5v12l3.969-3.531a.75.75 0 01.945-.093l3.99 2.66 5.565-5.566a.75.75 0 01.865-.14l2.666 2.92V7.5a1.5 1.5 0 00-1.5-1.5h-15z"
        fill="#5CCAD2"
      />
    </Svg>
  );
}
