import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface Props extends SvgProps {
  active?: boolean;
}

export default function Like({ active, ...otherProps }: Props) {
  return (
    <Svg width={18} height={16} viewBox="0 0 18 16" fill="none" {...otherProps}>
      <Path
        d="M4.855 0C1.962 0 0 2.674 0 5.622 0 10.674 3.892 12.757 8.865 16c4.973-3.243 8.865-5.326 8.865-10.378C17.73 2.674 15.767 0 12.875 0c-1.861 0-3.392 1.053-4.01 1.684C8.246 1.053 6.716 0 4.855 0z"
        fill={active ? "#f0304e" : "#D7E0EB"}
      />
    </Svg>
  );
}
