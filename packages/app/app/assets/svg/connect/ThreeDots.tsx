import * as React from "react";
import Svg, { Circle, SvgProps } from "react-native-svg";

export default function ThreeDots(props: SvgProps) {
  return (
    <Svg width={32} height={21} viewBox="0 0 32 21" fill="none" {...props}>
      <Circle cx={13.2297} cy={10} r={2.5} fill="#B8C0CC" />
      <Circle cx={21.2297} cy={10} r={2.5} fill="#B8C0CC" />
      <Circle cx={29.2297} cy={10} r={2.5} fill="#B8C0CC" />
    </Svg>
  );
}
