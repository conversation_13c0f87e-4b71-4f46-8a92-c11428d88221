import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

export default function ConnectLogo(props: SvgProps) {
  return (
    <Svg width={28} height={26} viewBox="0 0 28 26" fill="none" {...props}>
      <Path
        d="M2.91 19.477a4.428 4.428 0 008.813.856l.43-4.406-4.407-.428a4.428 4.428 0 00-4.835 3.978z"
        fill="#004987"
      />
      <Path
        d="M22.135 21.346a4.429 4.429 0 01-4.834 3.978 4.429 4.429 0 01-3.979-4.834l.429-4.406 4.406.429a4.424 4.424 0 013.978 4.833zM1.044.655L.438 6.892a6.266 6.266 0 005.63 6.843l6.237.606.606-6.236a6.27 6.27 0 00-5.63-6.845L1.043.655z"
        fill="#F18A00"
      />
      <Path
        d="M27.591 3.237l-.606 6.238a6.266 6.266 0 01-6.842 5.63l-6.238-.607.606-6.236a6.267 6.267 0 016.846-5.63l6.235.605z"
        fill="#004987"
      />
    </Svg>
  );
}
