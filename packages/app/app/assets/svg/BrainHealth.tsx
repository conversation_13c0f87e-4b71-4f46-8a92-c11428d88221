import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"

function BrainHealth(props: SvgProps) {
  return (
    <Svg
      width={21}
      height={25}
      viewBox="0 0 21 25"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M13.762 9.922h-2.68a2.06 2.06 0 00-2.076 2.057v2.239H7.65v-1.785c0-.635.03-1.452-.421-1.966a5.636 5.636 0 00-.903-.787c-.24-.151-.482-.333-.662-.575-.09-.12-.15-.272-.21-.423a2.374 2.374 0 011.113-.151c.18 0 .33-.121.33-.303.031-.181-.09-.333-.24-.393a3.11 3.11 0 00-1.384.151c0-.06 0-.121-.03-.151-.03-.272.03-.545.09-.817.24-.635.843-.907 1.414-1.12.09-.03.211-.06.362-.09l.09-.03h.03c.15.03.27.06.391.09l.15.03.181.031c.301.363.452.817.422 1.27-.03.454-.241.878-.602 1.18a.369.369 0 00-.03.484c.12.121.3.152.451.03.21-.181.392-.393.512-.635h.03a4.188 4.188 0 001.475-.453c.06-.061.12-.121.15-.212s0-.182-.06-.242c-.12-.151-.3-.182-.451-.091-.271.151-.542.272-.873.303.03-.091.06-.212.06-.303.03-.393-.03-.786-.18-1.12.24.031.481.061.722.061.21 0 .421-.03.632-.06.391.484.903.817 1.505.968.783.393 1.505.907 1.505 1.754 0 .121.06.273.15.333.12.06.241.06.362 0 .12-.06.18-.212.15-.333 0-.665-.3-1.27-.813-1.663.422-.03.783-.242 1.054-.545a.369.369 0 00-.03-.484.365.365 0 00-.482.03c-.15.182-.542.333-1.114.303a2.434 2.434 0 01-1.565-.575 2.59 2.59 0 001.174-1.028c.09-.152.03-.363-.12-.484-.15-.091-.362-.03-.452.12-.511.938-1.595 1.21-3.31.787a1.54 1.54 0 00.511-1.361.333.333 0 00-.18-.303.319.319 0 00-.362.03c-.12.061-.15.212-.15.333.03.787-.392.938-1.084 1.15-.09.03-.21.06-.3.09a1.568 1.568 0 00-1.024-.877c-.12-.03-.241 0-.331.091-.09.09-.12.212-.09.363.03.121.12.212.27.242.241.06.422.242.542.454v.03a2.222 2.222 0 00-1.234 1.27.97.97 0 00-.09.424c-.06-.06-.12-.121-.15-.212a2.557 2.557 0 01-.242-.816c-.03-.121-.12-.242-.21-.273-.12-.06-.241-.03-.331.06-.09.092-.151.213-.09.334.06.363.15.695.33.998.18.302.422.544.753.696.03.423.18.847.391 1.24a2.78 2.78 0 01-.782.665c-.09.06-.151.182-.181.303 0 .12.06.242.18.302s.241.06.332-.03c.3-.212.572-.423.812-.696.18.152.362.273.542.394.301.181.542.393.783.665.27.333.27.968.27 1.512v1.816H5.726c0-2.239-1.084-2.36-1.204-2.42-.451-.121-1.625-.757-1.896-2.027-.03-.06-.03-.151-.03-.212a2.007 2.007 0 01.09-2.42 2.314 2.314 0 01-.09-.635c0-.968.692-1.815 1.655-1.997.301-.847 1.084-1.421 1.957-1.391.15 0 .33.03.481.06.361-.423.873-.665 1.445-.726a2.052 2.052 0 011.535.545 2.134 2.134 0 011.806-.272 1.991 1.991 0 011.324 1.27h.12c1.145 0 2.048.908 2.078 2.057v.09a2.042 2.042 0 011.023 1.937c-.24 1.059-1.174 1.875-2.257 1.845zm6.41 3.176l-2.076-3.6v-.12c.12-3.328-1.565-6.444-4.425-8.107a9.036 9.036 0 00-9.24 0C1.57 2.934-.114 6.05.006 9.378c0 2.843 1.294 5.535 3.552 7.26V24.2h9.511v-3.6h1.475c.963 0 1.866-.363 2.529-1.059.662-.665 1.023-1.603 1.023-2.54v-1.816h1.325c.782-.06 1.474-.968.752-2.087z"
        fill="#5351E8"
      />
      <Path
        d="M8.997 24.2h1.241c.06-.513.017-3.893.931-5.895.775-1.698 1.551-3.102 3.723-2.482 1.551.62 3.103 2.482 3.103 2.482l.31-.93-1.406-1.407a5 5 0 00-1.78-1.146l-1.347-.505a.05.05 0 01-.001-.093l.882-.353c.363-.145.75-.22 1.141-.22 1.04 0 2.035.421 2.759 1.168l.372.383h.931s-1.139-2.792-3.161-2.792c-.562 0-2.878.195-4.285 1.241-.895.665-.885 1.921-1.551 2.896-1.67 2.442-1.862 5.736-1.862 7.653z"
        fill="#fff"
      />
    </Svg>
  )
}

export default BrainHealth;
