import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"

function BoneHealth(props: SvgProps) {
  return (
    <Svg
      width={19}
      height={29}
      viewBox="0 0 19 29"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.32 14.67c-3.652 0-6.615-3.285-6.615-7.335S11.668 0 15.321 0c-3.653 3.04-4.394 8.785-1.654 12.838a8.848 8.848 0 001.654 1.833z"
        fill="#2CD7AE"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.839 27.977c3.653 0 6.615-3.285 6.615-7.336s-2.962-7.335-6.615-7.335c3.653 3.038 4.394 8.786 1.654 12.837a8.776 8.776 0 01-1.654 1.834zM5.571 26.441c0-.47.7-.853 1.567-.853s1.567.382 1.567.853-.7.853-1.567.853-1.567-.382-1.567-.853zM7.66 23.712c0-.471.7-.853 1.567-.853s1.567.382 1.567.853c0 .47-.7.853-1.567.853s-1.567-.382-1.567-.853z"
        fill="#2CD7AE"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.312 20.641c0-.658 1.09-1.194 2.438-1.194 1.347 0 2.437.536 2.437 1.194 0 .659-1.09 1.194-2.437 1.194-1.348 0-2.438-.535-2.438-1.194zM0 8.018c0-.659 1.09-1.194 2.437-1.194 1.348 0 2.438.535 2.438 1.194 0 .658-1.09 1.194-2.438 1.194C1.09 9.212 0 8.676 0 8.018zM.696 11.43c0-.66 1.09-1.195 2.438-1.195 1.347 0 2.437.536 2.437 1.194 0 .659-1.09 1.194-2.437 1.194-1.348 0-2.438-.535-2.438-1.194zM3.482 14.159c0-.659 1.09-1.194 2.437-1.194 1.348 0 2.438.535 2.438 1.194 0 .658-1.09 1.194-2.438 1.194-1.347 0-2.437-.536-2.437-1.194zM5.92 17.4c0-.566 1.09-1.023 2.437-1.023 1.347 0 2.437.457 2.437 1.023 0 .566-1.09 1.024-2.437 1.024-1.348 0-2.438-.458-2.438-1.024zM3.134 2.73c0-.376.7-.683 1.567-.683s1.567.307 1.567.682c0 .376-.7.683-1.567.683s-1.567-.307-1.567-.683zM1.045 5.459c0-.375.7-.683 1.566-.683.868 0 1.567.308 1.567.683 0 .375-.7.682-1.567.682s-1.566-.307-1.566-.682zM2.786 28.488c0-.283.546-.511 1.218-.511.672 0 1.219.228 1.219.511 0 .283-.547.512-1.219.512s-1.218-.229-1.218-.512zM6.268.512C6.268.229 6.814 0 7.486 0c.672 0 1.219.229 1.219.512 0 .283-.547.512-1.219.512S6.268.794 6.268.512z"
        fill="#2CD7AE"
      />
    </Svg>
  )
}

export default BoneHealth;
