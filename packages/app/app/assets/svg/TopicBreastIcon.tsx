import * as React from "react";
import Svg, { Path, Rect,G, SvgProps } from "react-native-svg";

function TopicBreastIcon(props: SvgProps) {
  return (
    <Svg width={58} height={44} viewBox="0 0 58 44" fill="none" {...props}>
      {/* Background Rectangle (Same as TopicLungsIcon) */}
      <Rect width={58} height={44} rx={5} fill="#FF4B91" />

      {/* Breast Cancer Awareness Icon - Centered */}
      <G transform="translate(20, 7)">
        <Path
          d="M4.085 4.493A8.017 8.017 0 015.37 2.34L6.32 1.2A3.335 3.335 0 018.88 0h.617c.296 0 .592.024.884.073l.694.116a5.263 5.263 0 013.945 3.054l.264.594a8.011 8.011 0 01.633 4.12 8.151 8.151 0 01-.918 2.95l-2.157 4.007 6.214 9.115L17.4 29l-2.9-4.143-4.557-5.8L2.486 29 0 23.614l6.629-9.528-1.658-2.9-.58-1.015a6.327 6.327 0 01-.38-5.49l.074-.188z"
          fill="#fff"
        />
        <Path
          d="M10.967 9.745a1 1 0 01-1.808-.02L7.112 5.274a1 1 0 01.919-1.417l4.19.045a1 1 0 01.888 1.437l-2.142 4.407z"
          fill="#FF4B91"
        />
        <Path
          stroke="#FF4B91"
          strokeWidth={0.5}
          d="M6.20801 13.8613L10.208 19.8613"
        />
        <Path
          d="M5.513 2L7.5 6l1.513 3.5.361.721c.425.85.938 1.654 1.529 2.398v0a13.714 13.714 0 002.508 2.44l.59.441"
          stroke="#FF4B91"
          strokeWidth={0.5}
        />
      </G>
    </Svg>
  );
}

export default TopicBreastIcon;
