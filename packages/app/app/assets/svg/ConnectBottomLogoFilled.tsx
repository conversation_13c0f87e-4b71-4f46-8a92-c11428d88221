import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

export default function ConnectBottomLogoFilled(props: SvgProps) {
  return (
    <Svg width={22} height={16} viewBox="0 0 22 16" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.158 4.233a4.207 4.207 0 01-4.209 4.234 4.207 4.207 0 01-4.21-4.234A4.206 4.206 0 0110.95 0a4.206 4.206 0 014.21 4.233zM10.95 16c-3.431 0-6.36-.544-6.36-2.72 0-2.177 2.91-2.74 6.36-2.74 3.431 0 6.361.544 6.361 2.72S14.399 16 10.949 16zm6.008-11.69a5.765 5.765 0 01-.984 ************ 0 00.107.245c.156.027.318.042.483.046 1.643.044 3.118-1.02 3.525-2.621C20.692 2.84 18.92.706 16.663.706c-.245 0-.48.026-.708.073-.031.007-.064.021-.082.05-.022.034-.006.08.016.11a5.807 5.807 0 011.068 3.37zm2.721 5.203c1.104.217 1.83.66 2.131 1.304a1.923 1.923 0 010 1.67c-.46.998-1.944 1.319-2.52 1.402-.12.018-.215-.086-.203-.206.295-2.767-2.048-4.08-2.654-4.381-.026-.014-.032-.034-.03-.047.003-.009.013-.023.033-.026 1.312-.024 2.722.156 3.243.284zM5.438 7.84c.164-.004.325-.019.483-.047a.158.158 0 00.106-.244 5.765 5.765 0 01-.984-3.24c0-1.25.39-2.416 1.068-3.372.022-.03.037-.075.016-.11-.017-.027-.051-.042-.082-.05a3.52 3.52 0 00-.71-.072C3.08.706 1.309 2.84 1.913 5.22c.407 1.6 1.882 2.665 3.525 2.621zm.159 1.413c.003.014-.003.034-.028.048-.607.302-2.95 1.614-2.656 4.38.013.121-.082.224-.201.207-.577-.083-2.06-.403-2.52-1.402a1.916 1.916 0 010-1.67c.3-.644 1.026-1.087 2.13-1.305.522-.127 1.93-.307 3.244-.283.02.003.03.017.03.025z"
        fill="#FF8E1C"
      />
    </Svg>
  );
}
