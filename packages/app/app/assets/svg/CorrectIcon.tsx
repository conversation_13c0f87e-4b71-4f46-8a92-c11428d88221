import * as React from "react"
import Svg, { Rect, Path, SvgProps } from "react-native-svg"

function CorrectQuestionIcon(props: SvgProps) {
  return (
    <Svg
      width={37}
      height={35}
      viewBox="0 0 37 35"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Rect
        x={0.5}
        y={6.75977}
        width={27}
        height={27}
        rx={4.5}
        stroke="#0B79D3"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.877 19.086a3 3 0 01.176-4.132l1.064-1.026a3 3 0 014.344.185l2.868 3.286 15.46-14.945a3 3 0 014.345.184l.99 1.135a3 3 0 01-.174 4.13L16.158 26.067a3 3 0 01-4.344-.184l-5.937-6.797z"
        fill="#0B79D3"
        stroke="#E1F1FF"
        strokeWidth={1.2}
        strokeMiterlimit={8}
        strokeLinejoin="round"
      />
    </Svg>
  )
}

export default CorrectQuestionIcon
