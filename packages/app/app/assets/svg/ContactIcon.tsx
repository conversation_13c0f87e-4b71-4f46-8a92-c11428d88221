import * as React from "react"
import Svg, { G, Rect, Circle, Path, Defs, ClipPath, SvgProps } from "react-native-svg"

function ContactIcon(props: SvgProps) {
  return (
    <Svg
    //   xmlns="http://www.w3.org/2000/svg"
      width={112}
      height={113}
      viewBox="0 0 112 113"
      fill="none"
      {...props}
    >
      <G clipPath="url(#clip0_8435_19898)">
        <Rect y={0.259766} width={112} height={112} rx={21} fill="#D8D6CC" />
        <Circle
          cx={52.5}
          cy={55.7598}
          r={33}
          stroke="#A9A29A"
          strokeWidth={3}
        />
        <G filter="url(#filter0_i_8435_19898)">
          <Path fill="#2CD7AE" d="M101 -1.74023H112V26.25977H101z" />
        </G>
        <G filter="url(#filter1_i_8435_19898)">
          <Path fill="#B4DDFF" d="M101 26.2598H112V54.2598H101z" />
        </G>
        <G filter="url(#filter2_i_8435_19898)">
          <Path fill="#FFA346" d="M101 54.2598H112V82.2598H101z" />
        </G>
        <G filter="url(#filter3_i_8435_19898)">
          <Path fill="#2CD7AE" d="M101 82.2598H112V110.2598H101z" />
        </G>
        <Path
          d="M65.822 55.04a6.39 6.39 0 005.487-6.318 6.389 6.389 0 00-5.34-6.306m3.939 20.08c2.999.45 5.092 1.501 5.092 3.669 0 1.492-.986 2.461-2.58 3.07M39.179 55.04a6.388 6.388 0 01-5.487-6.317 6.389 6.389 0 015.34-6.306m-3.939 20.08c-2.999.45-5.092 1.501-5.092 3.669 0 1.492.986 2.461 2.58 3.07m19.921-5.82c-7.134 0-13.227 1.083-13.227 5.408 0 4.323 6.055 5.437 13.227 5.437 7.134 0 13.225-1.072 13.225-5.399s-6.053-5.446-13.225-5.446zm0-6.172c4.682 0 8.477-3.8 8.477-8.492 0-4.689-3.795-8.491-8.477-8.491-4.681 0-8.477 3.802-8.477 8.492-.018 4.673 3.75 8.476 8.415 8.491h.062z"
          stroke="#A9A29A"
          strokeWidth={3}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_8435_19898">
          <Rect y={0.259766} width={112} height={112} rx={21} fill="#fff" />
        </ClipPath>
      </Defs>
    </Svg>
  )
}

export default ContactIcon;
