import * as React from "react"
import Svg, { <PERSON>, Ellipse, SvgProps } from "react-native-svg"

function BookIcon(props: SvgProps) {
  return (
    <Svg
      width={34}
      height={32}
      viewBox="0 0 30 32"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M.6 8.309a.6.6 0 00-.6.6v18.488a.6.6 0 00.6.6h9.968c1 0 1.944.344 2.628.936.672.582 1.034 1.35 1.044 2.13v.031a.6.6 0 ********.6 0 00.6-.6v-.03c.01-.781.371-1.549 1.044-2.13.684-.593 1.628-.937 2.628-.937h9.968a.6.6 0 00.6-.6V8.909a.6.6 0 00-.6-.6h-8.544c-1.644 0-3.236.564-4.42 1.59-.531.46-.962.995-1.276 1.582a5.593 5.593 0 00-1.276-1.582c-1.185-1.026-2.776-1.59-4.42-1.59H.6zm14.84 19.959v-14.43-.027c.008-1.109.52-2.19 1.46-3.005.952-.823 2.258-1.297 3.636-1.297h7.944v17.288h-9.368c-1.267 0-2.496.434-3.414 1.23-.09.077-.176.157-.258.24zm-1.2-14.457c-.009-1.109-.52-2.19-1.461-3.005-.951-.823-2.258-1.297-3.635-1.297H1.2v17.288h9.368c1.266 0 2.495.434 3.413 ***********.176.157.259.24V13.84v-.028z"
        fill="#FF8E1C"
      />
      <Ellipse
        cx={14.8402}
        cy={3.64439}
        rx={2.73719}
        ry={2.64439}
        stroke="#FF8E1C"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  )
}

export default BookIcon
