import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export default function W2(props: SvgProps) {
  return (
    <Svg width={225} height={228} viewBox="0 0 225 228" fill="none" {...props}>
      <Path
        d="M174.514 73.135a1.913 1.913 0 01-.923-.702 2.046 2.046 0 01-.054-2.272c.215-.339.525-.6.888-.75l.309-.454-.003-.14c-.117-4.718-14.741 2.364-16.613 5.994a16.22 16.22 0 00-1.636 5.148 65.618 65.618 0 013.987-26.526 62.154 62.154 0 012.702-6.2 64.2 64.2 0 012.693-4.822c5.248-8.492 12.353-15.565 20.731-20.637 3.151 1.496 6.164 2.13 8.867-4.271.48-1.14 1.678-1.808 2.285-2.875a15.096 15.096 0 01-.961-.287l-.298-.103-.037-.013a1.887 1.887 0 01-.872-.641 2.035 2.035 0 01-.196-2.125c.168-.336.424-.614.74-.802l.589-.35c.3-.18.603-.354.902-.535a.823.823 0 00.095-.052c.344-.206.686-.406 1.03-.612a5.801 5.801 0 00-1.038-2.353c-1.794-2.363-5.367-2.681-7.864-1.17-2.502 1.51-3.967 4.457-4.405 7.434a23.694 23.694 0 00.373 7.684c-.189.117-.381.227-.57.344a61.828 61.828 0 00-9.691 7.473c1.924-3.687 1.329-20.065.184-23.948-1.375-4.664-7.503-5.36-9.666-1.034l-.062.126c.261.333.51.674.748 1.026.298.444.461.971.466 1.513a2.775 2.775 0 01-.434 1.523c-.289.45-.701.8-1.183 1.001a2.481 2.481 0 01-1.519.13l-.052-.012c-6.966 4.852.912 28.038 5.96 25.6a65.921 65.921 0 00-8.936 14.21 63.89 63.89 0 00-3.454 9.383l.026-.117c1.734-7.11-8.198-22.76-12.511-23.543-2.485-.452-4.713-1.483-5.267 1.083l-.027.123a17.72 17.72 0 012.219 2.67c.298.444.46.972.466 1.514a2.776 2.776 0 01-.435 1.523 2.6 2.6 0 01-1.183 1.001 2.477 2.477 0 01-1.518.13l-.052-.012-.107-.025c-4.767 6.488 5.943 24.432 16.534 24.406l.005.002a68.373 68.373 0 00-.536 13.589l14.064 5.71c.111-.143.22-.293.326-.437a16.813 16.813 0 01-3.802-1.822c1.542-.912 3.088-1.835 4.63-2.747.032-.015.064-.033.095-.052.784-.469 1.572-.93 2.356-1.399a27.098 27.098 0 001.635-6.5zm-3.775-39.59l.011-.007-.015.018.004-.01zm-14.045 28.032l-.016-.333c.082-.203.159-.408.232-.62.022-.059.033-.12.055-.178-.09.381-.185.76-.27 1.144l-.001-.013z"
        fill="#F1F2F2"
      />
      <Path
        d="M152.361 75.776c-.335-41.533-32.7-75.1-72.59-75.123C39.539.63 7.083 35.06 7.083 77.011v137.565c0 3.552 1.353 6.958 3.76 9.47 2.408 2.511 5.673 3.922 9.077 3.922h132.442c40.117 0 72.639-34.579 72.639-76.429s-32.522-75.763-72.639-75.763z"
        fill="#004987"
      />
      <Path
        d="M111.288 161.301c1.149-.187 2.106-.412 2.801-.659.696-.247 1.114-.51 1.224-.771.111-.26-.088-.512-.583-.737-.495-.225-1.273-.418-2.281-.566l1.581-11.018.202-1.409c.208-1.449-8.956-1.97-15.074-.857l-6.574 1.196 3.107 3.379.344 1.248 2.211 8.029c-1.548.38-2.38.828-2.34 1.261.04.432.95.818 2.557 1.085 1.608.267 3.801.396 6.165.363 2.364-.034 4.733-.227 6.66-.544z"
        fill="#A0616A"
      />
      <Path
        opacity={0.1}
        d="M152.361 75.123C152.026 33.59 119.661.023 79.771 0 57.962 0 38.443 10.106 25.13 26.055c12.605-10.14 28.08-15.628 43.997-15.603 39.889.023 72.254 33.59 72.589 75.122 40.118 0 72.64 33.926 72.64 75.776.024 18.255-6.293 35.9-17.781 49.669 8.848-7.068 16.015-16.169 20.945-26.596s7.49-21.899 7.481-33.525c0-41.849-32.522-75.775-72.639-75.775z"
        fill="#000"
      />
      <Path
        d="M84.596 121.605c-2.96-9.842-9.692-18.868-19.002-22.196-9.311-3.327-22.944 1.742-29.944 9.019-12.803 13.311-13.455 62.094-4.659 76.544 1.75-.096 7.788-.168 9.558-.237l2.505-8.71v8.624c13.822-.445 29.207 11.415 42.61.122 1.291-10.231 1.892-53.324-1.068-63.166z"
        fill="#2F2E41"
      />
      <Path
        d="M64.417 152.486c11.028 0 19.967-9.326 19.967-20.83 0-11.504-8.94-20.83-19.967-20.83-11.028 0-19.968 9.326-19.968 20.83 0 11.504 8.94 20.83 19.968 20.83z"
        fill="#D2976B"
      />
      <Path
        d="M78.137 104.532c-7.627-4.851-17.793-4.991-25.54-.352-7.748 4.64-12.759 13.868-12.59 23.185 11.16.535 22.738 11.47 34.775.699l2.81-7.174 1.656 7.179c3.622 0 7.252-.005 10.888-.015.405-9.309-4.371-18.672-11.999-23.522z"
        fill="#2F2E41"
      />
      <Path
        d="M82.595 118.656c4.582-9.141 12.738-16.783 22.466-18.34a19.987 19.987 0 017.674.404c14.323 3.354 23.284 18.248 20.484 33.28-.555 2.982-1.204 6.067-1.667 9.492a8.837 8.837 0 01-1.148 3.3 8.455 8.455 0 01-2.315 2.547 8.066 8.066 0 01-3.099 1.37 7.906 7.906 0 01-3.365-.035l-.053-.012-2.451-.547c-7.284-1.609-15.619.21-23.522.11-11.779-.148-20.057-12.142-16.301-23.788a55.016 55.016 0 013.297-7.781z"
        fill="#E6E7E8"
      />
      <Path
        d="M100.752 153.101c11.028 0 19.968-9.326 19.968-20.83 0-11.504-8.94-20.83-19.968-20.83s-19.968 9.326-19.968 20.83c0 11.504 8.94 20.83 19.968 20.83z"
        fill="#A0616A"
      />
      <Path
        d="M91.847 103.043c8.329-3.367 18.358-1.629 25.197 4.367 6.839 5.995 10.206 16.002 8.461 25.142-11.076-1.533-24.325 7.094-34.345-5.728l-1.55-7.579-2.847 6.76c-3.566-.669-7.137-1.343-10.715-2.024 1.18-9.237 7.468-17.571 15.799-20.938z"
        fill="#E6E7E8"
      />
      <Path
        d="M148.624 154.338a8.006 8.006 0 00-3.171-1.602 7.802 7.802 0 00-3.522-.051 7.969 7.969 0 00-3.213 1.508 8.397 8.397 0 00-2.299 2.784l-56.362 20.657 5.592 12.4 54.565-21.214a7.86 7.86 0 005.795.384c1.911-.617 3.539-1.943 4.576-3.728a8.832 8.832 0 001.048-5.958c-.361-2.053-1.432-3.896-3.009-5.18z"
        fill="#D2976B"
      />
      <Path
        d="M88.396 167.959l-8.326 3.759-4.45 2.01-2.904 1.308a9.227 9.227 0 00-3.382 1.757c-1.714 1.397-2.913 3.368-3.397 5.58a10.25 10.25 0 00.743 6.564c.964 2.034 2.57 3.659 4.55 4.601a9.1 9.1 0 006.311.573l17.042-2.031c.484-.06.95-.227 1.368-.491a3.47 3.47 0 001.049-1.04c.563-.858 5.823-3.444 5.569-4.447l2.251-1.077 3.267-1.067 12.266-5.887-1.855-7.155 1.716-8.784c-.257-.99-28.088 6.33-28.977 5.893a3.282 3.282 0 00-1.414-.359 3.274 3.274 0 00-1.427.293z"
        fill="#3F3D56"
      />
      <Path
        d="M112.263 226.918H34.415c-3.23 0-6.327-1.339-8.61-3.721-2.285-2.382-3.567-5.614-3.567-8.983v-29.897c0-3.369 1.282-6.601 3.566-8.983 2.284-2.382 5.381-3.721 8.611-3.721l9.959-14.637h34.76l33.129 14.637c1.599 0 3.183.329 4.66.967a12.17 12.17 0 013.951 2.754 12.77 12.77 0 012.639 4.121c.612 1.542.927 3.194.927 4.862v29.897c0 1.669-.315 3.32-.927 4.862a12.77 12.77 0 01-2.639 4.121 12.17 12.17 0 01-3.951 2.754 11.747 11.747 0 01-4.66.967z"
        fill="#3F3D56"
      />
      <Path
        d="M140.133 226.918H70.506c-1.6 0-3.183-.329-4.66-.967a12.16 12.16 0 01-3.951-2.754 12.76 12.76 0 01-2.64-4.121 13.184 13.184 0 01-.927-4.862v-29.897c0-3.369 1.283-6.601 3.567-8.983 2.283-2.382 5.38-3.721 8.61-3.721l18.335-14.637H123.6l16.532 14.637c3.229 0 6.327 1.339 8.611 3.721 2.283 2.382 3.566 5.614 3.566 8.983v29.897c0 3.369-1.283 6.601-3.566 8.983-2.284 2.382-5.382 3.721-8.611 3.721z"
        fill="#FF8E1C"
      />
      <Path
        d="M77.568 223.21a7.948 7.948 0 01-2.502 2.383 7.535 7.535 0 01-6.591.583 7.798 7.798 0 01-2.852-1.909l-48.636 1.64-6.218.21c-6.397.216-10.081-7.504-6.06-12.699l4.319-5.581 15.45 2.528 5.587.255 35.945 1.644a7.669 7.669 0 015.242-2.011 7.687 7.687 0 015.198 2.128 8.329 8.329 0 012.532 5.193 8.467 8.467 0 01-1.414 5.636z"
        fill="#D2976B"
      />
      <Path
        d="M34.666 159.976s-9.27-2.454-13.665 5.44c-3.518 6.319-15.484 30.221-17.969 40.03-12.12 30.473 16.061 18.338 15.273 21.212l9.428-16.144c4.096-2.137-3.789.621-4.828-.911l7.227-8.194 8.194-4.274 3.006-33.458-6.666-3.701z"
        fill="#3F3D56"
      />
      <Path
        d="M68.653 224.484a7.957 7.957 0 002.503 2.383 7.605 7.605 0 003.233 1.054 7.507 7.507 0 003.358-.472 7.786 7.786 0 002.851-1.909l48.636 1.641 6.219.209c6.396.216 10.081-7.504 6.06-12.698l-4.319-5.582-15.451 2.528-5.586.256-35.946 1.643a7.668 7.668 0 00-5.241-2.01 7.682 7.682 0 00-5.199 2.128c-1.424 1.349-2.325 3.197-2.531 5.193a8.467 8.467 0 001.413 5.636z"
        fill="#A0616A"
      />
      <Path
        d="M111.555 161.25s9.271-2.454 13.665 5.439c3.518 6.32 15.485 30.221 17.969 40.031 12.121 30.472-16.061 18.337-15.272 21.212l-9.428-16.145c-4.097-2.136 3.789.621 4.827-.911l-7.227-8.194-8.193-4.273-3.006-33.458 6.665-3.701z"
        fill="#FF8E1C"
      />
    </Svg>
  );
}
