import * as React from "react"
import Svg, { Rect, Path, Defs, LinearGradient, Stop } from "react-native-svg"


function SymptomTrackerLogo(props) {
  return (
    <Svg
      width={82}
      height={82}
      viewBox="0 0 82 82"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Rect width={82} height={82} rx={8} fill="#F2F9FF" />
      <Path
        d="M55.846 20.839h.908v-2.987h-.908v2.987zM55.846 28.678h.908v-5.04h-.908v5.04zM55.846 34.652h.908v-4.806h-.908v4.806z"
        fill="#C5E1F9"
      />
      <Path
        d="M23.355 72.41a4.869 4.869 0 004.856 4.856h23.345c2.67 0 4.856-2.185 4.856-4.855V13.85c0-2.667-2.187-4.853-4.856-4.853H28.21c-2.671 0-4.855 2.186-4.855 4.854V72.41z"
        fill="#C5E1F9"
      />
      <Path
        d="M51.556 76.58c2.3 0 4.17-1.87 4.17-4.168v-58.56c0-2.298-1.87-4.168-4.17-4.168H28.212c-2.3 0-4.171 1.87-4.171 4.169V72.41a4.174 4.174 0 004.17 4.168h23.345v.001z"
        fill="url(#paint0_linear_4931_9991)"
      />
      <Path d="M54.262 17.3h-28.75v51.547h28.75V17.301z" fill="#EAF4FE" />
      <Path
        d="M34.75 35.726V19.028c0-.884-.667-1.612-1.525-1.712.767.004 15.586 0 16.055 0 .858.1 1.524.828 1.524 1.712v16.594h.007v.393h-.015v14.552h.006v.393c0 .885.666 1.612 1.525 1.712-.31-.002-2.892-.002-5.9-.002v12.312a5.842 5.842 0 01-5.843 5.84 5.842 5.842 0 01-5.844-5.84V35.828h.014l.002-.1h-.006v-.002z"
        fill="#fff"
        stroke="#9AC7F7"
        strokeWidth={0.401873}
        strokeMiterlimit={10}
      />
      <Path
        d="M37.54 19.016h8.767M37.54 20.47h8.767M37.54 21.928h8.767M37.54 23.383h8.767M37.54 24.838h8.767M37.54 26.297h8.767M37.54 27.75h8.767M37.54 29.205h8.767M37.54 30.664h8.767M37.54 32.117h8.767M37.54 33.57h8.767M37.54 35.027h8.767M37.54 36.484h8.767M37.54 37.94h8.767M37.54 39.397h8.767M37.54 40.852h8.767M37.54 42.307h8.767M37.54 43.764h8.767M37.54 45.219h8.767M37.54 46.674h8.767M37.54 48.13h8.767M37.54 49.584h8.767M37.54 51.041h8.767M37.54 52.5h8.767M37.54 53.951h8.767M37.54 55.406h8.767M37.54 56.865h8.767M37.54 58.32h8.767M37.54 59.773h8.767M37.54 61.23h8.767M37.54 62.685h8.767M37.54 64.143h8.767M37.54 65.6h8.767M37.54 67.055h8.767M48.71 19.016h-1.048M48.71 20.47h-1.048M48.71 21.928h-1.048M48.71 23.383h-1.048M48.71 24.838h-1.048M48.71 26.297h-1.048M48.71 27.75h-1.048M48.71 29.205h-1.048M48.71 30.664h-1.048M48.71 32.117h-1.048M48.71 33.57h-1.048M48.71 35.027h-1.048M48.71 36.484h-1.048M48.71 37.94h-1.048M48.71 39.397h-1.048M48.71 40.852h-1.048M48.71 42.307h-1.048M48.71 43.764h-1.048M48.71 45.219h-1.048M48.71 46.674h-1.048M48.71 48.13h-1.048M48.71 49.584h-1.048M48.71 51.041h-1.048M48.71 52.5h-1.048M48.71 53.951h-1.048M48.71 55.406h-1.048M48.71 56.865h-1.048M48.71 58.32h-1.048M48.71 59.773h-1.048M48.71 61.23h-1.048M48.71 62.685h-1.048M48.71 64.143h-1.048M48.71 65.6h-1.048M48.71 67.055h-1.048"
        stroke="#9AC7F7"
        strokeWidth={0.401873}
        strokeLinejoin="round"
      />
      <Path
        d="M40.315 70.82a5.842 5.842 0 005.83-5.842v-8.367h.012v-7.33c0-.052.003-.103.008-.153v-6.371a5.842 5.842 0 015.843-5.841 5.842 5.842 0 015.843 5.84v6.523h5.239v15.698a5.842 5.842 0 01-5.844 5.841c-.039 0-.077-.002-.116-.003v.003H40.314v.001z"
        fill="#EAF4FE"
        stroke="#83B1FF"
        strokeWidth={0.401873}
        strokeMiterlimit={10}
      />
      <Path
        d="M68.518 36.92v.003c.04 0 .078-.003.117-.003a5.842 5.842 0 015.843 5.84v8.368H60.995v7.33a1.724 1.724 0 11-3.45 0v-7.33h-.011v-8.367a5.842 5.842 0 00-5.83-5.841h16.814z"
        fill="#9AC7F7"
        stroke="#9AC7F7"
        strokeWidth={0.401873}
        strokeMiterlimit={10}
      />
      <Path
        d="M60.995 58.457V41.76h.006l-.006-.289c0-.884.672-1.612 1.53-1.711.47 0 15.29.003 16.056 0-.858.1-1.524.827-1.524 1.711v.394h-.007v16.592c0 .885-.665 1.613-1.524 1.712-.47 0-15.29-.003-16.055 0a1.724 1.724 0 001.524-1.712z"
        fill="#fff"
        stroke="#9AC7F7"
        strokeWidth={0.401873}
        strokeMiterlimit={10}
      />
      <Path
        d="M64.18 42.338v-.867c0-.884-.665-1.612-1.524-1.711.767.003 15.586 0 16.056 0 .858.1 1.524.827 1.524 1.711v.867H64.181z"
        fill="#C5E1F9"
        stroke="#9AC7F7"
        strokeWidth={0.401873}
        strokeMiterlimit={10}
      />
      <Path
        d="M63.68 45.154h8.811M63.68 46.61h8.811M63.68 48.066h8.811M63.68 49.522h8.811M63.68 50.978h8.811M63.68 52.435h8.811M63.68 53.889h8.811M63.68 55.344h8.811M63.68 56.803h8.811M63.68 58.256h8.811M75.225 45.154h-1.192M75.225 46.61h-1.192M75.225 48.066h-1.192M75.225 49.522h-1.192M75.225 50.978h-1.192M75.225 52.435h-1.192M75.225 53.889h-1.192M75.225 55.344h-1.192M75.225 56.803h-1.192M75.225 58.256h-1.192"
        stroke="#9AC7F7"
        strokeWidth={0.401873}
        strokeLinejoin="round"
      />
      <Path
        d="M23.878 53.711c1.366.344 3.884 1.568 4.412 3.66.79 3.144-.793 7.758 1.713 12.27.8 1.438-13.845 3.372-13.845 3.372l-2.826-15.88s3.53-3.447 4.252-3.68c1.496-.48 3.565-.429 6.295.257l-.001.001z"
        fill="url(#paint1_linear_4931_9991)"
      />
      <Path
        d="M13.308 57.143c-.508 1.27-1.793 8.819-.361 10.599.165.204-.744.525-.58 1.465.028.156.302.905.302.905l1.17.305 3.538-2.013-.678-.824-.32.007s-.303-.282-.219-.629c.084-.346-.811-.908-2.164-6.767-.398-1.725-.576-3.328-.688-3.048z"
        fill="url(#paint2_linear_4931_9991)"
      />
      <Path
        d="M14.675 67.386l1.047-.23.424-.068c.002.287.233.501.233.501l-.812.025-.95.204-1.148.654 1.207-1.086z"
        fill="#ECB60D"
      />
      <Path
        d="M16.172 68.36s-.808-.01-1.497.414c-.524.323-1.007 1.025-.526 1.405.482.38 1.225.735 1.225.735s1.841.05.798-2.554z"
        fill="#C4D1E1"
      />
      <Path
        d="M14.152 70.206l.493-.106s1.696-.153 1.937-.021c.242.133 1.02.659 1.02.659l.16.147s-.378.236-.889-.122c-.512-.357-.554-.083-.554-.083l.086.452-.007.578.08.242s.07.132.014.18c-.161.141-.32.074-.32.074l-.518-.901-.14.98s-.069.132-.231.168c-.151.032-.152-.008-.152-.008l-.117-.25a.318.318 0 01-.103.172c-.129.1-.308.087-.308.087l.048-1.032-.211.524s-.075.13-.232.167c-.171.041-.218.054-.218.054l.162-1.962z"
        fill="#E4EEF3"
      />
      <Path
        d="M22.94 66.167s-1.232 3.118-4.19 3.953c-.513.144-.827.697-.58.875.316.228.75.278 1.492.275 1.547-.004 3.653-.484 5.11-1.893 2.156-2.085 2.835-3.033 2.616-4.083-.217-1.05-4.447.873-4.447.873z"
        fill="#F7B68B"
      />
      <Path
        d="M27.87 56.322c.625 1.03.989 2.683.984 5.05-.005 1.83-1.585 4.83-1.828 5.75-.04.155-.136.814-.136.814l-1.752 1.718-3.513-2.38 1.068-1.64.318.034s.202-.903.148-1.256c-.056-.352.973-5.075 1.481-8.103.292-1.745 2.608-1.008 3.23.012v.001z"
        fill="url(#paint3_linear_4931_9991)"
      />
      <Path
        d="M24.724 65.611l-1.024-.317-.416-.103c-.027.286-.274.48-.274.48l.806.093.93.284 1.089.748-1.111-1.185z"
        fill="#8DCFFF"
      />
      <Path
        d="M18.974 70.113l-1.79-.49s-.507.68-.41.85c.096.168-.002 1.174-.002 1.174l.868.737.706-.235s.048-.307-.194-.336c-.241-.032.163-.206.29-.278.127-.072 1.163-.608.531-1.422z"
        fill="#F7B68B"
      />
      <Path
        d="M21.539 53.055a1.632 1.632 0 01-.998 2.074l-.008.003a1.633 1.633 0 01-2.075-.998l-.55-1.57a1.631 1.631 0 01.998-2.074l.007-.002a1.632 1.632 0 012.074.997l.55 1.57h.002z"
        fill="#E4EEF3"
      />
      <Path
        d="M17.286 55.83a.705.705 0 00.868.49l3.063-.843a.706.706 0 00.493-.868l-1.25-4.536a.706.706 0 00-.869-.492l-3.063.843a.705.705 0 00-.492.868l1.25 4.538z"
        fill="#F7B68B"
      />
      <Path
        d="M16.288 52.23s-.532-.326-.683-.574c-.075-.126-.084-.679.202-.971.286-.292.67-.002.67-.002s-.46.452-.457.574c.004.121.268.973.268.973z"
        fill="#163F59"
      />
      <Path
        d="M21.334 52.029s-.017-1.872-.243-2.358c-.227-.488-.38-.826-1.02-.712-.957.173-.913.376-1.446.217-.531-.16-1.536-.617-1.836-.608-.499.015-1.189.374-.975 1.26.157.654-.207.928-.19.947.058.07.25.097.588.164.967.191 1.502.136 2.107-.215.606-.349 1.719-1.052 1.993-.61.275.444.624 1.933.713 2.03.088.1.219.002.31-.115z"
        fill="#194866"
      />
      <Path
        d="M20.744 53.656a.237.237 0 00.29.165l.662-.183a.235.235 0 00.164-.289l-.334-1.21a.236.236 0 00-.289-.164l-.662.182a.236.236 0 00-.164.29l.333 1.21zM16.796 54.743c.035.126.165.2.29.164l.662-.18a.235.235 0 00.165-.289l-.333-1.211a.236.236 0 00-.29-.164l-.662.182a.237.237 0 00-.165.289l.334 1.21v-.001z"
        fill="#F7B68B"
      />
      <Path
        d="M21.149 52.506l.657 2.382c.148.538.26 1.778-2.783 2.617-1.8.495-2.099-.846-2.219-1.28-.09-.331.01-1.425.148-1.668.004-.007.203.737.203.737s.177-.821 1.033-1.057c1.38-.38 1.917.342 2.521.16.466-.141.608-1 .44-1.89v-.001z"
        fill="#194866"
      />
      <Path
        d="M19.08 55.252l-.945.26a.236.236 0 01-.29-.164l-.036-.132 1.398-.386.037.133a.235.235 0 01-.166.288l.001.001z"
        fill="#F7B68B"
      />
      <Path
        d="M4.136 73.367c.436 1.206 8.078 5.64 10.64 5.378l7.717.368c6.566-.8 9.67-10.996 9.67-10.996-.262-2.56-6.728 1.15-9.032 2.3-3.65 1.822-5.959 1.913-5.959 1.913-2.44.251-5.273-1.633-8.354-1.698-3.386-.073-5.37.837-4.683 2.735h.001z"
        fill="#281616"
      />
      <Path
        d="M20.928 72.141l.006-.01v-.321l-13.286-.242-.007.32v.012l.034.06c.165.281.47.337.822.343l11.562.21c.35.006.658-.038.834-.313l.034-.059h.001z"
        fill="#B6C2C9"
      />
      <Path
        d="M17.725 72.137v.009l.032.05c.051.073.116.126.192.166l2.02.036c.338.007.632-.027.801-.25l.033-.048.006-.009v-.156l-3.08-.056-.004.258z"
        fill="#D8E5ED"
      />
      <Path
        d="M15.934 72.02l-6.64-.12.005-.226 6.638.12-.003.227z"
        fill="#879BA6"
      />
      <Path
        d="M17.66 71.74l-1.604-6.56a.435.435 0 00-.388-.319l-9.322-.17c-.172-.003-.278.134-.235.308l1.605 6.559 9.944.181z"
        fill="#D9E1E4"
      />
      <Path
        d="M12.45 68.224c.083.335-.12.602-.454.596-.333-.006-.67-.284-.752-.617-.081-.336.123-.602.456-.596.333.006.67.282.752.618l-.001-.001z"
        fill="#B6C2C9"
      />
      <Path
        d="M23.673 80.811c-.919.159-1.62-.91-1.62-.91l-2.375-1.869.501-1.264 4.627 2.079s-.213 1.806-1.132 1.964h-.001z"
        fill="#F7B68B"
      />
      <Path
        d="M22.049 79.828s-.002.111.175.598c.178.486.394.656.625.725.886.26 1.525.129 3.658.54 2.133.41 2.773.1 3.397-.882.625-.983-.024-1.754-1.063-1.833-1.507-.115-2.345.058-3.128-.062-.823-.125-.874-.309-1.354-.603-.452-.278-.866 1.371-.44 1.754.376.337-1.549.388-1.87-.238v.001z"
        fill="url(#paint4_linear_4931_9991)"
      />
      <Path
        d="M22.506 80.777c.887.26 1.527.128 3.659.54 2.132.41 2.772.1 3.397-.883.289-.455.302-.86.134-1.176.469.323.629.887.206 1.55-.623.982-1.263 1.292-3.396.882-2.133-.411-2.773-.28-3.659-.54-.177-.053-.346-.168-.495-.439.05.027.101.05.153.065z"
        fill="#D9E1E4"
      />
      <Path
        d="M22.053 79.857c-.004-.02-.004-.029-.004-.029l.02.032c-.006-.001-.011-.003-.017-.003z"
        fill="#F7B68B"
      />
      <Path
        d="M26.66 80.6c.07.024.13-.104.152-.286.04-.368.122-.736.246-1.099.061-.18.06-.34-.014-.36-.076-.02-.196.11-.255.285a5.125 5.125 0 00-.233 1.086c-.018.18.035.345.104.373zM26 80.48c.072.014.138-.112.157-.282.041-.343.121-.69.244-1.034.06-.169.056-.314-.023-.326-.079-.008-.203.118-.26.286-.117.341-.194.684-.23 1.023-.017.167.04.315.111.332zM25.306 80.447c.075.011.15-.122.177-.299a5.28 5.28 0 01.288-1.067c.068-.174.069-.322-.015-.325-.082-.005-.213.132-.279.305a5.154 5.154 0 00-.273 1.056c-.026.174.027.322.102.331z"
        fill="#D9E1E4"
      />
      <Path
        d="M23.512 77.76s-16.327-6.711-17.26-6.72C3.1 71.01 3.823 74.2 5.42 75.7c1.885 1.77 16.219 5.01 16.219 5.01l1.872-2.95z"
        fill="url(#paint5_linear_4931_9991)"
      />
      <Path
        d="M17.035 78.923l4.457-4.224-1.9-1.88-4.865 5.114 2.308.99z"
        fill="#F7B68B"
      />
      <Path
        d="M16.11 75.602s11.342-10.393 12.107-10.704c2.588-1.05 4.194 1.808 4.102 2.795-.118 1.28-11.641 11.69-11.641 11.69L16.109 75.6h.001z"
        fill="#075B89"
      />
      <Path
        d="M9.39 79.256c-.266.246-.312.965-.312.965s3.785 1.432 5.115 1.458c.346.007.967-.131 1.563-.297.1-.026.33-.286.428-.315.182-.053.223.123.38.074.402-.13.69-.238.7-.259.8-1.7.53-2.535.53-2.535s-1.74.497-1.442.109c.637-.833-1.186-1.686-1.592-1.243-1.62 1.774-4.602 1.336-5.37 2.043z"
        fill="url(#paint6_linear_4931_9991)"
      />
      <Path
        d="M9.18 79.608c1.163.462 3.607 1.417 4.094 1.45 1.992.142 3.385-.566 4.315-.982-.082.242-.186.51-.325.804-.01.02-.298.13-.7.259-.157.05-.198-.127-.38-.074-.097.029-.328.289-.428.315-.596.166-1.217.304-1.563.297-1.329-.026-5.115-1.458-5.115-1.458s.01-.326.101-.612l.001.001zM11.854 78.509c-.033.065.114.142.326.188.43.087.86.217 1.278.387.208.086.396.105.425.032.027-.073-.116-.21-.32-.29a7.056 7.056 0 00-1.264-.374c-.21-.042-.41-.008-.445.057zM12.704 78.209c-.032.068.113.162.32.221.418.122.832.284 1.235.486.198.1.38.132.407.056.025-.076-.11-.228-.308-.324a7.24 7.24 0 00-1.224-.47c-.205-.057-.398-.037-.43.03zM13.522 77.945c-.03.07.11.179.311.256.406.153.805.347 1.191.578.191.114.366.156.39.077.024-.078-.106-.245-.295-.356a7.231 7.231 0 00-1.182-.563c-.2-.073-.386-.063-.415.007z"
        fill="#D9E1E4"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_4931_9991"
          x1={42.4642}
          y1={74.1607}
          x2={25.5507}
          y2={31.3806}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#6FA9DB" />
          <Stop offset={1} stopColor="#A5DBF6" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_4931_9991"
          x1={25.3459}
          y1={71.967}
          x2={20.6693}
          y2={53.2562}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#8BB3D6" />
          <Stop offset={1} stopColor="#CBD4DE" />
        </LinearGradient>
        <LinearGradient
          id="paint2_linear_4931_9991"
          x1={15.9492}
          y1={69.7169}
          x2={10.3324}
          y2={59.4952}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#638FB6" />
          <Stop offset={1} stopColor="#CBD4DE" />
        </LinearGradient>
        <LinearGradient
          id="paint3_linear_4931_9991"
          x1={26.8247}
          y1={68.8964}
          x2={21.7002}
          y2={56.6429}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#638FB6" />
          <Stop offset={1} stopColor="#CBD4DE" />
        </LinearGradient>
        <LinearGradient
          id="paint4_linear_4931_9991"
          x1={23.4416}
          y1={79.3528}
          x2={25.4338}
          y2={84.2685}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#00497C" />
          <Stop offset={1} stopColor="#1D9AD4" />
        </LinearGradient>
        <LinearGradient
          id="paint5_linear_4931_9991"
          x1={2.68343}
          y1={71.0104}
          x2={3.08938}
          y2={79.6556}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#00497C" />
          <Stop offset={1} stopColor="#1D9AD4" />
        </LinearGradient>
        <LinearGradient
          id="paint6_linear_4931_9991"
          x1={10.5842}
          y1={78.4645}
          x2={13.4362}
          y2={84.4157}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#00497C" />
          <Stop offset={1} stopColor="#1D9AD4" />
        </LinearGradient>
      </Defs>
    </Svg>
  )
}

export default SymptomTrackerLogo;
