import * as React from "react"
import Svg, { Rect, Path, SvgProps } from "react-native-svg"

function MyProgressIcon(props: SvgProps) {
  return (
    <Svg
      width={35}
      height={35}
      viewBox="0 0 35 35"
      fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Rect width={35} height={35} rx={6} fill="#5351E8" fillOpacity={0.1} />
      <Path
        d="M10.016 14.793c.149.149.39.149.539 0l2.207-2.207 2.207 2.207a.38.38 0 00.538-.538l-2.206-2.207 2.206-2.207a.38.38 0 00-.538-.539l-2.207 2.207-2.207-2.207a.38.38 0 00-.539.539l2.207 2.207-2.207 2.207a.381.381 0 000 .538zM23.888 19.969a.381.381 0 00-.538 0l-2.207 2.207-2.207-2.207a.38.38 0 00-.539.538l2.207 2.207-2.207 2.207a.381.381 0 00.53.548l.009-.01 2.207-2.206 2.207 2.207a.38.38 0 00.538-.539l-2.207-2.207 2.207-2.207a.381.381 0 000-.538z"
        fill="#5351E8"
      />
      <Path
        d="M12.85 25.571a3.08 3.08 0 002.955-2.88 3.048 3.048 0 00-2.662-3.188v-.789c0-.631.511-1.143 1.143-1.143h5.333a1.905 1.905 0 001.905-1.904V10.5c0-.002.002-.004.004-.004 0 0 .002 0 .002.002l2.01 2.01a.38.38 0 00.539-.54l-2.667-2.666c-.003-.003-.008-.004-.012-.008a.388.388 0 00-.111-.076c-.003 0-.007 0-.01-.002a.37.37 0 00-.252-.003.34.34 0 00-.153.09l-2.666 2.666a.38.38 0 00.538.538l2.01-2.01a.004.004 0 01.006 0v5.17c0 .63-.51 1.143-1.142 1.143h-5.334a1.905 1.905 0 00-1.905 1.904v.789a3.048 3.048 0 00.469 6.068zm-.088-5.333a2.286 2.286 0 110 4.571 2.286 2.286 0 010-4.57z"
        fill="#5351E8"
      />
      <Path
        d="M4.934 23.286h.97v3.047h-.97a.936.936 0 00-.934.934v.08c0 .492.4.89.891.891h.252a.381.381 0 000-.762H4.89a.13.13 0 01-.13-.129v-.08c0-.094.078-.171.173-.172h.97v2.286c0 .21.171.38.382.38h21.333c.21 0 .381-.17.381-.38v-24c0-.21-.17-.381-.381-.381H6.286c-.21 0-.381.17-.381.381v1.905h-.97A.936.936 0 004 8.22v.08c0 .491.4.89.891.89h.252a.381.381 0 000-.761H4.89a.13.13 0 01-.13-.13v-.08c0-.094.078-.171.173-.171h.97v3.047h-.97A.936.936 0 004 12.03v.08c0 .492.4.89.891.891h.252a.381.381 0 000-.762H4.89a.13.13 0 01-.13-.129v-.08c0-.095.078-.172.173-.172h.97v3.048h-.97a.936.936 0 00-.934.934v.08c0 .491.4.89.891.89h.252a.381.381 0 000-.761H4.89a.13.13 0 01-.13-.13v-.08c0-.094.078-.171.173-.171h.97v3.047h-.97a.936.936 0 00-.934.934v.08c0 .492.4.89.891.891h.252a.381.381 0 000-.762H4.89a.13.13 0 01-.13-.129v-.08c0-.095.078-.172.173-.172h.97v3.048h-.97a.936.936 0 00-.934.934v.08c0 .491.4.89.891.89h.252a.381.381 0 000-.761H4.89a.13.13 0 01-.13-.13v-.08c0-.094.078-.17.173-.171zM6.667 5.762h20.571V29H6.667V5.762z"
        fill="#5351E8"
      />
    </Svg>
  )
}

export default MyProgressIcon;
