import * as React from "react"
import Svg, { Rect, Path, SvgProps } from "react-native-svg"

function HandIcon(props: SvgProps) {
  return (
    <Svg
    //   xmlns="http://www.w3.org/2000/svg"
      width={45}
      height={46}
      viewBox="0 0 45 46"
      fill="none"
      {...props}
    >
      <Rect y={0.259766} width={45} height={45} rx={5} fill="#0B79D3" />
      <Path
        d="M32.911 11.568c-1.16 0-2.088.914-2.088 2.056v8.907c0 .229-.232.457-.464.457s-.464-.228-.464-.457V9.056c0-1.142-.928-2.055-2.089-2.055-1.16 0-2.088.913-2.088 2.055v13.475c0 .229-.232.457-.464.457-.233 0-.465-.228-.465-.457V6.27c0-1.097-.928-2.01-2.088-2.01-1.16 0-2.089.913-2.089 2.01V22.53c0 .229-.232.457-.464.457s-.464-.228-.464-.457V9.056c0-1.142-.928-2.055-2.088-2.055-1.16 0-2.089.913-2.089 2.055v18.729l-1.903-8.451c-.278-1.233-1.531-2.01-2.784-1.736-1.254.274-2.043 1.507-1.764 2.74l2.785 12.334c.139.457.417.914.881 1.28l5.106 3.882v3.426h13.46v-2.284c0-3.243 3.712-3.472 3.712-9.593V13.624c0-1.142-.928-2.056-2.089-2.056z"
        fill="#fff"
      />
    </Svg>
  )
}

export default HandIcon;
