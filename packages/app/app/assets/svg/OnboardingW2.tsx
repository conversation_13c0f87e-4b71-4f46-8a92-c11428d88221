import * as React from "react";
import Svg, { Path, Text as SvgText, SvgProps } from "react-native-svg";

interface QuestionBubbleProps extends SvgProps {
  message?: string; // New prop for dynamic text
  xMargin?: number;
  yMargin?: number;
  dy?: number;
  dx?: number;
  dxMarginPercent?: string;
  height?: number;
  width?: number;
}

function OnboardingW2({
  message = "Just 6 quick questions to make this a personalized experience for you!",
  xMargin = 0,
  yMargin = 20,
  dy = 12,
  dx = 18,
  dxMarginPercent = '12%',
  height = 129,
  width = 258,
  ...props
}: QuestionBubbleProps) {
  const fontSize = 18; // Font size
  const padding = 10; // Padding on both sides
  const bubbleWidth = width; // Fixed width
  const bubbleHeight = height; // Fixed height

  // Word wrapping logic
  const maxTextWidth = bubbleWidth - padding * 0.5; // Text area width
  const words = message.split(" ");
  const lines: string[] = [];
  let currentLine = "";

  // Split the message into multiple lines based on the available width
  words.forEach((word) => {
    // Approximation: 0.6 * fontSize for average character width
    if ((currentLine + word).length * fontSize * 0.5 < maxTextWidth) {
      currentLine += word + " ";
    } else {
      lines.push(currentLine.trim());
      currentLine = word + " ";
    }
  });

  if (currentLine) lines.push(currentLine.trim()); // Add the last line

  // Calculate line height
  const lineHeight = fontSize * 1.2;

  return (
    <Svg
      width={bubbleWidth}
      height={bubbleHeight}
      viewBox={`0 0 ${bubbleWidth} ${bubbleHeight}`}
      fill="none"
      {...props}
    >
      {/* Speech Bubble Path */}
      <Path
        d="M1 15C1 7.268 7.268 1 15 1h228c7.732 0 14 6.268 14 14v66.058c0 7.514-5.931 13.688-13.439 13.99l-32.858 1.317a4.001 4.001 0 00-2.879 1.396l-24.719 28.887c-1.086 1.269-3.158.726-3.482-.913l-5.158-26.12a4 4 0 00-3.879-3.225L14.842 94.636C7.172 94.549 1 88.307 1 80.636V15z"
        stroke="#B4DDFF"
        strokeWidth={2}
      />
      {/* Dynamic Text with Wrapping */}
      {lines.map((line, index) => (
        <SvgText
          key={index}
          x={index === lines.length - 1 ? dxMarginPercent : xMargin + padding} // Center last line
          y={yMargin + index * lineHeight} // Start from top with vertical spacing
          textAnchor="start" // Align text to the left
          fill="#004987"
          fontSize={fontSize}
          dx={dx}
          dy={dy}
        >
          {line}
        </SvgText>
      ))}
    </Svg>
  );
}

export default OnboardingW2;
