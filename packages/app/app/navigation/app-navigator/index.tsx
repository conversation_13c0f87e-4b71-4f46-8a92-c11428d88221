import HomeNavTabBarButton from "@components/navigation/HomeNavTabBarButton";
import ProfileNavigator from "@navigation/profile-navigator";
import ServicesNavigator from "@navigation/services-navigator/profile-navigator";
import {
  BottomTabNavigationOptions,
  createBottomTabNavigator,
} from "@react-navigation/bottom-tabs";

import { AppNavParams } from "./AppNavParams";
import { Platform } from "react-native";
import ConnectNavigator from "@navigation/connect-navigator";
import LearnNavigator from "@navigation/learn-navigator";
import ManageNavigator from "@navigation/manage-navigator";

const Tab = createBottomTabNavigator<AppNavParams>();

const screenOptions: BottomTabNavigationOptions = {
  tabBarStyle: {
    height: Platform.OS === "ios" ? 75 : 70,
    paddingBottom: Platform.OS === "ios" ? 20 : 10,
  },
  unmountOnBlur: true,
  headerShown: false,
};

const options: BottomTabNavigationOptions = {
  tabBarButton: (props) => <HomeNavTabBarButton {...props} />,
};

export default function AppNavigator() {
  return (
    <Tab.Navigator screenOptions={screenOptions}>
      <Tab.Screen
        name="ManageNavigator"
        component={ManageNavigator}
        options={options}
      />
      <Tab.Screen
        name="LearnNavigator"
        component={LearnNavigator}
        options={options}
      />
      <Tab.Screen
        name="ConnectNavigator"
        component={ConnectNavigator}
        options={options}
      />
      <Tab.Screen
        name="ServicesNavigator"
        component={ServicesNavigator}
        options={options}
      />
      <Tab.Screen
        name="ProfileNavigator"
        component={ProfileNavigator}
        options={options}
      />
    </Tab.Navigator>
  );
}
