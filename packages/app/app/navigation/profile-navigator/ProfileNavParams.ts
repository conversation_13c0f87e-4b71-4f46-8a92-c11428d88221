import { EditUserFormData } from "@screens/profile/UserProfileScreen";
import { createCaregiverValidator } from "../../../../shared/validators/user.validator";
import { z } from "zod";

interface Option {
  label: string;
  value: number;
}

type FormData = z.infer<typeof createCaregiverValidator>;

export type ProfileNavParams = {
  ProfileScreen: undefined;
  HealthProfileListScreen: { userOrPatientId: string };
  HealthProfileScreen: { userOrPatientId: string; healthProfileId?: string };
  CreateProfileScreen: { userOrPatientId: string };
  FindPatientScreen?: undefined;
  CreatePatientScreen?: {
    patientData?: {};
    fullName?: string;
    email?: string;
    mobilePhone?: string;
    zipcode?: string;
    gender?: Option[];
    race?: Option[];
  };
  Caregiver: undefined;
  CreateCaregiverPatient: { userOrPatientId: string };
  FindCaregiverPatient: undefined;
  HealthProfileCaregiver: { patientData: FormData; userOrPatientId: string };
  HealthUserEditScreen?: Partial<EditUserFormData>;
  ContactScreen: undefined;
  PolicyScreen: undefined;
  RelationshipWithPatientScreen: { username: string };
  PatientUserHealthProfile: { userOrPatientId: string };
  ActivityScreen: undefined;
};
