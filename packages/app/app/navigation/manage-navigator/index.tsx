import NavigationHeader from "@components/navigation/NavigationHeader";
import { Entypo } from "@expo/vector-icons";
import {
  createStackNavigator,
  StackNavigationOptions,
} from "@react-navigation/stack";

import ManageCalendarScreen from "@screens/manage/ManageCalendarScreen";
import ManageHomeScreen from "@screens/manage/ManageHomeScreen";
import ManageTopicListsScreen from "@screens/manage/ManageTopicListsScreen";
import ManageTopicQuestionsList from "@screens/manage/ManageTopicQuestionsList";
import React from "react";
import { ManageNavParams, ManageTopicNavParams } from "./ManageNavParams";
import BlogDetailsScreen from "@screens/learn/BlogDetailsScreen";
import ManageStreakScreen from "@screens/manage/ManageStreak";
import OnboardingDailyCheckInProcessed from "@components/onboarding/OnboardingDailyCheckInProcessed";
import OnboardingTopics from "@components/onboarding/OnboardingTopics";
import OnboardingQuestion from "@components/onboarding/OnboardingQuestions";
import OnboardingProvider from "../../components/onboarding/OnboardingProvider";
import OnboardingWelcomeSplashScreen from "@components/onboarding/OnboardingWelcomeSplashScreen";
import ManageLearningSceen from "@components/manage/ManageLearningSceen";

const options: StackNavigationOptions = {
  header: NavigationHeader,
  headerLeft: () => (
    <Entypo name="chevron-small-left" size={24} color="black" />
  ),
  headerShown: false,
  cardStyle: { backgroundColor: "#fff" },
};

const ManageStackNav = createStackNavigator<ManageNavParams>();
const Stack = createStackNavigator<ManageTopicNavParams>();

// function TopicQuestionsNavigator() {
//   return (
//     <Stack.Navigator screenOptions={{ ...options }}>
//       <Stack.Screen
//         name="TopicQuestionsList"
//         component={ManageTopicQuestionsList}
//         options={{ headerShown: false }}
//       />
//     </Stack.Navigator>
//   );
// }

export default function ManageNavigator() {
  return (
    <ManageStackNav.Navigator screenOptions={{ ...options }}>
      <ManageStackNav.Screen
        name="ManageHomeScreen"
        component={ManageHomeScreen}
        options={{ headerShown: false }}
      />

      <ManageStackNav.Screen
        name="TopicsListScreen"
        component={ManageTopicListsScreen}
        options={{ headerShown: false }}
      />
      <ManageStackNav.Screen
        name="TopicQuestionsNavigator"
        component={ManageTopicQuestionsList}
        options={{ headerShown: false }}
      />

      <ManageStackNav.Screen
        name="BlogDetailsScreen"
        component={BlogDetailsScreen}
        options={{
          headerShown: true,
          headerTitle: "Learn",
        }}
      />
      <ManageStackNav.Screen
        name="ManageCalendarScreen"
        component={ManageCalendarScreen}
        options={{ headerShown: false }}
      />
      <ManageStackNav.Screen
        name="ManageStreakScreen"
        component={ManageStreakScreen}
        options={{ headerShown: false }}
      />
      <ManageStackNav.Screen
        name="OnboardingWelcomeSplashScreen"
        component={OnboardingWelcomeSplashScreen}
        options={{ headerShown: false }}
      />
      <ManageStackNav.Screen
        name="OnboardingDailyCheckInProcessed"
        component={OnboardingDailyCheckInProcessed}
        options={{ headerShown: false }}
      />
      <ManageStackNav.Screen
        name="OnboardingTopics"
        component={OnboardingTopics}
        options={{ headerShown: false }}
        initialParams={{ hideTabBar: true }}
      />
      <ManageStackNav.Screen
        name="ManageLearningScreen"
        component={ManageLearningSceen}
        options={{ headerShown: false }}
        initialParams={{ hideTabBar: true }}
      />
      <Stack.Screen
        name="OnboardingProvider"
        component={OnboardingProvider}
        options={{ gestureEnabled: false }}
      />
      <ManageStackNav.Screen
        name="OnboardingQuestion"
        component={OnboardingQuestion}
        options={{ headerShown: false }}
      />
    </ManageStackNav.Navigator>
  );
}
