export type ManageNavParams = {
  ManageHomeScreen?: { startOfWeek?: string; topicId?: string };
  TopicsListScreen: { fromConnectScreen?: boolean };
  TopicQuestionsNavigator: { topicId: string };
  ManageCalendarScreen: undefined;
  BlogDetailsScreen: { blog: string };
  OnboardingDailyCheckInProcessed?: {
    streakCount?: number | string;
    dailyTrackers?: any;
    isDailyCheckInCompleted?: any;
  };
  ManageStreakScreen?: {
    streakCount?: number | string;
    dailyTrackers?: any;
    isDailyCheckInCompleted?: any;
  };
  OnboardingTopics?: { fromManageScreen?: boolean | any };
  OnboardingQuestion?: {
    fromManageScreen?: boolean | any;
    topicId?: any;
    currentIndex?: number;
    questionAnswer?: any;
  };
  OnboardingProvider?: { fromManageScreen?: boolean | any };
  OnboardingWelcomeSplashScreen?: undefined;
  ManageLearningScreen: {
    topicId?: string;
    source?: "onboarding" | "daily";
  };
};

export type ManageTopicNavParams = {
  TopicQuestionsList: { topicId: string, fromConnectScreen?: boolean  };
};
