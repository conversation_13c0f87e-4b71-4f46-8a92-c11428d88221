import { PostTypes } from "../../../../shared/types/Connect";
import { RabbleGroup, RabbleGroupUserRole } from "../../models";

type Contact = {
  id: string;
  name: string;
  phoneNumbers: string;
  email: string;
  userId?: any | undefined;
};

export type ConnectNavParams = {
  ConnectPosts: undefined;
  RabbleGroups: { selectedTab?: any | undefined };
};

export type ConnectPostNavParams = {
  ConnectScreen: undefined;
  RabbleGroups: { selectedTab?: any | undefined };
  ManageHomeScreen?: { startOfWeek?: string; topicId?: string };
  CreateNewRabble?: { fromConnectScreen?: boolean };
  GroupPrivacyPreferences?: { fromConnectScreen?: boolean };
  RabbleTagAssociation?: { fromConnectScreen?: boolean };
  OnboardingTopics?: { fromManageScreen?: boolean | any };
  OnboardingQuestion: {
    topicId?: any;
    currentIndex?: number;
    questionAnswer?: any;
    fromManageScreen?: boolean | any;
  };
  ManageStreakScreen?: {
    streakCount?: number | string;
    dailyTrackers?: any;
    isDailyCheckInCompleted?: any;
  };
  RabbleDetailsScreenV2: RabbleGroup;
  PostOrGroupPostDetailsScreen: {
    postId: string;
    postType: "CONNECT" | "PRIVATE" | "PUBLIC";
    groupId?: string;
    groupRole?: RabbleGroupUserRole;
  };
  OnboardingContactList: {
    contacts?: Contact | Contact[];
    groupId?: string;
    fromConnectScreen?: boolean;
  };
  CreatePostScreen: {
    postType: PostTypes;
    rabbleGroup?: string;
    connectPost?: any;
    contactUserId?: any;
    isEditMode?: boolean;
  };
};

export type RabbleGroupsNavParams = {
  RabbleGroupsScreen: undefined;
};
