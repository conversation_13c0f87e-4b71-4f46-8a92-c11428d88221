import NavigationHeader from "@components/navigation/NavigationHeader";
import { Entypo } from "@expo/vector-icons";
import {
  createStackNavigator,
  StackNavigationOptions,
} from "@react-navigation/stack";
import Services from "@screens/services";
import ServicesListing from "@screens/services/ServicesListing";

import { ServicesNavParams } from "./ServicesNavParams";
import ServicesDetail from "@screens/services/ServicesDetail";

const options: StackNavigationOptions = {
  header: NavigationHeader,
  headerLeft: () => (
    <Entypo name="chevron-small-left" size={24} color="black" />
  ),
  headerShown: false,
  cardStyle: { backgroundColor: "#fff" },
};

const Stack = createStackNavigator<ServicesNavParams>();

export default function ServicesNavigator() {
  return (
    <Stack.Navigator screenOptions={{ ...options }}>
      <Stack.Screen
        name="ServicesScreen"
        component={Services}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ServicesListingScreen"
        component={ServicesListing}
        options={{
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="ServicesDetailScreen"
        component={ServicesDetail}
        options={{
          headerShown: true,
          headerTitle: "details",
        }}
      />
    </Stack.Navigator>
  );
}
