import Rocket from "@assets/svg/connect/Rocket";
import AppText from "@components/common/AppText";
import AppScrollView from "@components/common/AppScrollView";
import AppTextInput from "@components/common/AppTextInput";
import ConnectPost from "@components/connect/Post";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { RouteProp, useRoute } from "@react-navigation/native";

import { useCallback, useRef, useState } from "react";
import { ActivityIndicator, Platform, View, Clipboard } from "react-native";
import useKeyboard from "@hooks/useKeyboard";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import { PostComment } from "@components/connect/PostComment";
import InfoModal from "@components/common/InfoModal";
import ModalBottomSheet from "@components/common/ModalBottomSheet";
import SectionLink from "@components/profile/ProfileSectionItem";
import Trash from "@assets/svg/connect/Trash";
import Report from "@assets/svg/connect/Report";
import Toast from "react-native-toast-message";
import errorHandler from "@utils/errorhandler";
import { Comment } from "../../../../shared/types/Connect";
import { RouterOutput } from "../../../../shared";
import Pressable from "@components/common/Pressable";
import mixpanel from "@utils/mixpanel";
import { Share } from "react-native";

export default function PostDetailsScreen() {
  const {
    params: { postId, groupId, groupRole },
  } = useRoute<RouteProp<RootNavParams, "PostDetailsScreen">>();
  const { user } = useSession();
  const {
    data: comments,
    isLoading: isCommentsLoading,
    refetch: refetchComments,
  } = trpc.connect.getComments.useQuery({ post: postId });
  const groupInfo = trpc.rabbleGroups.getGroupInfo.useQuery(
    {
      group: groupId || "",
    },
    { enabled: !!groupId }
  );

  const {
    data: post,
    isLoading: isPostsLoading,
    refetch: refetchPost,
  } = trpc.connect.getPosts.useInfiniteQuery(
    { limit: 30, postId, user: user?._id as string },
    {
      initialCursor: 0,
    }
  );

  const { status: createCommentStatus, mutateAsync: createCommentAsync } =
    trpc.connect.createComment.useMutation();
  const getUserById = trpc.user.getUserById.useMutation();

  // State
  const [comment, setComment] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedComment, setSelectedComment] =
    useState<RouterOutput["connect"]["getComments"][number]>();
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [replyToComment, setReplyToComment] = useState<Comment | null>(null);
  const inputRef = useRef<any>(null);

  const handleCommentOptions = useCallback((comment: Comment) => {
    setSelectedComment(comment);
    setIsModalOpen(true);
    setDeleteModalVisible(false);
    setReportModalVisible(false);
  }, []);

  const handleContextMenuClick = (
    _comment: Comment,
    mode?: "reply" | "report" | "delete"
  ) => {
    if (mode === "reply") {
      mixpanel.trackEvent(
        "Comment replied",
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
          // reply_content: comment || "",
          comment_id: _comment?._id?.toString() || "",
          post_id: postId || "",
          post_content: _comment?.comment,
        },
        String(user?._id),
        "v2"
      );
      setReplyToComment(_comment);
      setTimeout(() => inputRef.current?.focus(), 100);
    }

    if (mode === "report") {
      setReportModalVisible(true);
    }

    if (mode === "delete") {
      setDeleteModalVisible(true);
    }
  };

  const deleteAction = () => {
    // setIsModalOpen(false);
    setDeleteModalVisible(true);
  };

  const reportAction = () => {
    setIsModalOpen(false);
    setReportModalVisible(true);
  };

  const handleCreateComment = async () => {
    createCommentAsync(
      {
        post: postId,
        comment,
        ...(replyToComment?._id
          ? { repliedTo: replyToComment?._id?.toString() }
          : {}),
      },
      {
        onSuccess() {
          setComment("");
          setReplyToComment(null);
          refetchComments();
        },
      }
    );
    let groupUser = null;
    if (groupInfo?.data?.createdBy) {
      groupUser = await getUserById.mutateAsync(
        String(groupInfo?.data?.createdBy)
      );
    }
    // const groupUser = groupInfo?.data?.createdBy && await getUserById.mutateAsync(String(groupInfo?.data?.createdBy) || "")
    console.log({ postData: post?.pages?.at(0)?.posts?.at(0) });
    await mixpanel.trackEvent(
      "Post comment posed (Rabble screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        post_content: comment || "",
        post_username:
          post?.pages?.at(0)?.posts?.at(0)?.user[0]?.username || "",
        post_id: postId || "",
        time: new Date().toISOString(),
        group_title: groupInfo?.data?.groupName || "",
        group_status: !groupInfo?.data?.groupName ? "Inactive" : `Active`,
        group_admin:
          `${groupUser?.firstname || ""} ${groupUser?.lastname || ""}` || "",
        group_id: groupId || "",
      },
      String(user?._id),
      "v2"
    );
    console.log("commented");
  };

  const { mutateAsync: likePost, isLoading: isLikeLoading } =
    trpc.connect.likePost.useMutation();

  const _deleteComment = trpc.connect.deleteComment.useMutation();
  const _reportComment = trpc.connect.reportComment.useMutation();

  const likeComment = trpc.connect.likeComment.useMutation();

  const handleDeleteComment = async () => {
    try {
      await _deleteComment.mutate(selectedComment?._id!, {
        onSettled: () => {
          refetchComments();
          setDeleteModalVisible(false);
          setIsModalOpen(false);
        },
      });

      // handle toast
      Toast.show({
        type: "success",
        text1: "comment deleted successfully",
        visibilityTime: 2000,
      });
    } catch (ex) {
      errorHandler(ex);
    }
  };

  const handleReportComment = async () => {
    await _reportComment.mutate({ comment: selectedComment?._id! });
    // handle toast
    Toast.show({
      type: "success",
      text1: "comment reported successfully",
      visibilityTime: 2000,
    });
    setReportModalVisible(false);
  };

  const keyboard = useKeyboard();

  if (isCommentsLoading || isPostsLoading)
    return (
      <View className="flex flex-col flex-1 justify-center items-center">
        <View className="flex flex-row items-center">
          <ActivityIndicator color="#FF8E1C" className="mr-2" />
          <AppText className="font-montserratSemiBold text-[#ff8e1c]">
            loading post
          </AppText>
        </View>
      </View>
    );

  if (!post) return null;
  return (
    <View className="flex flex-1">
      <AppScrollView
        className="mb-14"
        contentContainerStyle={{
          marginHorizontal: 16,
        }}
      >
        <ConnectPost
          {...post?.pages?.at(0)?.posts?.at(0)}
          isLikeLoading={isLikeLoading}
          onLike={() =>
            likePost(
              {
                post: postId,
                status: !post?.pages.at(0)?.posts.at(0)?.isLiked,
              },
              {
                onSettled() {
                  refetchPost();
                },
              }
            )
          }
        />

        <View className="mt-4 mb-6">
          {comments?.map((comment) => (
            <PostComment
              key={comment._id}
              comments={comments}
              commentOptionsCallback={handleCommentOptions}
              onlikeComment={async (post, status, emogi?: any) => {
                await likeComment.mutateAsync(
                  {
                    comment: comment._id as string,
                    status: emogi && status ? status : !status,
                    reactionType: emogi || "",
                  },
                  {
                    onSuccess: () => {
                      refetchComments();
                    },
                  }
                );
              }}
              contextMenus={[
                {
                  label: "Reply",
                  icon: "➡️",
                  onClick: () => handleContextMenuClick(comment, "reply"),
                },
                {
                  label: "Forward",
                  icon: "⏩",
                  onClick: () => {
                    mixpanel.trackEvent(
                      "Comment forwarded",
                      {
                        email: user?.email || "",
                        phone: user?.contact?.phone || "",
                        // message: comment.comment || "",
                        comment_id: comment?._id?.toString() || "",
                        post_id: postId || "",
                        forwarded_comment_content: comment.comment,
                      },
                      String(user?._id),
                      "v2"
                    );
                    Share.share({
                      message: comment.comment ?? "Check this out!",
                    });
                  },
                },
                {
                  label: "Copy",
                  icon: "📃",
                  onClick: () => {
                    mixpanel.trackEvent(
                      "Comment Copied",
                      {
                        email: user?.email || "",
                        phone: user?.contact?.phone || "",
                        // message: comment.comment || "",
                        comment_id: comment?._id?.toString() || "",
                        post_id: postId || "",
                        copy_comment_content: comment?.comment || "",
                      },
                      String(user?._id),
                      "v2"
                    );
                    Clipboard.setString(comment?.comment);
                  },
                },
                { label: "Star", icon: "⭐️" },
                ...(String(user?._id) === String(comment?.user?._id)
                  ? [
                      {
                        label: "Delete",
                        icon: "🗑",
                        color: "#E81448",
                        onClick: () => {
                          mixpanel.trackEvent(
                            "Comment deleted",
                            {
                              email: user?.email || "",
                              phone: user?.contact?.phone || "",
                              // message: comment.comment || "",
                              comment_id: comment?._id?.toString() || "",
                              post_id: postId || "",
                              comment_content: comment?.comment || "",
                            },
                            String(user?._id),
                            "v2"
                          );
                          setTimeout(() => {
                            deleteAction();
                          }, 1000);
                        },
                      },
                    ]
                  : []),
                { label: "More...", icon: "", color: "#0B79D3" },
              ]}
              {...comment}
            />
          ))}
        </View>
      </AppScrollView>
      <View
        className="absolute left-0 right-0 bg-white mx-8 mb-5"
        style={{
          bottom: Platform.OS === "ios" ? keyboard.keyboardHeight : 0,
        }}
      >
        {/* Reply Banner ABOVE the input */}
        {replyToComment && (
          <View className="bg-gray-100 p-2 rounded-t-lg border-t border-gray-300 mb-[-2]">
            <View className="flex-row justify-between items-center">
              <AppText className="text-sm font-bold">
                Replying to {replyToComment.user.username}
              </AppText>
              <Pressable onPress={() => setReplyToComment(null)}>
                <AppText className="text-xs text-red-500">Cancel</AppText>
              </Pressable>
            </View>
            <AppText className="text-xs text-gray-500 mt-1">
              {replyToComment.comment}
            </AppText>
          </View>
        )}

        {/* Input Row */}
        <View className="flex flex-row items-center">
          <AppTextInput
            placeholder="Share your thoughts..."
            wrapperClass="flex flex-row flex-1 items-center"
            onChangeText={setComment}
            value={comment}
          />
          <Pressable
            actionTag="create comment"
            onPress={handleCreateComment}
            className="bg-primary w-[50px] h-[50px] justify-center items-center rounded-lg ml-4"
            disabled={createCommentStatus === "loading"}
            style={{ backgroundColor: comment ? "#004987" : "#ccc" }}
          >
            {createCommentStatus === "loading" ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Rocket />
            )}
          </Pressable>
        </View>
      </View>

      {/* Render ModalBottomSheet */}
      <ModalBottomSheet
        visible={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      >
        {/* Content of the modal */}
        <View className="my-6 mx-2">
          <AppText className="font-montserratMedium text-xl my-2">
            user setting
          </AppText>
          <View>
            {user?._id === selectedComment?.user?._id ||
            (groupId && (groupRole === "admin" || groupRole === "owner")) ? (
              <SectionLink
                title="delete comment"
                icon={<Trash />}
                onPress={deleteAction}
              />
            ) : (
              <SectionLink
                title="report this comment"
                icon={<Report />}
                onPress={reportAction}
              />
            )}
          </View>
        </View>
      </ModalBottomSheet>
      {/* Modal for Delete*/}
      <InfoModal
        visible={deleteModalVisible}
        label="delete comment"
        description="Are you sure you want to delete this comment? This action cannot be undone."
        onRequestClose={setDeleteModalVisible}
        actionText="Yes"
        showLogo={true}
        handleAction={handleDeleteComment}
        secondaryText="No"
      />
      {/* Modal for Report*/}
      <InfoModal
        visible={reportModalVisible}
        label="submit report"
        description="Are you sure you want to report this comment?"
        showLogo={true}
        onRequestClose={setReportModalVisible}
        actionText="Yes"
        handleAction={handleReportComment}
        secondaryText="No"
      />
    </View>
  );
}
