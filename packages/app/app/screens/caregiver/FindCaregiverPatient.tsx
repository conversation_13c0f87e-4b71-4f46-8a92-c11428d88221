import LogoOutline from "@assets/svg/LogoOutline";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import Screen from "@components/common/Screen";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { RouteProp, useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import usePersistedUser from "@hooks/persistUser";
import { z } from "zod";
import { zString } from "@utils/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import AppFormInput from "@components/form/AppFormInput";
import { trpc } from "@providers/RootProvider";

const schema = z.object({
  username: zString,
  usernameOnSubmit: z.string().optional(),
});

type FormData = z.infer<typeof schema>;

export default function FindCaregiverPatient() {
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();
  const { persistedUser } = usePersistedUser();
  const { data: user } = persistedUser;

  const methods = useForm<FormData>({
    defaultValues: {},
    resolver: zodResolver(schema),
    mode: "all",
  });

  const { handleSubmit } = methods;

  const { userFilter } = trpc.user;

  const username: any = methods.watch("usernameOnSubmit");

  // check if patient exists with username in database
  userFilter.useQuery(
    {
      username: username,
      includePatientsOnly: true,
    },
    {
      enabled: !!username?.length,
      onSuccess: (data: any) => {
        if (!data.length) {
          methods.setError("username", {
            message: "patient not found with specified username",
          });
        } else {
          methods.clearErrors("username");
          navigation.navigate("RelationshipWithPatientScreen", {
            username,
          });
        }
      },
    }
  );

  const onSubmit: SubmitHandler<FormData> = async ({ username }) => {
    methods.setValue("usernameOnSubmit", username);
  };

  return (
    <Screen className="mx-4">
      <AppScrollView>
        <LogoOutline className="self-center mt-20" />

        <AppText className="my-8 text-primary font-montserratSemiBold text-2xl">
          Search for someone you want to take care
        </AppText>

        {/* Form */}
        <FormProvider {...methods}>
          <AppFormInput
            name="username"
            placeholder="enter patient username"
            label="enter patient username"
            keyboardType="default"
            autoCapitalize="none"
          />
        </FormProvider>
      </AppScrollView>
      <View className="flex flex-row">
        <AppButton
          title="continue"
          className="mb-4"
          onPress={handleSubmit(onSubmit)}
        />
      </View>
    </Screen>
  );
}
