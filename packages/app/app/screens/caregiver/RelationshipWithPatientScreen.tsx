import LogoOutline from "@assets/svg/LogoOutline";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import Screen from "@components/common/Screen";
import AppFormSelect from "@components/form/AppFormSelect";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import { selectSchema } from "@utils/index";
import usePersistedUser from "@hooks/persistUser";
import { Option } from "@components/common/AppSelect";
import { z } from "zod";
import { zString } from "@utils/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import AppFormInput from "@components/form/AppFormInput";
import { trpc } from "@providers/RootProvider";
import { CaregiverRelationTypes } from '../../../../shared/enums/user';

const schema = z.object({
  relationship: z.nativeEnum(CaregiverRelationTypes),
});

type FormData = z.infer<typeof schema>;

export default function RelationshipWithPatientScreen() {
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();
  const { persistedUser } = usePersistedUser();
  const { data: user } = persistedUser;

  const { params } =
    useRoute<RouteProp<ProfileNavParams, "RelationshipWithPatientScreen">>();

  const methods = useForm<FormData>({
    defaultValues: {},
    resolver: zodResolver(schema),
    mode: "all",
  });


  const { handleSubmit } = methods;

  const reqPatientToCG = trpc.user.requestPatientToBeCareGiver.useMutation();

  const onSubmit: SubmitHandler<FormData> = async ({ relationship }) => {
    // logic here
    reqPatientToCG.mutateAsync({ username: params.username, relationship }, {
      onError({ data, message }) {
        alert(message);
      },
      onSuccess: (data: any) => {
        navigation.navigate("ProfileScreen");
      },
    })
  };

  return (
    <Screen className="mx-4">
      <AppScrollView>
        <LogoOutline className="self-center mt-20" />

        <AppText className="my-8 text-primary font-montserratSemiBold text-2xl">
            Please define your relationship with patient
        </AppText>

        {/* Form */}
        <FormProvider {...methods}>
        <AppFormSelect
            name="relationship"
            placeholder="relationship"
            label="define relationship"
          >
            {Object.keys(CaregiverRelationTypes)
            .filter((key) => isNaN(Number(key)))
            .map((relationType, index) => (
                 <Option
                 key={relationType}
                 label={relationType}
                 value={relationType}
               />
            ))}
          </AppFormSelect>
        </FormProvider>
      </AppScrollView>
      <View className="flex flex-row">
        <AppButton
          title="continue"
          className="mb-4"
          onPress={handleSubmit(onSubmit)}
        />
      </View>
    </Screen>
  );
}
