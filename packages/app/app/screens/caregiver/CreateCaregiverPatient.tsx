import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import { Option, Options } from "@components/common/AppSelect";
import Screen from "@components/common/Screen";
import AppFormSelect from "@components/form/AppFormSelect";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import {
  CompositeNavigationProp,
  useNavigation,
} from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { FormProvider, useForm } from "react-hook-form";
import { View } from "react-native";
import errorHandler from "@utils/errorhandler";
import React from "react";
import AppFormInput from "@components/form/AppFormInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { rawTrpcClient, trpc } from "@providers/RootProvider";
import { createUpdateUserOrPatientProfileValidator } from "../../../../shared/validators/user.validator";
import AppFormCheckbox from "@components/form/AppFormCheckbox";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import AppFormDatePicker from "@components/form/AppFormDatePicker";
import { useSession } from "@hooks/persistUser";
import { zString } from "../../../../shared/validators";
import { debounceAsync } from "@utils/debounceAsync";

const validateEmail = debounceAsync(async (email: string | undefined) => {
  try {
    if (email && z.string().email().parse(email)) {
      const data = await rawTrpcClient.user.userEmailOrPhoneFilter.query({
        email,
      });
      return !data.length;
    }
    return true;
  } catch (err) {
    return true;
  }
}, 500);

const validatePhone = debounceAsync(async (phone: string | undefined) => {
  try {
    if (phone) {
      const data = await rawTrpcClient.user.userEmailOrPhoneFilter.query({
        phone,
      });
      return !data.length;
    }
    return true;
  } catch (err) {
    return true;
  }
}, 500);

const validateUsername = debounceAsync(async (username: string | undefined) => {
  try {
    if (username) {
      const data = await rawTrpcClient.user.userFilter.query({ username });
      return !data.length;
    }
    return true;
  } catch (err) {
    return true;
  }
}, 500);

const createCaregiverValidator = z
  .object({
    username: zString.refine(validateUsername, {
      message: "username is already in use",
    }),
    firstname: zString,
    lastname: zString,
    email: z
      .string()
      .email()
      .or(z.literal(""))
      .optional()
      .refine(validateEmail, { message: "Email is already in use" }),
    contact: z
      .object({
        phone: z
          .string()
          .optional()
          .refine(validatePhone, { message: "Phone is already is use" }),
      })
      .optional(),
    tncConsent: z.literal(true),
    marketingConsent: z.boolean().optional(),
  })
  .merge(createUpdateUserOrPatientProfileValidator)
  .superRefine(({ contact, email }, ctx) => {
    if (!email && !contact?.phone) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["email"],
        message: "email or contact is required",
      });
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["contact", "phone"],
        message: "email or contact is required",
      });
    }
  });

type FormData = z.infer<typeof createCaregiverValidator>;

export default function CreateCaregiverPatient() {
  const navigation =
    useNavigation<
      CompositeNavigationProp<
        StackNavigationProp<ProfileNavParams>,
        StackNavigationProp<RootNavParams>
      >
    >();
  const userProfileOptions = trpc.lib.userProfileOptions.useQuery();
  const utils = trpc.useUtils();
  const states = trpc.lib.states.useQuery();

  const { user } = useSession();

  const methods = useForm<FormData>({
    // @ts-ignore
    resolver: zodResolver(createCaregiverValidator),
    mode: "all",
    defaultValues: {
      address: { city: "", state: "", zip: "" },
      contact: { phone: "" },
      email: "",
      firstname: "",
      lastname: "",
      marketingConsent: false,
      tncConsent: true,
      personal: { dob: undefined, gender: undefined, race: undefined },
      username: "",
      userOrPatientId: user?._id?.toString() as string,
    },
  });

  const { handleSubmit } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      navigation.navigate("HealthProfileCaregiver", {
        // @ts-ignore
        patientData: data,
        userOrPatientId: data?.userOrPatientId as string,
      });
    } catch (ex) {
      errorHandler(ex);
    }
  });

  const handleTC = () => navigation.navigate("TermsConditions");
  const handlePP = () => navigation.navigate("PrivacyPolicy");

  return (
    <Screen className="mx-4">
      <AppScrollView>
        <AppText className="text-primary font-montserratSemiBold text-xl my-10">
          enter patient details
        </AppText>

        {/* Form */}
        <FormProvider {...methods}>
          <View style={{ gap: 15 }}>
            <AppFormInput
              name="firstname"
              placeholder="enter first name"
              label="patient first name"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormInput
              name="lastname"
              placeholder="enter last name"
              label="patient last name"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormInput
              name="username"
              placeholder="enter username"
              label="patient username"
              autoCapitalize="none"
            />

            <AppFormInput
              name="contact.phone"
              placeholder="enter number"
              label="phone number"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormInput
              name="email"
              placeholder="enter email"
              label="email"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormDatePicker
              name="personal.dob"
              label="Date of birth"
              placeholder="d.o.b"
            />
            <AppFormSelect name="personal.race" label="Race" placeholder="race">
              {(Object.values(userProfileOptions.data?.race || {}) || []).map(
                (race) => {
                  return <Option key={race} value={race} label={race} />;
                }
              )}
            </AppFormSelect>
            <AppFormSelect
              name="personal.gender"
              label="Gender"
              placeholder="gender"
            >
              {(Object.values(userProfileOptions.data?.gender || {}) || []).map(
                (gender) => {
                  return <Option key={gender} value={gender} label={gender} />;
                }
              )}
            </AppFormSelect>
            <AppFormSelect
              name="address.state"
              label="state"
              placeholder="state"
            >
              {(Object.values(states.data || {}) || []).map((state) => {
                return <Option key={state} value={state} label={state} />;
              })}
            </AppFormSelect>
            <AppFormInput
              name="address.city"
              placeholder="city"
              label="city"
              autoCapitalize="none"
            />
            <AppFormInput
              name="address.zip"
              maxLength={5}
              placeholder="zip code"
              label="zip code"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormCheckbox
              name="tncConsent"
              label={
                <AppText className="text-sm">
                  by creating a RabbleHealth account, you agree to our{" "}
                  <AppText
                    onPress={handleTC}
                    className="text-sm font-montserratSemiBold text-primary"
                  >
                    terms and conditions
                  </AppText>{" "}
                  &{" "}
                  <AppText
                    onPress={handlePP}
                    className="text-sm font-montserratSemiBold text-primary"
                  >
                    {" "}
                    privacy policy
                  </AppText>
                </AppText>
              }
            />
            <AppFormCheckbox
              name="marketingConsentAccepted"
              label={
                <AppText className="text-sm">
                  Keep me informed of the enhancements and surveys
                </AppText>
              }
            />
          </View>
        </FormProvider>
      </AppScrollView>
      <View className="flex-row mt-4">
        <AppButton
          title="continue"
          className="mb-4"
          onPress={(...args) => {
            console.log({ errors: methods.formState.errors });
            onSubmit(...args);
          }}
        />
      </View>
    </Screen>
  );
}
