import _ from "lodash";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import { Option, Options } from "@components/common/AppSelect";
import Screen from "@components/common/Screen";
import AppFormSelect from "@components/form/AppFormSelect";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import {
  CommonActions,
  RouteProp,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import errorHandler from "@utils/errorhandler";
import { useMemo, useState } from "react";
import React from "react";
import LoadingScreen from "@components/common/LoadingScreen";
import { z } from "zod";
import { createUpdateUserOrPatientHealthProfileValidator } from "../../../../shared/validators/user.validator";
import { zodResolver } from "@hookform/resolvers/zod";
import { trpc } from "@providers/RootProvider";
import AppText from "@components/common/AppText";
import InfoModal from "@components/common/InfoModal";
import {
  Disease,
  ApplierKey,
  DiagnoseStageKey,
} from "../../../../shared/types/user";

type FormData = z.infer<typeof createUpdateUserOrPatientHealthProfileValidator>;

export default function HealthProfileCaregiver() {
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();

  const { patientData, userOrPatientId } =
    useRoute<RouteProp<ProfileNavParams, "HealthProfileCaregiver">>().params;
  const [errorVisible, setErrorVisible] = useState(false);
  const [error, setError] = useState("");

  const utils = trpc.useUtils();
  const healthProfileOptions = trpc.lib.healthProfileOptions.useQuery();

  const methods = useForm<FormData>({
    // @ts-ignore
    resolver: zodResolver(createUpdateUserOrPatientHealthProfileValidator),
    mode: "all",
    defaultValues: {
      userOrPatientId: userOrPatientId,
      additionalApplier: [],
    },
  });

  const disease = useMemo(() => {
    const watchedDisease = methods.watch("disease");
    return _.findKey(
      healthProfileOptions.data?.diseaseTypes,
      (v) => v === watchedDisease
    );
  }, [methods.watch("disease")]) as Disease | undefined;

  const diagnosisTypes = useMemo(
    () => (disease && healthProfileOptions.data?.diagnosisTypes[disease]) || [],
    [disease]
  );

  const selectedDiagnosis = useMemo(() => {
    const watchedDiagnosis = methods.watch("diagnosis");
    if (typeof watchedDiagnosis === "string") {
      return diagnosisTypes.find((_) => _.name === watchedDiagnosis);
    } else {
      return undefined; // or handle the case where watchedCancer is not a string
    }
  }, [methods.watch("diagnosis"), diagnosisTypes]);

  const additionalApplier = useMemo(
    () =>
      _.keys(healthProfileOptions.data?.additionalDiagonsisAppliers).find(
        (key) => methods.watch("diagnosisSubType")?.includes(key)
      ),
    [methods.watch("diagnosisSubType"), healthProfileOptions.data]
  ) as ApplierKey | undefined;

  const diagnosisStageKey = useMemo(
    () =>
      healthProfileOptions.data?.diagnonsisStageFields.find(
        (key) => key === methods.watch("diagnosisSubType")
      ),
    [methods.watch("diagnosisSubType")]
  ) as DiagnoseStageKey | undefined;

  const additionalApplierValues: string[] = useMemo(() => {
    return (
      (additionalApplier &&
        healthProfileOptions.data?.additionalDiagonsisAppliers[
          additionalApplier
        ]) ||
      []
    );
  }, [additionalApplier, healthProfileOptions.data]);

  const { handleSubmit } = methods;

  const cc = trpc.user.createCareGiver.useMutation();
  const patienthealthcaregiver =
    trpc.user.createUpdateUserOrPatientHealthProfile.useMutation();

  // const onSubmit = handleSubmit(async (data) => {
  //   try {
  //     patienthealthcaregiver.mutateAsync(data, {
  //       onError({ data, message }) {
  //         alert(message);
  //       },
  //       onSuccess: () => {
  //         utils.user.getCareGiverPatients.invalidate();
  //         navigation.dispatch(
  //           CommonActions.reset({
  //             index: 0,
  //             routes: [
  //               {
  //                 name: "AppNavigator",
  //               },
  //             ],
  //           })
  //         );
  //       },
  //     });
  //   } catch (ex) {
  //     errorHandler(ex);
  //   }
  // });
  // return console.log("data", patientData.personal);

  const onSubmit = handleSubmit(async (data) => {
    try {
      const { _id } = await cc.mutateAsync(
        { ...patientData },
        {
          onError({ data, message }) {
            setError(message);
            setErrorVisible(true);
          },
          onSuccess: () => {
            utils.user.getCareGiverPatients.invalidate();
          },
        }
      );
      patienthealthcaregiver.mutateAsync(
        { ...data, userOrPatientId: _id as string },
        {
          onError({ data, message }) {
            setError(message);
            setErrorVisible(true);
          },
          onSuccess: () => {
            utils.user.getCareGiverPatients.invalidate();
          },
        }
      ),
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "AppNavigator" }],
          })
        );
    } catch (ex) {
      errorHandler(ex);
    }
  });

  return (
    <View className="flex flex-1 m-3">
      <Screen className="mx-4">
        <AppScrollView>
          <AppText className="text-primary font-montserratSemiBold text-xl my-10">
            tell us a bit about patient, so that we can help in the journey.
          </AppText>
          <FormProvider {...methods}>
            <View style={{ gap: 8 }}>
              <AppFormSelect
                name="cancerJourneyStage"
                placeholder="role"
                label="how would you describe yourself?"
              >
                {_.reject(
                  healthProfileOptions.data?.cancerJourneyStage || [],
                  (v) => v == "supporting a loved one navigating cancer"
                ).map((journeyStage, idx) => (
                  <Option key={idx} label={journeyStage} value={journeyStage} />
                ))}
              </AppFormSelect>
              <AppFormSelect
                name="disease"
                placeholder="where should we focus?"
                label="where should we focus?"
              >
                {_.entries(healthProfileOptions.data?.diseaseTypes).map(
                  ([key, value]) => (
                    <Option key={key} label={_.toString(value)} value={value} />
                  )
                )}
              </AppFormSelect>

              <AppFormSelect
                name="diagnosis"
                placeholder="what is your diagnosis?"
                label={`what is patient diagnosis?`}
                // TODO: onPressIn={() => methods.setValue("cancerSubType", "")}
              >
                {diagnosisTypes.map((diagnosis, idx: any) => (
                  <Option
                    key={idx}
                    label={diagnosis.name}
                    value={diagnosis.name}
                  />
                ))}
              </AppFormSelect>

              {selectedDiagnosis && (
                <AppFormSelect
                  name="diagnosisSubType"
                  placeholder="please share a little more."
                  label="please share a little more."
                >
                  {(selectedDiagnosis?.subTypes || []).map(
                    (subStage: any, idx: any) => {
                      return (
                        <Option key={idx} label={subStage} value={subStage} />
                      );
                    }
                  )}
                </AppFormSelect>
              )}

              {!additionalApplier && (
                <AppFormSelect
                  name="personalizedHelp"
                  placeholder="Select"
                  label="while we do not currently have a customized view for this disease, we would love to get feedback when we do.  Would patient like to help build it?"
                >
                  {(healthProfileOptions.data?.personalizedHelp || []).map(
                    (option, idx) => (
                      <Option key={idx} label={option} value={option} />
                    )
                  )}
                </AppFormSelect>
              )}

              {additionalApplier && (
                <AppFormSelect
                  name="additionalApplier"
                  placeholder="Select"
                  label="do any of these apply to patient?"
                  multiple
                >
                  {additionalApplierValues.map((value, idx) => (
                    <Option key={idx} label={value} value={value} />
                  ))}
                </AppFormSelect>
              )}

              {diagnosisStageKey && (
                <AppFormSelect
                  name="diagnosisStage"
                  placeholder="Select"
                  label="cancer stage?"
                >
                  {(
                    healthProfileOptions.data?.diagnosisStageTypes[
                      diagnosisStageKey
                    ] || []
                  ).map((stage, idx) => (
                    <Option key={idx} label={stage} value={stage} />
                  ))}
                </AppFormSelect>
              )}

              <AppFormSelect
                name="diagnosisDate"
                placeholder="Select"
                label="what year was patient diagnosed?"
              >
                {(healthProfileOptions.data?.diagnosisDates || []).map(
                  (year, idx) => (
                    <Option key={idx} label={year} value={year} />
                  )
                )}
              </AppFormSelect>
            </View>
          </FormProvider>
        </AppScrollView>
        <View className="flex flex-row">
          <AppButton
            title="continue"
            className="mb-4"
            onPress={onSubmit}
            isLoading={cc.isLoading}
          />
        </View>
      </Screen>
      {/* Modal for Error*/}
      <InfoModal
        visible={errorVisible}
        showLogo
        label={error}
        handleSecondaryAction={() => setErrorVisible(false)}
        handleAction={() => setErrorVisible(false)}
      />
    </View>
  );
}
