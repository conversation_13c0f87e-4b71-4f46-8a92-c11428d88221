import LogoOutline from "@assets/svg/LogoOutline";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import Screen from "@components/common/Screen";
import AppFormSelect from "@components/form/AppFormSelect";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import { selectSchema } from "@utils/index";
import usePersistedUser from "@hooks/persistUser";
import { Option } from "@components/common/AppSelect";
import { z } from "zod";
import { zString } from "@utils/schema";
import { zodResolver } from "@hookform/resolvers/zod";

const schema = z.object({
  profile: zString,
});

type FormData = z.infer<typeof schema>;

export default function Caregiver() {
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();
  const { persistedUser } = usePersistedUser();
  const { data: user } = persistedUser;

  const { params } =
    useRoute<RouteProp<ProfileNavParams, "HealthProfileScreen">>();

  const methods = useForm<FormData>({
    defaultValues: {},
    resolver: zodResolver(schema),
    mode: "all",
  });

  const { handleSubmit } = methods;

  const onSubmit: SubmitHandler<FormData> = async ({ profile }) => {
    if (profile === "Find the patient") {
      navigation.navigate("FindCaregiverPatient");
    } else if (profile === "Skip for now") { 
      navigation.navigate("ProfileScreen");
    } else {
      navigation.navigate("CreateCaregiverPatient");
    }
  };

  return (
    <Screen className="mx-4">
      <AppScrollView>
        <LogoOutline className="self-center mt-20" />

        <AppText className="my-8 text-primary font-montserratSemiBold text-2xl">
          whom you are creating this profile for
        </AppText>

        {/* Form */}
        <FormProvider {...methods}>
          <AppFormSelect
            name="profile"
            placeholder="select profile"
            label="select profile"
          >
            <Option
              key={"find_patient"}
              label={"Find the patient"}
              value={"Find the patient"}
            />
            <Option
              key={"new_patient"}
              label={"Create a new patient profile"}
              value={"Create a new patient profile"}
            />
             <Option
              key={"skip"}
              label={"Skip for now"}
              value={"Skip for now"}
            />
          </AppFormSelect>
        </FormProvider>
      </AppScrollView>
      <View className="flex flex-row">
        <AppButton
          title="continue"
          className="mb-4"
          onPress={handleSubmit(onSubmit)}
        />
      </View>
    </Screen>
  );
}
