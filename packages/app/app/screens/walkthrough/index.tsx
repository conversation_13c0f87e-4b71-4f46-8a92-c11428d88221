import W1 from "@assets/svg/W1";
import W2 from "@assets/svg/W2";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import Screen from "@components/common/Screen";
import AnimatedLogo from "@components/walkthrough/AnimatedLogo";
import Walkthrough from "@components/walkthrough/Walkthrough";
import { RootNavigationProp } from "@navigation/root-navigator";
import { useNavigation } from "@react-navigation/native";
import { ChevronLeft } from "lucide-react-native";
import { useState } from "react";
import { Dimensions, Image, View } from "react-native";
import Animated, {
  Extrapolate,
  SharedValue,
  interpolate,
  useAnimatedRef,
  useAnimatedStyle,
  useScrollViewOffset,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

const { width } = Dimensions.get("screen");

export default function WalkThroughScreen() {
  const [currentPageIndex, setCurrentPageIndex] = useState(0);

  const navigation = useNavigation<RootNavigationProp>();

  const opacity = useSharedValue(0);
  const animatedWalkthroughContainerStyles = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const animatedScrollRef = useAnimatedRef<Animated.ScrollView>();
  const scrollX = useScrollViewOffset(animatedScrollRef);

  const scrollNextPage = () => {
    const currentPage = scrollX.value / width;
    const nextPage = currentPage + 1;
    animatedScrollRef.current?.scrollTo({
      x: nextPage * width,
      animated: true,
    });

    return currentPage;
  };

  const handleNavigationToLoginSplashScreen = () =>
    navigation.navigate("LoginSplashScreen");

  const handleBackScreen = () => {
    const currentPage = scrollX.value / width;
    const previousPage = Math.max(currentPage - 1, 0);
    animatedScrollRef.current?.scrollTo({
      x: previousPage * width,
      animated: true,
    });
  };

  const handleNextScreen = () => {
    const currentPage = scrollNextPage();
    if (currentPage >= 1.6) handleNavigationToLoginSplashScreen();
  };

  const currentIndex = () => {
    const currentPage = Math.round(scrollX.value / width);
    setCurrentPageIndex(currentPage);
  };

  return (
    <Screen className="flex-1">
      <ChevronLeft
        color={"#004987"}
        size={22}
        // @ts-expect-error
        className="ml-4"
        onPress={() => navigation.goBack()}
      />
      <View className="h-[160px] justify-center">
        <AnimatedLogo
          onAnimateEnd={() => {
            opacity.value = withTiming(1);
          }}
        />
      </View>

      <Animated.ScrollView
        overScrollMode="never"
        ref={animatedScrollRef}
        horizontal
        pagingEnabled
        style={[{}, animatedWalkthroughContainerStyles]}
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        onMomentumScrollEnd={currentIndex}
      >
        <Walkthrough
          contentClassName="mt-9"
          Svg={
            <W1
              className="self-center min-h-[300px] mt-8"
              style={{ transform: [{ scale: 0.68 }] }}
            />
          }
          title="welcome to myRabble"
          subTitle="we're here to help you and your loved ones navigate illness"
        />
        <Walkthrough
          Svg={<W2 className="self-center min-h-[300px] mt-16" />}
          title="connect with others"
          subTitle="build your circle of support, invite others"
        />
        <Walkthrough
          Image={
            <Image
              className="self-center"
              source={require("../../assets/img/W3.png")}
            />
          }
          title="access services that help"
          subTitle="in the moment, at the right time for you"
        />

        {/* <Walkthrough
          Svg={<W3 className="self-center min-h-[300px] mt-16" />}
          title="access services that help"
          subTitle="in the moment, at the right time for you"
        /> */}
      </Animated.ScrollView>

      <View className="flex flex-row items-center justify-between mx-4 mb-6">
        <Pressable
          actionTag="walkthrough button"
          className="flex flex-1"
          // onPress={handleNavigationToLoginSplashScreen}
          onPress={handleBackScreen}
          hitSlop={10}
        >
          {currentPageIndex !== 0 && (
            <AppText className="text-base text-[#004987] font-montserratMedium">
              back
            </AppText>
          )}
        </Pressable>
        <View className="flex flex-row items-center justify-center flex-1">
          {new Array(3).fill("").map((_, idx) => {
            return <CarouselDot key={idx} scrollX={scrollX} index={idx} />;
          })}
        </View>
        <Pressable
          actionTag="walkthrough next"
          className="flex flex-1"
          hitSlop={10}
          onPress={handleNextScreen}
        >
          <AppText className="text-base text-[#004987] font-montserratMedium self-end">
            next
          </AppText>
        </Pressable>
      </View>
    </Screen>
  );
}

interface CarouselDotProps {
  scrollX: SharedValue<number>;
  index: number;
}

export function CarouselDot({ scrollX, index }: CarouselDotProps) {
  const styles = useAnimatedStyle(() => {
    return {
      width: interpolate(
        scrollX.value,
        [(index - 1) * width, index * width, (index + 1) * width],
        [8, 28, 8],
        Extrapolate.CLAMP
      ),
      opacity: interpolate(
        scrollX.value,
        [(index - 1) * width, index * width, (index + 1) * width],
        [0.4, 1, 0.4],
        Extrapolate.CLAMP
      ),
    };
  });

  return (
    <Animated.View
      style={styles}
      className="w-2 h-2 bg-[#004987] rounded-full mx-1"
    />
  );
}
