import { Dimensions, Platform, Text, View } from "react-native";
import { Image } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import { DefaultIcon } from "./PostOrGroupPostDetailsScreen"; // assuming you've exported it from the header
import clsx from "clsx";
import _ from "lodash";

const { width } = Dimensions.get("screen");

interface ChatMessageProps {
  avatar?: string;
  name: string;
  message: string;
  time: string;
  isOwn?: boolean;
  reactions?: { emoji: string; count: number }[];
  isFromContextMenu?: boolean;
  repliedTo?: any;
  comments?: any;
}

export default function ChatMessage({
  avatar,
  name,
  message,
  time,
  isOwn = false,
  reactions = [],
  isFromContextMenu = false,
  repliedTo,
  comments,
}: ChatMessageProps) {
  return (
    <View
      className={clsx(
        "flex-row mb-2",
        !isOwn ? "justify-end mr-[48px]" : "ml-[48px] justify-start",
        isFromContextMenu && "ml-16 justify-center"
      )}
    >
      <View className={clsx("flex-row items-end mb-1 mr-2")}>
        {avatar ? (
          <Image
            uri={imageKit({ imagePath: avatar, transform: ["w-100"] })}
            style={{ width: 40, height: 41, borderRadius: 5 }}
          />
        ) : (
          <View className="w-[40px] h-[41px] rounded-full">
            <DefaultIcon width={40} height={41} />
          </View>
        )}
      </View>

      <View
        className={clsx(
          "rounded-xl px-4 py-2 bg-[#F5F7F9]",
          isFromContextMenu && "bg-white"
        )}
        style={{
          width: isFromContextMenu ? width * 0.75 : width * 0.7,
          boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
          elevation: 4,
          shadowColor: Platform.OS === "ios" ? "rgba(0, 0, 0, 0.25)" : "#000",
          shadowOffset: { width: 0, height: Platform.OS === "ios" ? 4 : 8 },
          shadowOpacity: Platform.OS === "ios" ? 0.25 : 6,
          shadowRadius: Platform.OS === "ios" ? 4 : 6
        }}
      >
        {repliedTo && (
          <View className="pb-2 bg-gray-200">
            <Text className="font-montserratMedium ml-2 mt-1">
              {_.find(comments, { _id: repliedTo })?.comment}
            </Text>
          </View>
        )}
        <Text className="text-[#0B79D3] font-[montserrat] text-[14px] font-[700] leading-[1.3] mb-1">
          {name}
        </Text>
        <Text className="text-[#336D9F] font-[montserrat] text-[14px] font-[400] leading-[1.3]">
          {message}
        </Text>

        <View className="flex-row flex-end justify-between items-center mt-1">
          <Text className="text-[#0B79D3] text-right font-[Montserrat] text-[8px] font-[600] leading-[130%] mt-1 ml-auto">
            {time}
          </Text>
        </View>

        {reactions.length > 0 && reactions[0]?.emoji && (
          <View
            style={{
              backgroundColor: "#fff",
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.25,
              shadowRadius: 4,
              elevation: 4,
              zIndex: 9999,
            }}
            className="flex-row py-1 ml-2 bg-[#ffffff] border rounded-full border-[#B4DDFF] items-center absolute bottom-[-20] left-0 text-[#0B79D3] text-center font-[Montserrat] text-[14px] font-medium leading-none"
          >
            {reactions.map((reaction, idx) =>
              reaction.emoji || reaction.count ? (
                <View key={idx} className="flex-row px-2 py-0.5 ml-1">
                  <Text className="text-[12px] mr-1">{reaction.emoji}</Text>
                  <Text className="text-[12px] text-[#0B79D3] font-[500]">
                    {reaction.count}
                  </Text>
                </View>
              ) : null
            )}
          </View>
        )}
      </View>
    </View>
  );
}
