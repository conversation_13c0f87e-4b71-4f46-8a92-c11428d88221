import GlobeIcon from '@assets/svg/GlobeIcon';
import LockIcon from '@assets/svg/LockIcon';
import AppText from '@components/common/AppText';
import LoadingScreen from '@components/common/LoadingScreen';
import AppFormInput from '@components/form/AppFormInput';
import { yupResolver } from '@hookform/resolvers/yup';
import useDebounceValue from '@hooks/useDebounceValue';
import { RabbleGroup } from '@models/index';
import { trpc } from '@providers/RootProvider';
import { useNavigation } from '@react-navigation/native';
import errorHandler from '@utils/errorhandler';
import { imageKit } from '@utils/index';
import { useCallback } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FlatList, Image, View } from 'react-native';
import * as yup from 'yup';
import { RouterOutput } from '../../../../shared';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavParams } from '@navigation/root-navigator/RootNavParams';
import Screen from '@components/common/Screen';
import Pressable from '@components/common/Pressable';

const schema = yup.object({ searchTerm: yup.string().required() });
type FormData = yup.InferType<typeof schema>;

const SearchResult = ({
  groupDescription,
  _id,
  groupName,
  privacy,
  image,
  onPress,
}: RouterOutput['rabbleGroups']['searchRabbleGroup'][number] & {
  onPress: (group: string) => void;
}) => {
  return (
    <Pressable
      actionTag='rabble search'
      onPress={() => onPress?.(_id.toString())}
      className='flex flex-row items-center gap-4 my-2'
    >
      <Image
        className='w-12 h-12 rounded-full'
        source={{
          uri: imageKit({ imagePath: image as string, transform: ['w-100'] }),
        }}
      />

      <View className='flex-1'>
        <View className='flex flex-row items-center gap-3 flex-1'>
          <AppText numberOfLines={1} className='flex-1'>
            {groupName}
          </AppText>
          <View className='flex flex-row items-center'>
            {privacy === 'public' ? (
              <GlobeIcon className='scale-50' />
            ) : (
              <LockIcon className='scale-50' />
            )}
            <AppText className='capitalize'>{privacy}</AppText>
          </View>
        </View>
        <AppText numberOfLines={1}>{groupDescription}</AppText>
      </View>
    </Pressable>
  );
};

const SearchRabblesScreen = () => {
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const methods = useForm<FormData>({ resolver: yupResolver(schema) });
  const { handleSubmit, watch } = methods;
  const debouncedSearchTerm = useDebounceValue(watch('searchTerm'), 500);

  const searchRabbles = trpc.rabbleGroups.searchRabbleGroup.useQuery({
    name: debouncedSearchTerm || '',
  });

  const handleGroup = (groupId: string) => {
    navigation.navigate('RabbleDetailsScreen', { groupId });
  };

  // const { isLoading, data: searchResults } = useSearchRabbleGroup({
  //   searchTerm: debouncedSearchTerm,
  // });

  return (
    <Screen className='mx-4'>
      <View className='flex-row'>
        <FormProvider {...methods}>
          <AppFormInput
            name='searchTerm'
            placeholder='Search for groups'
            autoFocus
          />
        </FormProvider>
      </View>

      {/* Search results */}
      {searchRabbles.isLoading ? (
        <LoadingScreen />
      ) : (
        <FlatList
          data={searchRabbles?.data}
          keyExtractor={({ _id }) => _id.toString()}
          renderItem={({ item }) => (
            <SearchResult {...item} onPress={handleGroup} />
          )}
        />
      )}
    </Screen>
  );
};

export default SearchRabblesScreen;
