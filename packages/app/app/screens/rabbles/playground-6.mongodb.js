/* global use, db */
// MongoDB Playground
// Use Ctrl+Space inside a snippet or a string literal to trigger completions.

// The current database to use.
use('rabble-connect-transition--dev');

// Search for documents in the current collection.
db.getCollection('rabble-group-users')
  .find(
    {
      /*
      * Filter
      * fieldA: value or expression
      */
    },
    {
      /*
      * Projection
      * _id: 0, // exclude _id
      * fieldA: 1 // include field
      */
    }
  )
  .sort({
    /*
    * fieldA: 1 // ascending
    * fieldB: -1 // descending
    */
  });
/* global use, db */
// MongoDB Playground
// Use Ctrl+Space inside a snippet or a string literal to trigger completions.

// The current database to use.
use('rabblehealth--prod');

const jsonArray = [
  {
    "email": "",
    "mobile_phone": "**********",
    "created_at": "2023-09-13 2:48:30",
    "updated_at": "2023-09-13 2:48:30"
  },
  {
    "email": "<EMAIL>",
    "mobile_phone": "**********",
    "created_at": "2023-08-28 13:45:08",
    "updated_at": "2023-10-09 17:01:47"
  },
  {
    "email": "",
    "mobile_phone": "**********",
    "created_at": "2023-09-14 19:58:49",
    "updated_at": "2023-09-14 19:58:49"
  },
  {
    "email": "",
    "mobile_phone": "**********",
    "created_at": "2023-09-07 23:05:35",
    "updated_at": "2023-09-07 23:05:35"
  },
  {
    "email": "",
    "mobile_phone": "**********",
    "created_at": "2023-09-16 16:43:31",
    "updated_at": "2023-09-16 16:43:31"
  },
  {
    "email": "",
    "mobile_phone": "**********",
    "created_at": "2023-09-16 16:43:40",
    "updated_at": "2023-09-16 16:43:40"
  },
  {
    "email": "",
    "mobile_phone": "**********",
    "created_at": "2023-09-16 16:46:58",
    "updated_at": "2023-09-16 16:46:58"
  },
  {
    "email": "",
    "mobile_phone": "9015209547",
    "created_at": "2023-09-16 16:47:39",
    "updated_at": "2023-09-16 16:47:39"
  },
  {
    "email": "",
    "mobile_phone": "9018311966",
    "created_at": "2023-09-16 16:47:57",
    "updated_at": "2023-09-16 16:47:57"
  },
  {
    "email": "",
    "mobile_phone": "9014900557",
    "created_at": "2023-09-16 17:03:32",
    "updated_at": "2023-09-16 17:03:32"
  },
  {
    "email": "",
    "mobile_phone": "9014065865",
    "created_at": "2023-09-16 17:18:17",
    "updated_at": "2023-09-16 17:18:17"
  },
  {
    "email": "",
    "mobile_phone": "4848897341",
    "created_at": "2023-09-19 16:02:04",
    "updated_at": "2023-09-19 16:02:04"
  },
  {
    "email": "",
    "mobile_phone": "8184168625",
    "created_at": "2023-09-27 21:36:23",
    "updated_at": "2023-09-27 21:36:23"
  },
  {
    "email": "",
    "mobile_phone": "3238237636",
    "created_at": "2023-09-22 19:39:36",
    "updated_at": "2023-09-22 19:39:36"
  },
  {
    "email": "",
    "mobile_phone": "7652992528",
    "created_at": "2023-09-29 7:52:10",
    "updated_at": "2023-09-29 7:52:10"
  },
  {
    "email": "",
    "mobile_phone": "9012688483",
    "created_at": "2023-09-07 2:11:03",
    "updated_at": "2023-09-07 2:11:03"
  },
  {
    "email": "",
    "mobile_phone": "6127109070",
    "created_at": "2023-09-05 13:59:27",
    "updated_at": "2023-09-05 13:59:27"
  },
  {
    "email": "",
    "mobile_phone": "7039814147",
    "created_at": "2023-09-09 17:52:57",
    "updated_at": "2023-09-09 17:52:57"
  },
  {
    "email": "",
    "mobile_phone": "9012684616",
    "created_at": "2023-09-10 20:47:34",
    "updated_at": "2023-09-10 20:47:34"
  },
  {
    "email": "",
    "mobile_phone": "9014896013",
    "created_at": "2023-10-28 18:09:23",
    "updated_at": "2023-10-28 18:09:23"
  },
  {
    "email": "",
    "mobile_phone": "9014849624",
    "created_at": "2023-10-28 18:10:04",
    "updated_at": "2023-10-28 18:10:04"
  },
  {
    "email": "",
    "mobile_phone": "9016209088",
    "created_at": "2023-10-17 23:21:43",
    "updated_at": "2023-10-17 23:21:43"
  },
  {
    "email": "",
    "mobile_phone": "9016790509",
    "created_at": "2023-10-21 22:20:37",
    "updated_at": "2023-10-21 22:20:37"
  },
  {
    "email": "<EMAIL>",
    "mobile_phone": "",
    "created_at": "2023-10-01 13:57:01",
    "updated_at": "2023-10-01 13:57:01"
  },
  {
    "email": "",
    "mobile_phone": "8052317743",
    "created_at": "2023-10-03 16:52:35",
    "updated_at": "2023-10-03 16:52:35"
  },
  {
    "email": "",
    "mobile_phone": "9312398964",
    "created_at": "2023-10-03 18:53:55",
    "updated_at": "2023-10-03 18:53:55"
  },
  {
    "email": "",
    "mobile_phone": "9184070264",
    "created_at": "2023-10-04 18:48:13",
    "updated_at": "2023-10-04 18:48:13"
  },
  {
    "email": "<EMAIL>",
    "mobile_phone": "",
    "created_at": "2023-10-04 20:48:36",
    "updated_at": "2023-10-04 20:48:36"
  },
  {
    "email": "",
    "mobile_phone": "8507603824",
    "created_at": "2023-10-05 3:43:31",
    "updated_at": "2023-10-05 3:43:31"
  },
  {
    "email": "",
    "mobile_phone": "5135708035",
    "created_at": "2023-10-06 3:26:52",
    "updated_at": "2023-10-06 3:26:52"
  },
  {
    "email": "",
    "mobile_phone": "2794992386",
    "created_at": "2023-10-23 5:35:33",
    "updated_at": "2023-10-23 5:35:33"
  },
  {
    "email": "<EMAIL>",
    "mobile_phone": "",
    "created_at": "2023-10-23 17:04:44",
    "updated_at": "2023-10-23 17:04:44"
  },
  {
    "email": "",
    "mobile_phone": "6059719337",
    "created_at": "2023-10-25 6:53:13",
    "updated_at": "2023-10-25 6:53:13"
  },
  {
    "email": "",
    "mobile_phone": "6059714423",
    "created_at": "2023-10-25 7:16:01",
    "updated_at": "2023-10-25 7:16:01"
  },
  {
    "email": "",
    "mobile_phone": "2252542523",
    "created_at": "2023-10-25 8:19:47",
    "updated_at": "2023-10-25 8:19:47"
  },
  {
    "email": "",
    "mobile_phone": "9494322808",
    "created_at": "2023-10-11 19:39:52",
    "updated_at": "2023-10-11 19:39:52"
  },
  {
    "email": "",
    "mobile_phone": "5623746199",
    "created_at": "2023-10-11 20:09:17",
    "updated_at": "2023-10-11 20:09:17"
  },
  {
    "email": "",
    "mobile_phone": "6266769782",
    "created_at": "2023-10-12 5:21:23",
    "updated_at": "2023-10-12 5:21:23"
  },
  {
    "email": "<EMAIL>",
    "mobile_phone": "",
    "created_at": "2023-09-28 4:44:47",
    "updated_at": "2023-09-30 5:43:09"
  },
  {
    "email": "",
    "mobile_phone": "9018109597",
    "created_at": "2023-10-28 16:01:19",
    "updated_at": "2023-10-28 16:01:19"
  },
  {
    "email": "",
    "mobile_phone": "9014950722",
    "created_at": "2023-10-28 16:03:24",
    "updated_at": "2023-10-28 16:03:24"
  },
  {
    "email": "",
    "mobile_phone": "9013045627",
    "created_at": "2023-10-28 16:04:04",
    "updated_at": "2023-10-28 16:04:04"
  },
  {
    "email": "",
    "mobile_phone": "9014941100",
    "created_at": "2023-10-28 14:48:55",
    "updated_at": "2023-10-28 14:48:55"
  },
  {
    "email": "",
    "mobile_phone": "9012977022",
    "created_at": "2023-10-28 14:55:16",
    "updated_at": "2023-10-28 14:55:16"
  },
  {
    "email": "",
    "mobile_phone": "9016031621",
    "created_at": "2023-10-28 15:58:46",
    "updated_at": "2023-10-28 15:58:46"
  },
  {
    "email": "",
    "mobile_phone": "8054906752",
    "created_at": "2023-09-04 5:44:05",
    "updated_at": "2023-09-30 5:53:40"
  },
  {
    "email": "",
    "mobile_phone": "2705597885",
    "created_at": "2023-10-28 17:59:31",
    "updated_at": "2023-10-28 17:59:31"
  },
  {
    "email": "",
    "mobile_phone": "9016498973",
    "created_at": "2023-10-28 18:07:38",
    "updated_at": "2023-10-28 18:07:38"
  },
  {
    "email": "",
    "mobile_phone": "6785482180",
    "created_at": "2023-10-28 18:08:14",
    "updated_at": "2023-10-28 18:08:14"
  },
  {
    "email": "",
    "mobile_phone": "-3022814650",
    "created_at": "2023-10-26 14:28:43",
    "updated_at": "2023-10-26 14:28:43"
  },
  {
    "email": "",
    "mobile_phone": "-2673800457",
    "created_at": "2023-10-27 10:40:27",
    "updated_at": "2023-10-27 10:40:27"
  },
  {
    "email": "",
    "mobile_phone": "-4086343220",
    "created_at": "2023-10-18 6:49:17",
    "updated_at": "2023-10-18 6:49:17"
  },
  {
    "email": "",
    "mobile_phone": "9013833907",
    "created_at": "2023-10-28 18:10:28",
    "updated_at": "2023-10-28 18:10:28"
  },
  {
    "email": "",
    "mobile_phone": "9016284569",
    "created_at": "2023-10-28 18:12:41",
    "updated_at": "2023-10-28 18:12:41"
  },
  {
    "email": "",
    "mobile_phone": "9015204113",
    "created_at": "2023-10-28 18:13:27",
    "updated_at": "2023-10-28 18:13:27"
  },
  {
    "email": "",
    "mobile_phone": "7404630034",
    "created_at": "2023-12-06 15:00:52",
    "updated_at": "2023-12-06 15:00:52"
  },
  {
    "email": "",
    "mobile_phone": "8052095284",
    "created_at": "2023-10-26 0:24:25",
    "updated_at": "2023-10-26 0:26:15"
  },
  {
    "email": "",
    "mobile_phone": "9016126470",
    "created_at": "2023-10-28 18:18:50",
    "updated_at": "2023-10-28 18:18:50"
  },
  {
    "email": "",
    "mobile_phone": "8058079293",
    "created_at": "2023-11-17 20:18:32",
    "updated_at": "2023-11-17 20:18:32"
  },
  {
    "email": "<EMAIL>",
    "mobile_phone": "",
    "created_at": "2023-10-29 1:16:34",
    "updated_at": "2023-10-29 1:16:34"
  },
  {
    "email": "",
    "mobile_phone": "3045502022",
    "created_at": "2023-11-17 20:28:31",
    "updated_at": "2023-11-17 20:28:31"
  },
  {
    "email": "",
    "mobile_phone": "9794293446",
    "created_at": "2023-12-06 15:05:57",
    "updated_at": "2023-12-06 15:05:57"
  },
  {
    "email": "",
    "mobile_phone": "2812536740",
    "created_at": "2023-11-22 17:52:05",
    "updated_at": "2023-11-22 17:52:05"
  },
  {
    "email": "",
    "mobile_phone": "4128601389",
    "created_at": "2023-12-13 18:27:17",
    "updated_at": "2023-12-13 18:27:17"
  },
  {
    "email": "",
    "mobile_phone": "7144258023",
    "created_at": "2023-11-08 4:22:24",
    "updated_at": "2023-11-08 4:22:24"
  },
  {
    "email": "",
    "mobile_phone": "-6195376271",
    "created_at": "2023-10-31 13:36:20",
    "updated_at": "2023-10-31 13:40:21"
  },
  {
    "email": "",
    "mobile_phone": "3109184144",
    "created_at": "2023-11-03 14:26:50",
    "updated_at": "2023-11-03 14:26:50"
  },
  {
    "email": "",
    "mobile_phone": "6012918778",
    "created_at": "2023-09-07 18:30:28",
    "updated_at": "2023-09-07 18:34:57"
  },
  {
    "email": "",
    "mobile_phone": "9015033781",
    "created_at": "2023-11-29 19:33:23",
    "updated_at": "2023-11-29 19:33:23"
  },
  {
    "email": "",
    "mobile_phone": "9012682479",
    "created_at": "2023-11-30 5:14:54",
    "updated_at": "2023-11-30 5:14:54"
  },
  {
    "email": "",
    "mobile_phone": "6142717220",
    "created_at": "2023-11-30 8:28:34",
    "updated_at": "2023-11-30 8:28:34"
  },
  {
    "email": "",
    "mobile_phone": "9013596629",
    "created_at": "2023-11-30 20:08:57",
    "updated_at": "2023-11-30 20:08:57"
  },
  {
    "email": "",
    "mobile_phone": "6622310929",
    "created_at": "2023-09-07 20:00:43",
    "updated_at": "2023-09-07 20:02:47"
  },
  {
    "email": "",
    "mobile_phone": "9014849119",
    "created_at": "2023-09-07 18:04:47",
    "updated_at": "2023-09-07 18:11:50"
  },
  {
    "email": "",
    "mobile_phone": "9012812710",
    "created_at": "2023-10-28 16:12:57",
    "updated_at": "2023-10-28 16:43:02"
  },
  {
    "email": "<EMAIL>",
    "mobile_phone": "",
    "created_at": "2023-11-08 18:50:22",
    "updated_at": "2023-11-08 18:50:22"
  },
  {
    "email": "",
    "mobile_phone": "4042421406",
    "created_at": "2023-11-10 18:40:21",
    "updated_at": "2023-11-10 18:40:21"
  },
  {
    "email": "",
    "mobile_phone": "9018595557",
    "created_at": "2023-10-28 18:08:41",
    "updated_at": "2023-10-28 18:18:28"
  },
  {
    "email": "",
    "mobile_phone": "9014973593",
    "created_at": "2023-10-28 18:21:37",
    "updated_at": "2023-10-28 18:24:50"
  },
  {
    "email": "",
    "mobile_phone": "4133248551",
    "created_at": "2023-12-06 17:59:41",
    "updated_at": "2023-12-06 17:59:41"
  },
  {
    "email": "",
    "mobile_phone": "2485795654",
    "created_at": "2023-12-06 18:01:54",
    "updated_at": "2023-12-06 18:01:54"
  },
  {
    "email": "",
    "mobile_phone": "9018310965",
    "created_at": "2023-10-28 16:54:19",
    "updated_at": "2023-12-03 15:08:59"
  },
  {
    "email": "<EMAIL>",
    "mobile_phone": "",
    "created_at": "2023-12-13 19:37:17",
    "updated_at": "2023-12-13 19:37:17"
  },
  {
    "email": "<EMAIL>",
    "mobile_phone": "",
    "created_at": "2023-12-21 4:13:50",
    "updated_at": "2023-12-21 4:13:50"
  },
  {
    "email": "",
    "mobile_phone": "9018482800",
    "created_at": "2023-12-21 14:54:48",
    "updated_at": "2023-12-21 14:54:48"
  },
  {
    "email": "",
    "mobile_phone": "2533467725",
    "created_at": "2024-01-01 9:23:19",
    "updated_at": "2024-01-01 9:23:19"
  }
]

var bulkOps = jsonArray.map(function(doc) {
  var phoneOrEmail = doc.mobile_phone !== "" ? doc.mobile_phone : doc.email;
  var createdAt = new Date(doc.created_at);
  var updatedAt = new Date(doc.updated_at);

  const filter = doc.mobile_phone ? { 'contact.phone': doc.mobile_phone } : { email: doc.email };

  var update = {
      $set: {
          createdAt: createdAt,
          updatedAt: updatedAt
      }
  };

  return {
      updateOne: {
          filter: filter,
          update: update,
          upsert: false
      }
  };
});

// Execute bulk write operation
db.getCollection('user-accounts').bulkWrite(bulkOps);


db.getCollection('rabble-user-groups').find({ rabbleGroup: ObjectId('65f117fae347c30c30337fbe') })