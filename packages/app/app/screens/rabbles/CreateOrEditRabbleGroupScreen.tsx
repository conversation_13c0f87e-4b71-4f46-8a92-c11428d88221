import CameraIcon from "@assets/svg/CameraIcon";
import GlobeIcon from "@assets/svg/GlobeIcon";
import LockIcon from "@assets/svg/LockIcon";
import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import AppFormInput from "@components/form/AppFormInput";
import { s3Paths } from "@constants/index";
import { zodResolver } from "@hookform/resolvers/zod";
import useImagePicker from "@hooks/useImagePicker";
import { trpc } from "@providers/RootProvider";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { useUploadImage } from "@services/upload";
import errorHandler from "@utils/errorhandler";
import clsx from "clsx";
import React, { useState, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { Image, View } from "react-native";
import { createRabbleGroupValidator } from "../../../../shared/validators/rabblegroup.validator";
import { z } from "zod";
import AppScrollView from "@components/common/AppScrollView";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { imageKit } from "@utils/index";
import { StackNavigationProp } from "@react-navigation/stack";
import InfoModal from "@components/common/InfoModal";
import Pressable from "@components/common/Pressable";
import AppFormTagsAutocomplete from "@components/form/AppFormTagsAutocomplete";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";

type Form = z.infer<typeof createRabbleGroupValidator>;

const Radio = ({
  icon,
  text,
  active,
  onClick,
}: {
  active?: boolean;
  text: string;
  icon: JSX.Element;
  onClick?: (text: string) => void;
}) => {
  return (
    <Pressable
      actionTag={`radio-${text}`}
      className="flex  flex-row justify-between items-center p-2"
      onPress={() => onClick?.(text)}
    >
      <View className="flex flex-row items-center gap-3">
        {icon}
        <AppText>{text}</AppText>
      </View>
      <View
        className={clsx(
          "border-4 border-primary w-4 h-4 rounded-full",
          active ? "bg-primary/50 border-primary-dark" : "bg-white"
        )}
      />
    </Pressable>
  );
};
const CreateOrEditRabbleGroupScreen = () => {
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const route =
    useRoute<RouteProp<RootNavParams, "CreateOrEditRabbleGroupScreen">>();
  const { rabbleGroupInfo, groupId } = route?.params || {};

  const methods = useForm<Form>({
    resolver: zodResolver(createRabbleGroupValidator),
    defaultValues: {
      privacy: "public",
      ...rabbleGroupInfo,
    },
  });
  const { images, imagePicker } = useImagePicker();
  const [logo, setLogo] = useState("");
  const [modalVisible, setmodalVisible] = useState(false);
  const { user } = useSession();

  useEffect(() => {
    if (rabbleGroupInfo?.tags) {
      methods.setValue("tags", rabbleGroupInfo?.tags);
    }
  }, [rabbleGroupInfo?.tags]);

  const handleSelectImage = async () => {
    const assets = await imagePicker({
      allowsEditing: true,
      quality: 0.4,
      aspect: [1, 1],
    });

    if (assets?.length) {
      const selectedImage = assets[0].uri;
      setLogo(selectedImage);
    }
  };

  const { mutateAsync: createRabbleGroup, isLoading: creatingRabbleGroup } =
    trpc.rabbleGroups.createRabbleGroup.useMutation();

  const {
    mutateAsync: updateRabbleGroupPatials,
    isLoading: updatingUserPartials,
  } = trpc.rabbleGroups.updateRabbleGroupPatials.useMutation();

  const uploadImage = useUploadImage();

  const uploadImageIfPresent = async (logo: string) => {
    if (logo) {
      const formdata = new FormData();
      formdata.append("filePath", s3Paths.post);

      formdata.append("image", {
        uri: logo,
        type: "image/png",
        name: "image.png",
      } as any);

      // upload image
      const {
        data: { s3Path },
      } = await uploadImage.mutateAsync({ formdata });
      return { image: s3Path };
    }
    return {};
  };

  const handleSubmit = methods.handleSubmit(
    async ({ groupDescription, groupName, privacy, tags }) => {
      try {
        if (!logo && !groupId) return alert("group logo is required");
        const imageData = await uploadImageIfPresent(logo);
        if (groupId) {
          await updateRabbleGroupPatials({
            id: groupId,
            groupName,
            groupDescription,
            privacy,
            tags: tags || [],
            ...imageData,
          });
          navigation.goBack();
        } else {
          await createRabbleGroup({
            groupDescription,
            groupName,
            privacy,
            tags: tags || [],
            ...imageData,
          });
          methods.reset();
          setmodalVisible(true);
          await mixpanel.trackEvent(
            "New rabbles group created(Rabble screen)",
            {
              email: user?.email || "",
              phone: user?.contact?.phone || "",
              group_name: groupName || "",
              group_desc: groupDescription || "",
              group_tags: tags?.toString() || "",
              status: "active",
            },
            String(user?._id),
            "v2"
          );
        }
      } catch (ex) {
        errorHandler(ex);
      }
    }
  );

  // const tags: string[] = ['tags1', 'tags2'];

  return (
    <View>
      <AppScrollView>
        <FormProvider {...methods}>
          <View className="flex flex-1 m-3" style={{ gap: 10 }}>
            <Pressable
              actionTag="select group image"
              onPress={handleSelectImage}
              className="relative bg-neutral-200 w-20 h-20 rounded-full self-center"
            >
              <CameraIcon className="absolute bottom-0 right-0 bg-white z-10" />
              {(images?.length || rabbleGroupInfo?.image) && (
                <Image
                  className="absolute left-0 right-0 top-0 bottom-0 rounded-full "
                  source={{
                    uri: images?.length
                      ? images.at(0)?.uri
                      : imageKit({
                          imagePath: rabbleGroupInfo?.image || "",
                          transform: ["w-500"],
                        }),
                  }}
                />
              )}
            </Pressable>
            <AppFormInput
              label="group name"
              placeholder="enter group name"
              name="groupName"
            />
            <AppFormInput
              label="group description"
              placeholder="enter group description"
              name="groupDescription"
              multiline
              className="h-32"
            />

            {/* Enhanced Tags Input with Autocomplete */}
            <AppFormTagsAutocomplete
              name="tags"
              label="Tags"
              placeholder="Type to search or create tags..."
              maxTags={10}
              className="mb-4"
            />

            {/* Privacy */}
            <View style={{ gap: 4 }}>
              <Radio
                icon={<GlobeIcon />}
                text="public"
                active={methods.watch("privacy") === "public"}
                onClick={(text) => {
                  methods.setValue("privacy", text as "public" | "private");
                }}
              />
              <Radio
                icon={<LockIcon />}
                text="private"
                active={methods.watch("privacy") === "private"}
                onClick={(text) => {
                  methods.setValue("privacy", text as "public" | "private");
                }}
              />
            </View>
          </View>

          <View className="flex flex-row mb-6 mx-3">
            <AppButton
              title="continue"
              onPress={handleSubmit}
              isLoading={
                creatingRabbleGroup ||
                uploadImage.isLoading ||
                updatingUserPartials
              }
              disabled={creatingRabbleGroup || uploadImage.isLoading}
            />
          </View>
        </FormProvider>
      </AppScrollView>
      {/* Modal for Group Creation*/}
      <InfoModal
        visible={modalVisible}
        showLogo
        label="Rabble Group created successfully"
        handleSecondaryAction={() => setmodalVisible(false)}
        handleAction={() => {
          setmodalVisible(false);
          navigation.goBack();
        }}
      />
    </View>
  );
};

export default CreateOrEditRabbleGroupScreen;
