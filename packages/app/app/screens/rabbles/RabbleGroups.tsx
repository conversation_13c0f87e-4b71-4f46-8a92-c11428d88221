import Logo from "@assets/svg/Logo";
import { useEffect } from "react";
import LogoFilled from "@assets/svg/LogoFilled";
import AppText from "@components/common/AppText";
import {
  FlatList,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { Filter, Globe2, Lock, PlusCircle, Search } from "lucide-react-native";
import { trpc } from "@providers/RootProvider";
import {
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import Tabs from "@components/common/Tabs";
import { useCallback, useMemo, useState } from "react";
import { RouterOutput } from "../../../../shared";
import { Image } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import useAuthGuard from "@hooks/useAuthGuard";
import { useSession } from "@hooks/persistUser";
import _ from "lodash";
import { z } from "zod";
import { getDiscoverGroupsValidator } from "../../../../shared/validators/rabblegroup.validator";
import { FormProvider, useForm } from "react-hook-form";
import DiseaseTagsFilter from "app/elements/rabbles/DiseaseTagsFilter";
import Pressable from "@components/common/Pressable";
import { RabbleGroupListItem } from "@components/rabbleGroups/RabbleGroupListItem";
import mixpanel from "@utils/mixpanel";
import * as Contacts from "expo-contacts";

import * as React from "react";
import Svg, { Circle, Path, SvgProps } from "react-native-svg";
import { PostOrGroupCard } from "@components/common/PostOrGroupCard";
import ConnectPosts from "@screens/connect/ConnectPosts";
import AiSearchInput from "@components/common/SearchInput";
import { PlaceholderLogo } from "@components/onboarding/CreateNewRabble";
import AppScrollView from "@components/common/AppScrollView";
import ManageHomeScreenPills from "@screens/manage/ManageHomeScreenPills";
import { ButterFlyIcon } from "@screens/manage/ManageHomeScreen";
import CreateRabblePopup from "@components/onboarding/CreateRabbleCard";
import { Alert } from "react-native";
import { format } from "date-fns";
import Screen from "@components/common/Screen";
import AsyncStorage from "@react-native-async-storage/async-storage";

const countryDialingCodes: Record<string, string> = {
  IN: "91",
  US: "1",
  UK: "44",
  CA: "1",
  AU: "61",
  // Add more as needed
};

function ContactIcon(props: SvgProps) {
  return (
    <Svg
      // xmlns="http://www.w3.org/2000/svg"
      width={40}
      height={40}
      viewBox="0 0 40 40"
      fill="none"
      {...props}
    >
      <Circle cx={19.9273} cy={19.9273} r={19.9273} fill="#fff" />
      <Path
        d="M27.379 18.87a3.576 3.576 0 003.074-3.533 3.575 3.575 0 00-2.992-3.527M29.668 23.037c1.68.251 2.852.84 2.852 2.052 0 .834-.552 1.376-1.445 1.717"
        stroke="#004987"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.917 23.553c-3.996 0-7.41.605-7.41 3.024 0 2.417 3.393 3.04 7.41 3.04 3.997 0 7.409-.6 7.409-3.02 0-2.419-3.391-3.044-7.409-3.044zM19.917 20.1a4.748 4.748 0 10-4.748-4.748 4.73 4.73 0 004.714 4.749h.034z"
        fill="#004987"
      />
      <Path
        d="M12.454 18.87a3.574 3.574 0 01-3.074-3.533 3.575 3.575 0 012.992-3.527M10.165 23.037c-1.68.251-2.852.84-2.852 2.052 0 .834.552 1.376 1.445 1.717"
        stroke="#004987"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

const PlusIcon = (props: SvgProps) => (
  <Svg
    // xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    viewBox="0 0 30 30"
    fill="none"
    {...props}
  >
    <Circle cx={15} cy={15} r={15} fill="#FF8E1C" />
    <Path
      d="M15.0001 6.66602V22.216M22.2167 14.9993H6.66675"
      stroke="white"
      strokeWidth={1.67}
      strokeLinecap="round"
    />
  </Svg>
);

const ThreeDots = (props: SvgProps) => (
  <Svg
    // xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    viewBox="0 0 30 30"
    fill="none"
    {...props}
  >
    <Circle cx={15} cy={15} r={15} fill="#DFE1E2" />
    <Circle cx={10.5} cy={15.5} r={1.5} fill="#0B79D3" />
    <Circle cx={15.5} cy={15.5} r={1.5} fill="#0B79D3" />
    <Circle cx={20.5} cy={15.5} r={1.5} fill="#0B79D3" />
  </Svg>
);

const schema = z.object({ diseaseTags: z.array(z.string()).optional() });

type Filter = z.infer<typeof schema>;
export default function RabbleGroups() {
  const [open, setOpen] = useState(false);
  const [isOpenRabbleCard, setRabbleCardOpen] = useState(false);

  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const { selectedTab = 0 } =
    useRoute<RouteProp<RootNavParams, "RabbleGroups">>().params ?? {};
  const [tab, setTab] = useState(selectedTab || 0);
  const [selectedTopic, setSelectedTopic] = useState("");
  const authGuard = useAuthGuard();
  const { user } = useSession();
  console.log({ selectedTab });

  const methods = useForm<Filter>({ defaultValues: { diseaseTags: [] } });
  const diseaseTags = methods.watch("diseaseTags");

  const createRabbleGroup = () =>
    authGuard(() => navigation.navigate("CreateOrEditRabbleGroupScreen"));

  const createdGroups = trpc.rabbleGroups.getUserCreatedGroups.useQuery();
  const joinedGroups = trpc.rabbleGroups.userJoinedGroups.useQuery();
  const activeUsers = trpc.user.getAllActiveUsers.useQuery();

  const discoverGroups = trpc.rabbleGroups.discoverRabbleGroups.useQuery({
    diseaseTags: diseaseTags?.filter(Boolean) || [],
  });
  const getUserById = trpc.user.getUserById.useMutation();

  const combineGroups = useMemo(() => {
    const groups = _.uniqBy(
      [
        ...(createdGroups.data || []).map((_) => ({ ..._, owner: true })),
        ...(joinedGroups.data || []).map((_) => ({
          ..._.rabbleGroup,
        })),
        // ...(patientGroups.data || []).map((_) => ({
        //   ..._.rabbleGroup,
        //   isCareGiverManagedGroup: true,
        // })),
      ],
      "_id"
    );

    return groups;
  }, [createdGroups.data, joinedGroups.data]);

  const createdGroupsUnique = _.uniqBy(
    [
      ...(createdGroups.data || []).map((_) => ({ ..._, owner: true })),
      // ...(joinedGroups.data || []).map((_) => ({
      //   ..._.rabbleGroup,
      // })),
    ],
    "_id"
  );
  // console.log({ createdGroupsUnique });
  const joinedGroupsUnique = _.uniqBy(
    [
      ...(joinedGroups.data || []).map((_) => ({
        ..._.rabbleGroup,
      })),
    ],
    "_id"
  );

  const handleRabbleGroupSelect = useCallback(
    (group: string, isCareGiverManagedGroup?: boolean) => {
      // navigation.navigate("PostOrGroupPostDetailsScreen");
      navigation.navigate("RabbleDetailsScreenV2", {
        groupId: group,
        isCareGiverManagedGroup,
        tab,
      });
    },
    []
  );

  const handleRabbleExploreGroupSelect = useCallback(
    (group: string, isCareGiverManagedGroup?: boolean) => {
      // navigation.navigate("PostOrGroupPostDetailsScreen");
      navigation.navigate("RabbleDetailsScreen", {
        groupId: group,
        isCareGiverManagedGroup,
        tab: 3,
      });
    },
    []
  );

  useFocusEffect(
    useCallback(() => {
      createdGroups.refetch();
    }, [])
  );

  useFocusEffect(
    useCallback(() => {
      (async () => {
        console.log("is this event capturing", tab);

        if (tab === 1) {
          await mixpanel.trackEvent(
            "Discover Rabbles filter selected (Rabble screen)",
            {
              email: user?.email || "",
              phone: user?.contact?.phone || "",
            },
            String(user?._id),
            "v2"
          );
        } else if (tab === 0) {
          await mixpanel.trackEvent(
            "My Rabbles filter selected(Rabble screen)",
            {
              email: user?.email || "",
              phone: user?.contact?.phone || "",
            },
            String(user?._id),
            "v2"
          );
        }
      })();
    }, [tab])
  );

  const handleRabbleGroupSearch = () => {
    authGuard(() => navigation.navigate("SearchRabblesScreen"));
  };
  const postType = "CONNECT";
  const handleCreate = () => {
    if (tab === 0) {
      const handleCreatePost = () => {
        authGuard(async () => {
          let sortedContacts: any[] = [];
          const { status } = await Contacts.requestPermissionsAsync();
          if (status === "granted") {
            const { data } = await Contacts.getContactsAsync({
              fields: [Contacts.Fields.Emails, Contacts.Fields.PhoneNumbers],
            });
            const formattedContacts = data.map((contact) => ({
              id: contact.id || "",
              name: contact.name || "",
              email: contact.emails?.[0]?.email || "",
              phoneNumbers:
                contact.phoneNumbers?.map((phone) => {
                  if (phone?.countryCode && phone?.digits) {
                    const dialingCode =
                      countryDialingCodes[phone.countryCode.toUpperCase()] ||
                      "";
                    return dialingCode
                      ? `+${dialingCode}${phone.digits}`
                      : phone.digits;
                  }
                  if (phone?.number) {
                    const numberStr = phone.number.trim();
                    if (numberStr.startsWith("+")) {
                      const matches = numberStr.match(/^\+(\d+)\s*\(?(.+)/);
                      if (matches && matches.length >= 3) {
                        const countryCode = matches[1];
                        const digits = matches[2].replace(/\D/g, "");
                        return `+${countryCode}${digits}`;
                      }
                    }
                    return numberStr.startsWith("+")
                      ? numberStr.replace(/[^\d+]/g, "")
                      : numberStr.replace(/\D/g, "");
                  }
                  return "";
                })[0] || "",
            }));

            // Match contacts with active users
            const matchedContacts = _.map(formattedContacts, (contact) => {
              const matchedUser = _.find(activeUsers.data, (user) => {
                const emailMatch =
                  contact.email && _.get(user, "email") === contact.email;
                const phoneMatch =
                  contact.phoneNumbers &&
                  _.get(user, "contact.phone") === contact.phoneNumbers;
                return emailMatch || phoneMatch;
              });
              return {
                ...contact,
                userId: _.get(matchedUser, "_id", ""),
              };
            });

            sortedContacts = _.orderBy(matchedContacts, ["name"], ["asc"]);
          }
          console.log({ sortedContacts });
          if (postType)
            // navigation.navigate("CreatePostScreen", {
            //   postType,
            //   // rabbleGroup: rabbleGroupId,
            // });
            navigation.navigate("OnboardingContactList", {
              contacts: [
                ...sortedContacts,
                {
                  name: "test contact",
                  email: "<EMAIL>",
                  phoneNumbers: "+1234567890",
                  userId: "67eac43cd5f5058f9bf9050d",
                },
              ],
              fromConnectScreen: true,
            });
        });
      };
      handleCreatePost();
    } else if (tab === 1 || tab === 2 || tab === 3) {
      // createRabbleGroup();
      navigation.navigate("CreateNewRabble", { fromConnectScreen: true });
    }
  };

  const userTopics = trpc.manage.getUserTopics.useQuery({});

  const topics = trpc.manage.getTopics.useQuery(
    { topicIds: [userTopics?.data?.[0]?.topic?._id.toString() || ""] },
    {
      enabled: !!userTopics?.data?.[0]?.topic?._id.toString(),
    }
  );

  useFocusEffect(
    useCallback(() => {
      const fetchTopic = async () => {
        const topicId = await AsyncStorage.getItem("selectedTopic");
        userTopics?.refetch();
        setSelectedTopic(topicId || "");
      };

      fetchTopic();
    }, [])
  );

  // useEffect(() => {
  //   (async() => {
  //     const topicId = await AsyncStorage.getItem('selectedTopic') || ''
  //     setSelectedTopic(topicId)
  //   })();
  // }, []);

  const streak = trpc.manage.getStreak.useQuery(
    {
      topicId:
        selectedTopic || userTopics?.data?.[0]?.topic?._id.toString() || "",
    },
    {
      enabled: !!userTopics.data?.[0]?._id.toString(),
      refetchOnReconnect: false,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
    }
  );

  return (
    <Screen includeHeader showThreeDots headerClasses="mx-4">
      <View className="flex-1 p-0 mt-2">
        <View className="flex-row items-center justify-between px-4 pb-[10px]">
          <Text className="font-montserratSemiBold text-[35px] pb-[10px] text-primary">
            Rabbles
          </Text>
          <PlusIcon onPress={handleCreate} />
        </View>

        <View className="mb-2 -mt-8 px-4 flex-row items-center justify-between">
          <ManageHomeScreenPills
            topics={topics?.data ?? []}
            streak={streak.data}
            userTopics={userTopics.data ?? []}
            selectedTopic={
              selectedTopic || userTopics?.data?.[0]?.topic?._id.toString()
            }
            onTopicSelect={(topicId) => {
              AsyncStorage.setItem("selectedTopic", topicId);
              navigation.navigate("ManageNavigator", {
                screen: "ManageHomeScreen",
                params: { topicId },
              });
            }}
            fromConnectScreen
          />
          <View className="mb-2">
            <ButterFlyIcon height={80} width={40} />
          </View>
        </View>
        {/* Search */}
        <AiSearchInput
          placeholder="Ask Belle AI or Search"
          iconLeft={
            <View className="mr-2">
              <PlaceholderLogo />
            </View>
          }
        />
        {/* <Pressable
        actionTag="rabble groups search"
        className="border border-neutral-300 rounded-lg p-2 mt-4 flex-row items-center"
        style={{ gap: 8 }}
        onPress={handleRabbleGroupSearch}
      >
        <Search color="#acacac" />
        <AppText className="text-neutral-400 italic">search...</AppText>
      </Pressable> */}

        <View className="px-2 ml-2">
          <Tabs
            tabState={[tab, setTab]}
            tabs={["Chats", "Owned", "Joined", "Explore"]}
            selectedbgClass="bg-[#0B79D3]"
            shadowStyle={
              Platform.OS !== "ios"
                ? {
                    shadowColor: "#000000",
                    shadowOffset: { width: 10, height: 10 },
                    shadowOpacity: 6,
                    shadowRadius: 6,
                    elevation: 4,
                  }
                : {
                    shadowColor: "#000000",
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: 0.2,
                    shadowRadius: 6,
                    elevation: 8,
                  }
            }
          />
        </View>
        {/* {tab === 3 && (
        <View className="flex-1 py-0">
          <AppText className="text-[20px] text-[#336D9F] mb-4">
            Getting Started...
          </AppText>
          <View className=" justify-between bg-white rounded-xl shadow-md shadow-primary/20 w-[200px]">
            <View className="items-center">
              <Text className="text-[20px] text-600 text-center text-[#336D9F] font-montserrat mb-2 mt-2">
                How to create a{"\n"} Rabble
              </Text>
              <View className="bg-[#E1F1FF] border-[1px] border-[#B4DDFF] px-10 py-10 rounded-xl flex-row items-center justify-center space-x-1 mb-6 relative">
                <View className="absolute -left-2 -top-4">
                  <ContactIcon />
                </View>
                <View className="absolute right-0 top-0">
                  <Text className="text-[32px]">🫶</Text>
                </View>
                <View className="absolute left-0 bottom-0">
                  <Text className="text-[24px]">🔔</Text>
                </View>
                <View className="absolute border-[1px] border-[#B4DDFF] bottom-2 -right-8 bg-white rounded-full px-2 py-1 shadow-md">
                  <Text className="text-[12px] text-[#336D9F]">👍🏾 3 👍🏼 2</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      )} */}

        <View className={`flex-1`}>
          {tab === 0 && (
            <ConnectPosts />
            // <FlatList
            //   numColumns={2}
            //   data={[
            //     {
            //       name: "Carla Baker",
            //       message:
            //         "Hey there!  I'm attending a severe asthma webinar Friday, want to join me?  ",
            //     },
            //   ]}
            //   renderItem={({ item }) => <PostOrGroupCard {...item}  />}
            // />
          )}
          {tab === 1 && (
            <View className="mt-[-16px]">
              <FlatList
                numColumns={1}
                data={createdGroupsUnique}
                keyExtractor={(item) => item._id.toString()}
                contentContainerStyle={{
                  // gap: 12,
                  // marginTop: 16,
                  paddingHorizontal: 16,
                  paddingTop: 16,
                }}
                // columnWrapperStyle={{
                // gap: 12,
                // marginBottom: 15
                // justifyContent: "space-between",
                // marginBottom: 0,
                // }}
                renderItem={({ item }) => {
                  return (
                    <View style={{ alignItems: "center" }}>
                      <PostOrGroupCard
                        onPress={() =>
                          handleRabbleGroupSelect(item?._id?.toString())
                        }
                        name={item?.groupName || ""}
                        message={item?.groupDescription || ""}
                        imageUrl={item?.image || ""}
                        date={format(new Date(item?.createdAt), "dd-MMM-yy")}
                        privacy={item?.privacy}
                      />
                    </View>
                  );
                }}
              />
            </View>
          )}
          {tab === 2 && (
            <View className="mt-[-16px]">
              <FlatList
                numColumns={1}
                data={joinedGroupsUnique}
                contentContainerStyle={{
                  // gap: 12,
                  // marginTop: 16,
                  paddingHorizontal: 16,
                  paddingTop: 16,
                }}
                // columnWrapperStyle={{
                // gap: 12,
                // marginBottom: 15
                //   justifyContent: "space-between",
                //   marginBottom: 0,
                // }}
                keyExtractor={(item) => item._id.toString()}
                renderItem={({ item }) => (
                  <View style={{ alignItems: "center" }}>
                    <PostOrGroupCard
                      onPress={() =>
                        handleRabbleGroupSelect(item?._id?.toString())
                      }
                      // {...item}
                      name={item?.groupName || ""}
                      message={item?.groupDescription || ""}
                      imageUrl={item?.image || ""}
                      date={format(new Date(item?.createdAt), "dd-MMM-yy")}
                      privacy={item?.privacy}
                    />
                  </View>
                )}
                // renderItem={({ item }) => (
                //   <RabbleGroupListItem
                //     onPress={handleRabbleGroupSelect}
                //     {...item}
                //   />
                // )}
              />
            </View>
          )}
          {tab === 3 && (
            <AppScrollView>
              <Pressable
                onPress={() => setRabbleCardOpen(true)}
                className="flex-1 py-0 px-6"
              >
                <Text className="text-[22px] font-[600] font-[Montserrat] text-[#336D9F] mb-4">
                  Getting Started...
                </Text>
                <View className=" justify-between bg-white rounded-xl shadow-md shadow-primary/20 w-[200px]">
                  <View className="items-center">
                    <Text className="text-[14px] font-[700] text-center text-[#336D9F] font-montserrat mb-2 mt-2">
                      How to create a{"\n"} Rabble
                    </Text>
                    <View className="bg-[#E1F1FF] border-[1px] border-[#B4DDFF] px-10 py-10 rounded-xl flex-row items-center justify-center space-x-1 mb-6 relative">
                      <View className="absolute -left-2 -top-2">
                        <ContactIcon />
                      </View>
                      <View className="absolute right-0 top-0">
                        <Text className="text-[32px]">🫶</Text>
                      </View>
                      <View className="absolute left-0 bottom-0">
                        <Text className="text-[24px]">🔔</Text>
                      </View>
                      <View className="absolute border-[1px] border-[#B4DDFF] bottom-2 -right-8 bg-white rounded-full px-2 py-1 shadow-md">
                        <Text className="text-[12px] text-[#336D9F]">
                          👍🏾 3 👍🏼 2
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              </Pressable>
              <Text className="mt-4 px-6 text-[22px] font-[600] leading-[28.6px] text-[#336D9F] font-montserrat">
                Explore
              </Text>

              <FlatList
                numColumns={1}
                data={discoverGroups.data}
                contentContainerStyle={{
                  // gap: 12,
                  // marginTop: 16,
                  paddingHorizontal: 20,
                  paddingTop: 16,
                }}
                // columnWrapperStyle={{
                // gap: 12,
                // marginBottom: 15
                //   justifyContent: "space-between",
                //   marginBottom: 0,
                // }}
                keyExtractor={(item) => item._id.toString()}
                renderItem={({ item }) => (
                  <View style={{ alignItems: "center" }}>
                    <PostOrGroupCard
                      onPress={() =>
                        handleRabbleExploreGroupSelect(item?._id?.toString())
                      }
                      // {...item}
                      name={item?.groupName}
                      message={item?.groupDescription}
                      imageUrl={item?.image || ""}
                      date={format(new Date(item?.createdAt), "dd-MMM-yy")}
                      privacy={item?.privacy}
                    />
                  </View>
                )}
                // renderItem={({ item }) => (
                //   <RabbleGroupListItem
                //     {...item}
                //     onPress={handleRabbleGroupSelect}
                //   />
                // )}
              />
            </AppScrollView>
          )}
        </View>
        <CreateRabblePopup
          visible={isOpenRabbleCard}
          onClose={() => setRabbleCardOpen(false)}
          fromConnectScreen
        />
      </View>
    </Screen>
  );
}
