import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useState } from "react";
import { Dimensions, View, Text } from "react-native";
import AppButton from "@components/common/AppButton";
import BookIcon from "@assets/svg/BookIcon";
import CompleteDailyCheckInIcon from "@assets/svg/CompleteDailyCheckInIcon";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import AppTextInput from "@components/common/AppTextInput";
import AppFormInput from "@components/form/AppFormInput";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import errorHandler from "@utils/errorhandler";
import { zodResolver } from "@hookform/resolvers/zod";
import AppFormCheckbox from "@components/form/AppFormCheckbox";
import AppText from "@components/common/AppText";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import AppScrollView from "@components/common/AppScrollView";
import mixpanel from "@utils/mixpanel";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const schema = z.object({
  login: z.string().min(1, "Email / Number is required"),
});
export const emailRegex = /\S+@\S+\.\S+/;
type Form = z.infer<typeof schema>;

export default function SignupScreenV1() {
  const navigation = useNavigation<RootNavigationProp>();
  const { user } = useSession();

  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      login: "",
    },
  });
  const { mutateAsync: requestOtpAsync, isLoading: isRequestOtpLoading } =
    trpc.auth.requestLoginOtp.useMutation();
  const { handleSubmit } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      console.log("formSumbitted");
      // mixpanel.trackEvent(
      //   "ONBOARDING_ACTION",
      //   {
      //     action: "Email or Phone entered (Step 13) (Account Creation)",
      //     email: data?.login,
      //     phone: data?.login,
      //   },
      //   "",
      //   "v2"
      // );
      await requestOtpAsync({
        ...(emailRegex.test(data.login)
          ? { email: data.login }
          : { phone: data.login }),
        deviceId: user?.deviceId,
      });
      mixpanel.trackEvent(
        "Email or phone added (Step 4)(Account Creation)",
        {
          email_or_phone: data.login 
        },
        user?._id?.toString(),
        "v2"
      );
      navigation.navigate("SignupOtpScreenV1", {
        phoneOrEmail: data.login,
        deviceId: user?.deviceId,
      });
    } catch (ex) {
      // @ts-expect-error
      if (ex?.message && ex?.message === "user already exist.") {
        methods.setError("login", {
          type: "manual",
          message: "User already exists. Please log in.",
        });
      } else {
        errorHandler(ex);
      }
    }
  });

  return (
    <Screen>
      <AppScrollView>
        <View className="flex-row items-center mt-8">
          <ChevronLeft
            color={"#004987"}
            size={22}
            // @ts-expect-error
            className="ml-4"
            onPress={() => navigation.goBack()}
          />
          <ProgressBar
            total={3}
            current={1}
            style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
          />
        </View>
        <View className="flex-1 items-center mt-4">
          <View className="flex-row items-center mb-8">
            <ButterFlyIcon height={140} width={90} />
            <QuestionPrompt
              dx={10}
              message="Let’s send you a magic code!  Enter your mobile number or email."
            />
          </View>

          <View
            className="flex-1 p-2 mt-[-32px]"
            style={{ width: cardWidth, gap: 8 }}
          >
            <FormProvider {...methods}>
              <AppFormInput
                name="login"
                fieldClass=""
                placeholderTextColor="#B4DDFF"
                placeholder="US mobile number or email"
                borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
                className="rounded-lg text-[18px]"
              />
              <View className="flex mt-48">
                <AnimatedAppButton
                  btnContainer="flex flex-row mb-4"
                  title="NEXT"
                  variant={"new-primary"}
                  className="flex-1 rounded-full"
                  textClassName="text-[21px]"
                  style={{
                    shadowColor: "#003366",
                    shadowOffset: { width: 0, height: 3 },
                    shadowOpacity: 1,
                    shadowRadius: 1,
                    elevation: 5,
                  }}
                  onPress={onSubmit}
                />
              </View>
            </FormProvider>
          </View>
        </View>
      </AppScrollView>
    </Screen>
  );
}
