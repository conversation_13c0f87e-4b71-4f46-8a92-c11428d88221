import { View } from "react-native";
import WebView from "react-native-webview";

const url = "https://www.rabblehealth.com/communityguidelines";
// const tncUrl = pdfPreview(
//   "https://rabblehealth-v2.s3.us-east-2.amazonaws.com/tnc/TermsOfUse_2023_08_29.pdf"
// );

export default function CommunityGuidelines() {
  return (
    <View className="flex flex-1">
      <WebView
        javaScriptEnabled={true}
        className="flex flex-1"
        source={{ uri: url }}
      />
    </View>
  );
}
