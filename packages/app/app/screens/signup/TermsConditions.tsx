import { View } from "react-native";
import WebView from "react-native-webview";

const tncUrl = "https://www.rabblehealth.com/termsofuse";
// const tncUrl = pdfPreview(
//   "https://rabblehealth-v2.s3.us-east-2.amazonaws.com/tnc/TermsOfUse_2023_08_29.pdf"
// );

export default function TermsConditions() {
  return (
    <View className="flex flex-1">
      <WebView
        javaScriptEnabled={true}
        className="flex flex-1"
        source={{ uri: tncUrl }}
      />
    </View>
  );
}
