import LoadingScreen from "@components/common/LoadingScreen";
import { pdfPreview } from "@utils/index";
import { useState } from "react";
import { View } from "react-native";
import WebView from "react-native-webview";

const PRIVACY_POLICY_PDF_URL =
  "https://rabblehealth-v2.s3.us-east-2.amazonaws.com/privacy/PrivacyPolicy_2023_08_29.pdf";
const PRIVACY_POLICY_URL = "https://www.rabblehealth.com/privacy-policy";

export default function PrivacyPolicy() {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <View className="flex flex-1">
      {isLoading && <LoadingScreen />}
      <WebView
        onLoadEnd={() => setIsLoading(false)}
        javaScriptEnabled
        className="flex flex-1"
        source={{ uri: PRIVACY_POLICY_URL }}
      />
    </View>
  );
}
