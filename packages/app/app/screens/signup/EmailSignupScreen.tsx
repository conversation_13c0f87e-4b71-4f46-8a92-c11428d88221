import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppFormCheckbox from "@components/form/AppFormCheckbox";
import AppFormInput from "@components/form/AppFormInput";
import { Entypo } from "@expo/vector-icons";
import { yupResolver } from "@hookform/resolvers/yup";
import usePersistedUser from "@hooks/persistUser";
import { User } from "@models/User";
import { RootNavigationProp } from "@navigation/root-navigator";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { useRoute, RouteProp, useNavigation } from "@react-navigation/native";
import useCreateUser, { useRequestOtp } from "@services/auth";
import errorHandler from "@utils/errorhandler";
import { useState } from "react";
import { useForm, FormProvider, SubmitHandler } from "react-hook-form";
import { ScrollView, View } from "react-native";
import * as yup from "yup";
import mixpanel from "@utils/mixpanel";
import { MIXPANEL_TOKEN } from "@constants/index";

const schema = yup
  .object({
    email: yup
      .string()
      .email()
      .required("This is a required field")
      .label("Email Address"),
    termsAccepted: yup
      .boolean()
      .required("This is a required field")
      .test({
        name: "alwaysTrue",
        test: (value) => value === true,
        message: "please agree to our terms and conditions to go forward",
      }),
    marketingConsentAccepted: yup.boolean().required(),
  })
  .required("This is a required field");

type FormData = yup.InferType<typeof schema>;

export default function EmailSignupScreen() {
  const { firstname, lastname, userName } =
    useRoute<RouteProp<RootNavParams, "EmailSignupScreen">>().params;

  const methods = useForm<FormData>({
    defaultValues: {
      email: "",
      termsAccepted: false,
      marketingConsentAccepted: false,
    },
    resolver: yupResolver(schema),
    mode: "onTouched",
  });

  const { handleSubmit } = methods;
  const { mutateAsync: requestOtpAsync } = useRequestOtp();

  const onSubmit: SubmitHandler<FormData> = async ({
    email,
    marketingConsentAccepted,
  }) => {
    try {
      // Request OTP using useRequestOtp hook
      const { data } = await requestOtpAsync({
        phoneOrEmail: email,
        type: "email",
      });

      // OTP requested successfully, navigate to Login otp screen
      navigation.navigate("SignupOtpScreen", {
        phoneOrEmail: email,
        type: "email",
        firstname,
        lastname,
        userName,
        marketingConsentAccepted,
      });
    } catch (ex) {
      const errorMessage = errorHandler(ex);
      if (errorMessage) methods.setError("email", { message: errorMessage });
    }
  };

  const navigation = useNavigation<RootNavigationProp>();

  const handleTC = () => {
    navigation.navigate("TermsConditions");
  };
  const handlePP = () => {
    navigation.navigate("PrivacyPolicy");
  };

  return (
    <View className="flex flex-1 px-4">
      <ScrollView
        contentContainerStyle={{
          paddingBottom: 10,
        }}
        keyboardDismissMode="interactive"
        keyboardShouldPersistTaps="handled"
        automaticallyAdjustKeyboardInsets
      >
        <View className="mb-8">
          <AppText className="text-2xl text-primary font-montserratSemiBold mb-4 mt-8">
            hello, {firstname}
          </AppText>

          <AppText className="text-primary">
            let’s finish setting up your account
          </AppText>
        </View>

        {/* Form */}
        <FormProvider {...methods}>
          <AppFormInput
            keyboardType="email-address"
            returnKeyType="next"
            autoCapitalize="none"
            name="email"
            placeholder="<EMAIL>"
            label="enter your email address"
            wrapperClass="my-4"
            importantForAutofill="yes"
            autoComplete="email"
          />

          <AppFormCheckbox
            name="termsAccepted"
            label={
              <AppText className="text-sm">
                by creating a RabbleHealth account, you agree to our{" "}
                <AppText
                  onPress={handleTC}
                  className="text-sm font-montserratSemiBold text-primary"
                >
                  terms and conditions
                </AppText>{" "}
                &{" "}
                <AppText
                  onPress={handlePP}
                  className="text-sm font-montserratSemiBold text-primary"
                >
                  {" "}
                  privacy policy
                </AppText>
              </AppText>
            }
          />
          <AppFormCheckbox
            name="marketingConsentAccepted"
            label={
              <AppText className="text-sm">
                Keep me informed of the enhancements and surveys
              </AppText>
            }
          />
        </FormProvider>
        <View className="mt-6 flex flex-row">
          <AppButton title="continue" onPress={handleSubmit(onSubmit)} />
        </View>
        <AppText className="mt-4" onPress={navigation.goBack}>
          sign up with{" "}
          <AppText className="font-montserratSemiBold text-primary">
            mobile
          </AppText>
        </AppText>
      </ScrollView>
    </View>
  );
}
