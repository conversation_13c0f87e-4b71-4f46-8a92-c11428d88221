import LogoSimple from "@assets/svg/LogoOutline";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import AppFormInput from "@components/form/AppFormInput";
import { yupResolver } from "@hookform/resolvers/yup";
import useDebounceValue from "@hooks/useDebounceValue";
import {
  CommonActions,
  CompositeNavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import errorHandler from "@utils/errorhandler";
import { useEffect, useRef, useState } from "react";
import { useForm, FormProvider, SubmitHandler } from "react-hook-form";
import { View } from "react-native";
import * as yup from "yup";
import AppFormCheckbox from "@components/form/AppFormCheckbox";
import { queryClient, rawTrpcClient, trpc } from "@providers/RootProvider";
import usePersistedUser, { useSession } from "@hooks/persistUser";
import { StackNavigationProp } from "@react-navigation/stack";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import mixpanel from "@utils/mixpanel";
import { ENV } from "@constants/index";
import { debounceAsync } from "@utils/debounceAsync";
import Toast from "react-native-toast-message";

const validateUsername = debounceAsync(
  async (username: string): Promise<boolean> => {
    try {
      if (username) {
        const data = await rawTrpcClient.user.userFilter.query({ username });
        return !data.length;
      }
      return true;
    } catch (err) {
      return true;
    }
  },
  500
);

const schema = yup
  .object({
    firstname: yup
      .string()
      .required("This is a required field")
      .label("First Name"),
    lastname: yup
      .string()
      .required("This is a required field")
      .label("Last Name"),
    userName: yup
      .string()
      .required("This is a required field")
      .label("User Name")
      .min(3)
      .max(15)
      .trim()
      .matches(/^\S+$/, "Username should not have spaces")
      .test("username", "username already exists", function (value: string) {
        return validateUsername(value).then((isValid) => {
          if (!isValid) {
            return this.createError({ message: "Username already exists" });
          }
          return true;
        });
      }),
    termsAccepted: yup
      .boolean()
      .required("This is a required field")
      .test({
        name: "alwaysTrue",
        test: (value) => value === true,
        message: "please agree to our terms and conditions to go forward",
      }),
    marketingConsentAccepted: yup.boolean().required(),
  })
  .required("This is a required field");

type FormData = yup.InferType<typeof schema>;

export default function SignupScreen() {
  const navigation =
    useNavigation<
      CompositeNavigationProp<
        StackNavigationProp<ProfileNavParams>,
        StackNavigationProp<RootNavParams>
      >
    >();

  const methods = useForm<FormData>({
    defaultValues: {
      firstname: "",
      lastname: "",
      userName: "",
      termsAccepted: false,
      marketingConsentAccepted: false,
    },
    resolver: yupResolver(schema),
    mode: "onTouched",
  });

  const { handleSubmit } = methods;

  const updateUserPartial = trpc.user.updateUserPartial.useMutation();
  const { persistedUser: user } = usePersistedUser();
  const { setPersistedUser } = usePersistedUser();

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    try {
      const {
        firstname,
        lastname,
        marketingConsentAccepted: marketingConsent,
        userName: username,
      } = data;

      const res = await updateUserPartial.mutateAsync({
        firstname,
        lastname,
        tncConsent: true,
        username,
        marketingConsent,
        privacyConsent: true,
        userOrPatientId: user?.data?._id?.toString() as string,
      });

      await setPersistedUser.mutateAsync({
        ...res,
        username,
        token: user?.data?.token ?? "",
      });
    } catch (ex) {
      const { message } = errorHandler(ex);
      methods.setError("userName", { message });
    }
  };

  const handleTC = () => navigation.navigate("TermsConditions");

  const handlePP = () => navigation.navigate("PrivacyPolicy");

  return (
    <View className="flex flex-1 px-4">
      <AppScrollView>
        <LogoSimple className="my-4 self-center" />

        <View className="mb-6">
          <AppText className="text-2xl font-montserratSemiBold text-primary mb-4">
            welcome to myRabble!
          </AppText>

          <AppText className="text-primary-light">
            Hello, my name is Belle. What is your preferred name?
          </AppText>
        </View>

        {/* Form */}
        <FormProvider {...methods}>
          <AppFormInput
            name="userName"
            placeholder="enter your username"
            label="username"
            labelHint="username between 5-15 characters"
            wrapperClass="mb-4"
            autoCapitalize="none"
          />
          <AppFormInput
            name="firstname"
            placeholder="enter your first name"
            label="first name"
            wrapperClass="mb-4"
          />
          <AppFormInput
            name="lastname"
            placeholder="enter your last name"
            label="last name"
            wrapperClass="mb-4"
          />
          <AppFormCheckbox
            name="termsAccepted"
            label={
              <AppText className="text-sm">
                by creating a RabbleHealth account, you agree to our{" "}
                <AppText
                  onPress={handleTC}
                  className="text-sm font-montserratSemiBold text-primary"
                >
                  terms and conditions
                </AppText>{" "}
                &{" "}
                <AppText
                  onPress={handlePP}
                  className="text-sm font-montserratSemiBold text-primary"
                >
                  {" "}
                  privacy policy
                </AppText>
              </AppText>
            }
          />
          <AppFormCheckbox
            name="marketingConsentAccepted"
            label={
              <AppText className="text-sm">
                Keep me informed of the enhancements and surveys
              </AppText>
            }
          />
        </FormProvider>
      </AppScrollView>
      <View className="flex flex-row items-center gap-4 mb-10">
        {/* <AppButton variant='outline' title='skip' onPress={handleSkip} /> */}
        <AppButton
          title="continue"
          onPress={handleSubmit(onSubmit)}
          disabled={updateUserPartial.isLoading}
          isLoading={updateUserPartial.isLoading}
        />
      </View>
    </View>
  );
}
