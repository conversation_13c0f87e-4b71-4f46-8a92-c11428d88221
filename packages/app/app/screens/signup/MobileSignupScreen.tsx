import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import AppFormCheckbox from "@components/form/AppFormCheckbox";
import { yupResolver } from "@hookform/resolvers/yup";
import { RootNavigationProp } from "@navigation/root-navigator";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { useRequestOtp } from "@services/auth";
import errorHandler from "@utils/errorhandler";
import { phoneSchema } from "@utils/index";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import * as yup from "yup";
import FormMobileInput from "@components/form/elements/FormMobileInput";

const schema = yup
  .object({
    phone: phoneSchema.required("This is a required field"),
    termsAccepted: yup
      .boolean()
      .required("This is a required field")
      .test({
        name: "alwaysTrue",
        test: (value) => value === true,
        message: "please agree to our terms and conditions to go forward",
      }),
    marketingConsentAccepted: yup.boolean(),
  })
  .required("This is a required field");

type FormData = yup.InferType<typeof schema>;

export default function MobileSignupScreen() {
  const navigation = useNavigation<RootNavigationProp>();

  const { firstname, lastname, userName } =
    useRoute<RouteProp<RootNavParams, "MobileSignupScreen">>().params;

  const methods = useForm<FormData>({
    defaultValues: {
      phone: "",
      termsAccepted: false,
      marketingConsentAccepted: false,
    },
    resolver: yupResolver(schema),
    mode: "all",
  });

  const { handleSubmit } = methods;
  const { mutateAsync: requestOtpAsync, isLoading: isRequestOtpLoading } =
    useRequestOtp();

  const onSubmit: SubmitHandler<FormData> = async ({
    phone,
    marketingConsentAccepted,
  }) => {
    try {
      // Request OTP using useRequestOtp hook
      await requestOtpAsync({ phoneOrEmail: phone, type: "phone" });
      // OTP requested successfully, navigate to SignupOtpScreen
      navigation.navigate("SignupOtpScreen", {
        firstname,
        lastname,
        phoneOrEmail: phone,
        userName,
        type: "phone",
        marketingConsentAccepted,
      });
    } catch (ex) {
      const errorMessage = errorHandler(ex);
      if (errorMessage) methods.setError("phone", { message: errorMessage });
    }
  };

  const handleSignupWithEmail = () => {
    navigation.navigate("EmailSignupScreen", { firstname, lastname, userName });
  };

  const handleTC = () => {
    navigation.navigate("TermsConditions");
  };

  const handlePP = () => {
    navigation.navigate("PrivacyPolicy");
  };

  return (
    <View className="flex flex-1 px-4 pt-10">
      <AppScrollView>
        <View className="mb-8">
          <AppText className="text-2xl text-primary font-montserratSemiBold mb-4">
            hello, {firstname}
          </AppText>

          <AppText className="text-primary">
            let’s finish setting up your account
          </AppText>
        </View>

        {/* Form */}
        <FormProvider {...methods}>
          <FormMobileInput name="phone" />

          <AppFormCheckbox
            name="termsAccepted"
            label={
              <AppText className="text-sm">
                by creating a RabbleHealth account, you agree to our{" "}
                <AppText
                  onPress={handleTC}
                  className="font-montserratSemiBold text-primary"
                >
                  terms and conditions
                </AppText>{" "}
                &{" "}
                <AppText
                  onPress={handlePP}
                  className="font-montserratSemiBold text-primary"
                >
                  {" "}
                  privacy policy
                </AppText>
              </AppText>
            }
          />
          <AppFormCheckbox
            name="marketingConsentAccepted"
            label={
              <AppText className="text-sm">
                Keep me informed of the enhancements and surveys
              </AppText>
            }
          />
        </FormProvider>
        <View className="mt-4 flex flex-row">
          <AppButton
            title="continue"
            isLoading={isRequestOtpLoading}
            onPress={handleSubmit(onSubmit)}
          />
        </View>

        <AppText className="mt-4" onPress={handleSignupWithEmail}>
          sign up with{" "}
          <AppText className="font-montserratSemiBold text-primary">
            email
          </AppText>
        </AppText>
      </AppScrollView>
    </View>
  );
}
