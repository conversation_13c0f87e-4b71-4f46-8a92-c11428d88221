import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import AppText from "@components/common/AppText";
import AppFormOtpInput from "@components/form/AppFormOtpInput";
import constants from "@constants/index";
import { yupResolver } from "@hookform/resolvers/yup";
import usePersistedUser from "@hooks/persistUser";
import { useInterval } from "@hooks/useInterval";
import { RootNavigationProp } from "@navigation/root-navigator";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import {
  useCreateUserwithEmailOrMobileNumber,
  useRequestOtp,
} from "@services/auth";
import errorHandler from "@utils/errorhandler";
import { formatStringToUsPhoneNumberFormat } from "@utils/index";
import clsx from "clsx";
import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import * as yup from "yup";

const schema = yup
  .object({
    otp: yup
      .string()
      .matches(/^\d{4}$/, "Magic code must be exactly 4 digits")
      .required("This is a required field")
      .max(4, "Magic code must be exactly 4 digits")
      .label("magic code"),
  })
  .required("This is a required field");

type FormData = yup.InferType<typeof schema>;

export default function SignupOtpScreen() {
  const { setPersistedUser } = usePersistedUser("signup");
  const navigation = useNavigation<RootNavigationProp>();
  // TODO: remove
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const {
    firstname,
    lastname,
    phoneOrEmail,
    userName,
    type,
    marketingConsentAccepted,
  } = useRoute<RouteProp<RootNavParams, "SignupOtpScreen">>().params;

  // otp timers
  const [otpTimer, setOtpTimer] = useState(60);
  useInterval(() => setOtpTimer((_) => _ - 1), otpTimer <= 0 ? null : 1000);

  // form methods
  const methods = useForm<FormData>({
    defaultValues: { otp: "" },
    resolver: yupResolver(schema),
  });

  const { handleSubmit } = methods;
  const {
    mutateAsync: createUserwithEmailOrMobileAsync,
    isLoading: isUserCreatedLoading,
  } = useCreateUserwithEmailOrMobileNumber();

  const { mutateAsync: requestOtpAsync } = useRequestOtp();

  const resendOtp = async () => {
    try {
      setOtpTimer(60);
      await requestOtpAsync({ phoneOrEmail: phoneOrEmail, type });
    } catch (ex) {
      const errorMessage = errorHandler(ex);
      if (errorMessage) methods.setError("otp", { message: errorMessage });
    }
  };

  const onSubmit: SubmitHandler<FormData> = async ({ otp }) => {
    try {
      const user = await createUserwithEmailOrMobileAsync({
        otp,
        emailOrphoneNo:
          type === "email"
            ? phoneOrEmail
            : constants.countryCode + phoneOrEmail,
        firstName: firstname,
        lastName: lastname,
        signupRole: "PATIENT",
        userName: userName,
        type,
        tncAccepted: true,
        privacyAccepted: true,
        marketingConsentAccepted: marketingConsentAccepted,
        caregiverConsentAccepted: false,
      });

      // persist the user to local storage and register event to mixpanel
      if (user) await setPersistedUser.mutateAsync(user);
    } catch (ex) {
      const errorMessage = errorHandler(ex);
      if (errorMessage) methods.setError("otp", { message: errorMessage });
    }
  };

  return (
    <View className="flex flex-1 px-4">
      <AppScrollView>
        <View className="lg:w-[600px] lg:p-8 lg:shadow-sm lg:bg-white lg:self-center lg:rounded-md lg:mt-20">
          <AppText className="text-primary font-montserratSemiBold my-6">
            please enter magic code sent to {"\n"}
            {type === "email"
              ? phoneOrEmail
              : `${constants.countryCode} ${formatStringToUsPhoneNumberFormat(
                  phoneOrEmail
                )}`}
          </AppText>
          {/* Form */}
          <FormProvider {...methods}>
            <AppFormOtpInput name="otp" />

            <View className="flex flex-row mt-6 mb-4">
              <AppButton
                title="continue"
                isLoading={isUserCreatedLoading}
                onPress={handleSubmit(onSubmit)}
              />
            </View>

            <AppText
              className={clsx(
                otpTimer <= 0 && "underline text-primary font-montserratBold"
              )}
              onPress={!otpTimer ? resendOtp : undefined}
            >
              resend code {otpTimer > 0 ? " in" : ""}
              {!!otpTimer && (
                <AppText className="text-primary font-montserratMedium">
                  {" "}
                  {otpTimer}s{" "}
                </AppText>
              )}
            </AppText>
          </FormProvider>
        </View>
      </AppScrollView>
    </View>
  );
}
