import Screen from "@components/common/Screen";
import { useEffect } from "react";
import { <PERSON>ing, ScrollView, Share, StyleSheet, View } from "react-native";
import Banner from "@components/learn/Banner";
import { LearnNavParams } from "@navigation/learn-navigator/LearnNavParams";
import { RouteProp, useRoute } from "@react-navigation/native";
import Title from "@components/learn/Title";
import AppText from "@components/common/AppText";
import { ENV } from "@constants/index";
import { Share2Icon, HeartIcon } from "lucide-react-native";
import { trpc } from "@providers/RootProvider";
import { format } from "date-fns";
import Pressable from "@components/common/Pressable";
import MarkDown from "@components/common/MarkDown";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";
import _ from "lodash";
import { useState } from "react";

export default function BlogDetailsScreen() {
  const {
    params: { blog: blogId },
  } = useRoute<RouteProp<LearnNavParams, "BlogDetailsScreen">>();
  const { user } = useSession();
  const [isFavorited, setIsFavorited] = useState(false);

  const { data: blog, isLoading } = trpc.blog.getBlog.useQuery({
    blog: blogId,
  });

  const { data: likeStatus } = trpc.blog.getBlogLikeStatus.useQuery({
    blog: blogId,
  });

  const utils = trpc.useUtils();

  const likeBlogMutation = trpc.blog.likeBlog.useMutation({
    onSuccess: () => {
      // Invalidate queries to refresh data
      utils.blog.getFavoritedBlogs.invalidate();
      utils.blog.getBlogLikeStatus.invalidate({ blog: blogId });
    },
    onError: (error) => {
      console.error("Error toggling favorite:", error);
      // Revert optimistic update on error
      setIsFavorited(!isFavorited);
    },
  });

  useEffect(() => {
    (async () => {
      if (_.size(blog)) {
        await mixpanel.trackEvent(
          "Learn post details screen view",
          {
            post_name: blog?.title || "", // need to re-verify row=>36
            post_id: blogId.toString() || "",
            email: user?.email || "",
            phone: user?.contact?.phone || "",
          },
          String(user?._id),
          "v2"
        );
      }
    })();
  }, [blog]);

  // Update favorite status when like status is loaded
  useEffect(() => {
    setIsFavorited(!!likeStatus);
  }, [likeStatus]);

  const handleShareBlog = () => {
    Share.share({
      message: `${ENV.EXPO_PUBLIC_RABBLE_INV_BASE_URL}/learn/${String(blogId)}`,
    });
  };

  const handleToggleFavorite = async () => {
    try {
      await likeBlogMutation.mutateAsync({
        blog: blogId,
        status: !isFavorited,
      });

      // Track favorite action in mixpanel
      await mixpanel.trackEvent(
        isFavorited ? "Blog unfavorited" : "Blog favorited",
        {
          blog_id: blogId,
          blog_title: blog?.title || "",
          email: user?.email || "",
          phone: user?.contact?.phone || "",
        },
        String(user?._id),
        "v2"
      );
    } catch (error) {
      console.error("Error toggling favorite:", error);
    }
  };

  if (isLoading || !blog) return null;
  return (
    <Screen className="flex flex-1 mx-4">
      <ScrollView>
        <View style={{ gap: 4 }} className="flex">
          <Title classNames="text-2xl" value={blog.title} />
          <View className="flex-row items-center justify-between">
            <View className="mt-3 flex flex-row flex-start items-center">
              <AppText className="text-[16px] text-[#191D23] font-montserratMedium">
                by{" "}
                <AppText className="text-[16px] text-[#191D23] capitalize font-montserratSemiBold">
                  {blog.author}{" "}
                </AppText>
              </AppText>

              <AppText className="text-[14px] text-[#191D23] font-montserratRegular">
                {format(new Date(blog.publishDate), "MMM dd, yyyy")}
              </AppText>
            </View>

            <View className="flex-row items-center gap-3">
              <Pressable
                actionTag="favorite blog"
                className="m-0 p-0 flex justify-center items-center"
                onPress={handleToggleFavorite}
                disabled={likeBlogMutation.isLoading}
              >
                <HeartIcon
                  size={18}
                  color={isFavorited ? "#FF4444" : "#999999"}
                  fill={isFavorited ? "#FF4444" : "transparent"}
                />
              </Pressable>

              <Pressable
                actionTag="share blog"
                className="m-0 p-0 flex justify-center items-center"
                onPress={handleShareBlog}
              >
                <Share2Icon size={18} color="#FF8E1C" />
              </Pressable>
            </View>
          </View>
          <View className="mt-3">
            <Banner url={blog.banner} />
          </View>
          <View className="mt-3">
            <MarkDown content={blog.contentHtml} />
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
}
