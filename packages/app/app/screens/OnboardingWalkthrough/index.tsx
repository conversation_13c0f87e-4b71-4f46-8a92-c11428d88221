import OnboardingW3 from "@assets/svg/OnboardingW3";
import OnboardingW4 from "@assets/svg/OnboardingW4";
import OnboardingW6 from "@assets/svg/OnboardingW6";
import W1 from "@assets/svg/W1";
import W2 from "@assets/svg/W2";
import withPressAnimation from "@components/common/AnimateButton";
import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import Screen from "@components/common/Screen";
import AnimatedLogo from "@components/walkthrough/AnimatedLogo";
import Walkthrough from "@components/walkthrough/Walkthrough";
import { RootNavigationProp } from "@navigation/root-navigator";
import { useNavigation } from "@react-navigation/native";
import { ChevronLeft } from "lucide-react-native";
import { useState } from "react";
import { Dimensions, Image, View } from "react-native";
import Animated, {
  Extrapolate,
  SharedValue,
  interpolate,
  useAnimatedRef,
  useAnimatedStyle,
  useScrollViewOffset,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

const { width } = Dimensions.get("screen");
const AnimatedAppButton = withPressAnimation(AppButton);

function Logo() {
  return (
    <Image
      style={{
        width: 160,
        height: 70,
      }}
      resizeMode="contain"
      source={require("../../assets/img/logo-large.png")}
    />
  );
}
export default function OnboardingWalkThroughRabbleScreen() {
  const [currentPageIndex, setCurrentPageIndex] = useState(0);

  const navigation = useNavigation<RootNavigationProp>();

  const opacity = useSharedValue(1);
  const animatedWalkthroughContainerStyles = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const animatedScrollRef = useAnimatedRef<Animated.ScrollView>();
  const scrollX = useScrollViewOffset(animatedScrollRef);

  const scrollNextPage = () => {
    const currentPage = scrollX.value / width;
    const nextPage = currentPage + 1;
    animatedScrollRef.current?.scrollTo({
      x: nextPage * width,
      animated: true,
    });

    return currentPage;
  };

  const handleNavigationToLoginSplashScreen = () =>
    navigation.navigate("OnboardingContacts");

  const handleBackScreen = () => {
    const currentPage = scrollX.value / width;
    const previousPage = Math.max(currentPage - 1, 0);
    animatedScrollRef.current?.scrollTo({
      x: previousPage * width,
      animated: true,
    });
  };

  const handleNextScreen = () => {
    const currentPage = scrollNextPage();
    if (currentPage >= 1.6) handleNavigationToLoginSplashScreen();
  };

  const currentIndex = () => {
    const currentPage = Math.round(scrollX.value / width);
    setCurrentPageIndex(currentPage);
  };

  return (
    <Screen className="flex-1">
      <Animated.ScrollView
        overScrollMode="never"
        ref={animatedScrollRef}
        horizontal
        pagingEnabled
        style={[{}, animatedWalkthroughContainerStyles]}
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        onMomentumScrollEnd={currentIndex}
      >
        <Walkthrough
          contentClassName="-mt-12"
          Svg={
            <View>
              <View className="bg-[#0B79D3] pb-16">
                <OnboardingW3
                  className="self-center min-h-[300px] mt-8"
                  style={{ transform: [{ scale: 0.68 }] }}
                />
              </View>
              <View className="h-[160px] ml-4">
                <Logo />
              </View>
            </View>
          }
          title="build health knowledge with ad-free lessons"
        />
        <Walkthrough
          contentClassName="-mt-12"
          Svg={
            <View>
              <View className="bg-[#0B79D3] pb-16">
                <OnboardingW4 className="self-center min-h-[300px] mt-16" />
              </View>
              <View className="h-[160px] ml-4">
                <Logo />
              </View>
            </View>
          }
          title="develop a check-in habit  with loved ones"
        />
        <Walkthrough
          contentClassName="-mt-12"
          Image={
            <View>
              <View className="bg-[#0B79D3] pb-16">
                <Image
                  className="self-center"
                  source={require("../../assets/img/W3.png")}
                />
              </View>
              <View className="h-[160px] ml-4">
                <Logo />
              </View>
            </View>
          }
          title="track progress  with health goals"
        />
        <Walkthrough
          contentClassName="-mt-12"
          Svg={
            <View>
              <View className="bg-[#0B79D3] pb-8">
                <OnboardingW6 className="self-center min-h-[300px] mt-16" />
              </View>
              <View className="h-[160px] ml-4">
                <Logo />
              </View>
            </View>
          }
          title="share progress with doctors and others"
        />
      </Animated.ScrollView>

      <View className="flex flex-row items-center justify-between mx-4 mb-6">
        <Pressable
          actionTag="walkthrough button"
          className="flex flex-1"
          // onPress={handleNavigationToLoginSplashScreen}
          onPress={handleBackScreen}
          hitSlop={10}
        >
          {currentPageIndex !== 0 && (
            <AppText className="text-base text-[#004987] font-montserratMedium">
              back
            </AppText>
          )}
        </Pressable>
        <View className="flex flex-row items-center flex-1 pb-32 left-[40%] absolute">
          {new Array(4).fill("").map((_, idx) => {
            return <CarouselDot key={idx} scrollX={scrollX} index={idx} />;
          })}
        </View>
        <AnimatedAppButton
          btnContainer="flex flex-row p-1"
          title="NEXT"
          variant="new-primary"
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          onPress={handleNextScreen}
          style={{
            shadowColor: "#004987",
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 1,
            shadowRadius: 1,
            elevation: 5,
          }}
        />
      </View>
    </Screen>
  );
}

interface CarouselDotProps {
  scrollX: SharedValue<number>;
  index: number;
}

export function CarouselDot({ scrollX, index }: CarouselDotProps) {
  const styles = useAnimatedStyle(() => {
    return {
      width: interpolate(
        scrollX.value,
        [(index - 1) * width, index * width, (index + 1) * width],
        [8, 28, 8],
        Extrapolate.CLAMP
      ),
      opacity: interpolate(
        scrollX.value,
        [(index - 1) * width, index * width, (index + 1) * width],
        [0.4, 1, 0.4],
        Extrapolate.CLAMP
      ),
    };
  });

  return (
    <Animated.View
      style={styles}
      className="w-2 h-2 bg-[#FF8E1C] rounded-full mx-1"
    />
  );
}
