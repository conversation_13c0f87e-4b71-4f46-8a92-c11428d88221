import Screen from "@components/common/Screen";
import {
  ScrollView,
  StyleSheet,
  View,
  Linking,
  Image,
  Dimensions,
} from "react-native";
import Email from "@assets/svg/services/Email";
import Phone from "@assets/svg/services/Phone";
import Time from "@assets/svg/services/Time";
import Globe from "@assets/svg/services/Globe";
import AppText from "@components/common/AppText";
import { useGetServiceDetails } from "@services/service";
import { RouteProp, useRoute } from "@react-navigation/native";
import { ServicesNavParams } from "@navigation/services-navigator/profile-navigator/ServicesNavParams";
import { format, parseISO } from "date-fns";
import errorHandler from "@utils/errorhandler";
import LoadingScreen from "@components/common/LoadingScreen";
import { useEffect, useState } from "react";
import mixpanel from "@utils/mixpanel";
import { User } from "@models/User";
import AsyncStorage from "@utils/asyncStorage";
import InfoModal from "@components/common/InfoModal";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import _ from "lodash";
import Pressable from "@components/common/Pressable";

export default function ServicesDetail() {
  const {
    params: { serviceId },
  } = useRoute<RouteProp<ServicesNavParams, "ServicesDetailScreen">>();
  const { user } = useSession();

  const [modalVisisble, setModalVisible] = useState(false);
  const [serviceLogoHeight, setServiceLogoHeight] = useState(0);

  const screenWidth = Dimensions.get("screen").width;

  /** Open Modal */
  const openModal = () => setModalVisible(true);
  const closeModal = () => setModalVisible(false);

  /** Filter out the URL from the parenthesis */
  const extractStringsInsideParentheses = (code: string) => {
    // Check if there are parentheses in the code
    if (/\(.*?\)/.test(code)) {
      // Use regular expression to find strings inside parentheses
      var matches = code.match(/\((.*?)\)/g);

      if (matches) {
        var extractedStrings = [];
        for (var i = 0; i < matches.length; i++) {
          // Remove the parentheses from the matched string
          var stringWithoutParentheses = matches[i].replace(/[\(\)]/g, "");
          extractedStrings.push("https://" + stringWithoutParentheses + "/");
        }
        return extractedStrings;
      }
    }
    return [code];
  };

  const { data: serviceDetails, isLoading: isServiceDetailsLoading } =
    trpc.service.getServiceById.useQuery(serviceId);

  const { data: getCategoriesData, isLoading: getCategoriesLoading } =
    trpc.service.getCategories.useQuery();

  /** Open Link */
  const openUrl = async (url: string, errorMessage?: string) => {
    const category = _.find(getCategoriesData, cat => String(cat._id) === String(serviceDetails?.category))
    if(!errorMessage) {
      await mixpanel.trackEvent(
        "Services website link clicked (Services Screen)",
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
          category_name: category?.title || "",
          service_post_name: serviceDetails?.title || "",
          service_post_desc: serviceDetails?.description || "",
          website_link: url,
        },
        String(user?._id),
        "v2"
      );
    }
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
        await mixpanel.trackEvent(
          "SERVICE_WEBSITE_EVENT",
          _.mapValues(serviceDetails, String),
          String(user?._id)
        );
      }
    } catch (ex) {
      errorHandler(ex);
    }
  };

  /** Phone call */
  const makePhoneCall = (phoneNumber: string) => {
    const category = _.find(getCategoriesData, cat => String(cat._id) === String(serviceDetails?.category))

    mixpanel.trackEvent(
      "Services call clicked (Services Screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        category_name: category?.title || "",
        service_post_name: serviceDetails?.title || "",
        service_post_desc: serviceDetails?.description || "",
        call_no: phoneNumber || "",
      },
      String(user?._id),
      "v2"
    );
    const phoneUrl = `tel:${phoneNumber}`;
    const errorMessage = `Phone calls are not supported on this device`;
    openUrl(phoneUrl, errorMessage);
  };

  /** Email Id */
  const sendEmail = (mailId: string) => {
    const emailUrl = `mailto:${mailId}`;
    const errorMessage = `Email is not supported on this device`;
    openUrl(emailUrl, errorMessage);
  };

  useEffect(() => {
    if (!serviceDetails?.logo) return;
    Image.getSize(
      serviceDetails.logo,
      (width, height) => {
        setServiceLogoHeight(Math.max(Math.min(height, 200), 200));
      },
      (error) => {
        console.log("Error getting image size: ", error);
      }
    );
  }, [serviceDetails?.logo]);

  useEffect(() => {
    (async () => {
      const user = await AsyncStorage.retrieveData<User>("persisted-user");

      if (serviceDetails?.title && serviceDetails?.description && user?._id) {
        await mixpanel.trackEvent(
          "SERVICE_DETAIL",
          {
            serviceName: serviceDetails?.title,
            serviceDescription: serviceDetails?.description,
            email: serviceDetails?.email,
            mobile: serviceDetails?.phone,
            website: serviceDetails?.website,
            category: String(serviceDetails?.category),
            state: serviceDetails?.states.join(","),
          },
          String(user?._id)
        );
      }
    })();
  }, []);

  if (isServiceDetailsLoading) return <LoadingScreen />;
  return (
    <Screen className="flex flex-1">
      <InfoModal
        visible={modalVisisble}
        label="Redirecting..."
        description="you are being directed to a website outside myRabble app, are you sure you want to continue?"
        onRequestClose={setModalVisible}
        actionText="Yes"
        handleAction={() => {
          const link = serviceDetails?.website ?? "";
          const extractedStrings = extractStringsInsideParentheses(link);
          if (extractedStrings) {
            openUrl(extractedStrings[0]);
          }
          setModalVisible(false);
        }}
        secondaryText="No"
      />
      <ScrollView>
        <View className="justify-center items-center">
          <Image
            className="mt-4 "
            style={{
              height: serviceLogoHeight * 0.4,
              width: screenWidth * 0.4,
            }}
            resizeMode="contain"
            source={{ uri: serviceDetails?.logo }}
          />
          <AppText
            numberOfLines={2}
            className="text-[#004987] text-xl font-montserratSemiBold mt-4 mx-2 text-center"
          >
            {serviceDetails?.title}
          </AppText>
          <AppText
            numberOfLines={2}
            className="text-[#191D23] text-[16px] font-montserratSemiBold mx-2 text-center mt-4"
          >
            {serviceDetails?.states
              ?.filter((st) => st !== "National")
              .join(", ")}
          </AppText>
          <View className="flex-row pt-4 gap-6">
            {serviceDetails?.email && (
              <Pressable
                actionTag="service email"
                onPress={async () => {
                  serviceDetails?.email && sendEmail(serviceDetails.email);

                  await mixpanel.trackEvent(
                    "SERVICE_EMAIL_EVENT",
                    _.mapValues(serviceDetails, String),
                    String(user?._id)
                  );
                }}
              >
                <Email style={{ transform: [{ scale: 1.2 }] }} />
              </Pressable>
            )}
            {serviceDetails?.phone && (
              <Pressable
                actionTag="service phone"
                onPress={async () => {
                  serviceDetails?.phone && makePhoneCall(serviceDetails.phone);
                  await mixpanel.trackEvent(
                    "SERVICE_PHONE_EVENT",
                    _.mapValues(serviceDetails, String),
                    String(user?._id)
                  );
                }}
              >
                <Phone style={{ transform: [{ scale: 1.2 }] }} />
              </Pressable>
            )}
          </View>
        </View>
        {serviceDetails?.date && (
          <View className="bg-white rounded-lg p-3 m-4" style={styles.card}>
            <AppText className="text-[#091F44] text-[16px] font-montserratSemiBold">
              Availability
            </AppText>
            <View className="flex-row items-center gap-3 my-1">
              <Time />
              <AppText className="text-[#004987] text-sm font-montserratMedium">
                {format(parseISO(serviceDetails.date), "hh:mm a")}
              </AppText>
            </View>
          </View>
        )}
        {serviceDetails?.website && (
          <View className="bg-white rounded-lg p-3 m-4" style={styles.card}>
            <AppText className="text-[#091F44] text-[16px] font-montserratSemiBold">
              Website
            </AppText>
            <View className="flex-row items-center gap-3 my-1">
              <Globe />
              <Pressable
                onPress={() => serviceDetails?.website && openModal()}
                className="mr-2 w-[90%]"
                actionTag="sevice website"
              >
                <AppText
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="text-[#004987] text-sm font-montserratMedium"
                >
                  {serviceDetails?.website}
                </AppText>
              </Pressable>
            </View>
          </View>
        )}
        <View className="bg-white rounded-lg p-3 m-4" style={styles.card}>
          <AppText className="text-[#091F44] text-[16px] font-montserratSemiBold">
            Description
          </AppText>
          <View className="flex-row items-center gap-3 my-1">
            <AppText className=" text-[14px] font-montserratRegular">
              {serviceDetails?.description}
            </AppText>
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
}

const styles = StyleSheet.create({
  card: {
    shadowColor: "#004987",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});
