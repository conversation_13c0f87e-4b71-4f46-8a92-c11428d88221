import Screen from "@components/common/Screen";
import React, { useCallback } from 'react';
import ServiceCategoryCard from "@components/services/ServiceCategoryCard";
import { FlatList, Platform } from "react-native";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import ConnectLogo from "@assets/svg/connect/ConnectLogo";
import { StackNavigationProp } from "@react-navigation/stack";
import { ServicesNavParams } from "@navigation/services-navigator/profile-navigator/ServicesNavParams";
import { useEffect } from "react";
import useAuthGuard from "@hooks/useAuthGuard";
import usePersistedUser, { useSession } from "@hooks/persistUser";
import LoginEmailScreen from "@screens/login/__LoginEmailScreen";
import LoadingScreen from "@components/common/LoadingScreen";
import { trpc } from "@providers/RootProvider";
import LoginMobileScreen from "@screens/login/LoginMobileScreen";
import clsx from "clsx";
import mixpanel from "@utils/mixpanel";

export default function Services() {
  const navigation = useNavigation<StackNavigationProp<ServicesNavParams>>();

  const { user } = useSession();

  const { data: getCategoriesData, isLoading: getCategoriesLoading } =
    trpc.service.getCategories.useQuery();

    useFocusEffect(
      useCallback(() => {
        (async () => {
          console.log('Services screen view captured');
          await mixpanel.trackEvent(
            "Services screen view",
            {
              email: user?.email || "",
              phone: user?.contact?.phone || ""
            },
            String(user?._id),
            "v2"
          );
        })();
    
      }, [user?._id])
    );

  const navigateToDetail = (categoryId: string, categoryName: string) => {
    
    navigation.navigate("ServicesListingScreen", {
      categoryId,
      header: categoryName,
    });
  }

  if (getCategoriesLoading) return <LoadingScreen />;

  if (!user) return <LoginMobileScreen navigationScreen="ConnectNavigator" />;
  return (
    <Screen
      includeHeader
      headerClasses="mx-4"
      className={clsx("flex flex-1", Platform.OS === "android" && "pt-10")}
    >
      <FlatList
        data={getCategoriesData}
        keyExtractor={(item) => item._id.toString()}
        renderItem={({ item }) => (
          <ServiceCategoryCard item={item} onPress={navigateToDetail} />
        )}
        contentContainerStyle={{
          rowGap: 16,
          marginVertical: 12,
          marginHorizontal: 14,
        }}
        columnWrapperStyle={{ columnGap: 12 }}
        numColumns={2}
      />
    </Screen>
  );
}
