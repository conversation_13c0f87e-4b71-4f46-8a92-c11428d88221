import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import Screen from "@components/common/Screen";
import Filter from "@components/services/Filter";
import ServiceCard from "@components/services/ServiceCard";
import { ServicesNavParams } from "@navigation/services-navigator/profile-navigator/ServicesNavParams";
import { trpc } from "@providers/RootProvider";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { useEffect, useState, useMemo } from "react";
import { FlatList, ScrollView, View } from "react-native";
import _ from "lodash";
import AppCheckBox from "@components/common/AppCheckbox";
import AppButton from "@components/common/AppButton";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";
import CloseIcon from "@assets/svg/services/CloseIcon";

export default function ServicesListing() {
  const {
    params: { categoryId, header },
  } = useRoute<RouteProp<ServicesNavParams, "ServicesListingScreen">>();

  const navigation = useNavigation<StackNavigationProp<ServicesNavParams>>();

  const [selectedCategoryName, setSelectedCategoryName] =
    useState<string>(header);
  const [selectedState, setSelectedState] = useState<string>("");

  const [categorySheetVisible, setCategorySheetVisible] =
    useState<boolean>(false);
  const [stateSheetVisible, setStateSheetVisible] = useState<boolean>(false);
  const [topicSheetVisible, setTopicSheetVisible] = useState<boolean>(false);
  const [selectedTopicIds, setSelectedTopicIds] = useState<string[]>([]);
  const [activeTopicIds, setActiveTopicIds] = useState<string[]>([]);
  const { user } = useSession();

  // Fetch all tags directly from the backend
  const allTagsQuery = trpc.blog.tags.useQuery({ scope: ["services"] });

  // Create a mapping of tag IDs to tag objects
  const tagIdToTagMap = useMemo(() => {
    const map = new Map();

    // Add tags from the direct tags query
    if (allTagsQuery.data) {
      allTagsQuery.data.forEach((tag) => {
        map.set(tag._id.toString(), tag);
      });
    }

    return map;
  }, [allTagsQuery.data]);

  // Helper function to get tag name from ID
  const getTagName = (tagId: string) => {
    const tag = tagIdToTagMap.get(tagId);
    return tag ? tag.tag : tagId;
  };

  const createMixPanelEvent = (topicIds = selectedTopicIds) => {
    // Convert tag IDs to tag names for analytics
    const topicNames = topicIds.map((id) => getTagName(id));

    mixpanel.trackEvent(
      "Services filter applied (Services Screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        filter_name: "Topic filter",
        filter_value: JSON.stringify(topicNames?.join(",")) || "",
      },
      String(user?._id),
      "v2"
    );
  };

  const applyTopicsFilter = () => {
    createMixPanelEvent();
    setActiveTopicIds([...selectedTopicIds]);
    refetchServices();
  };

  // Fetch user topics to get initial tag selection
  trpc.manage.getUserTopics.useQuery(
    {},
    {
      initialData: [],
      onSuccess(data) {
        if (!data.length) return;

        // Extract tag IDs from user topics
        const tagIds = _.flatMap(
          data,
          (userTopic) =>
            userTopic?.topic.tags?.map((tag) =>
              typeof tag === "string" ? tag : tag._id?.toString()
            ) || []
        );

        if (tagIds.length > 0) {
          createMixPanelEvent([...tagIds]);
          setSelectedTopicIds([...tagIds]);
          setActiveTopicIds([...tagIds]);
        }
      },
    }
  );

  const allTopics = trpc.manage.getTopics.useQuery({ status: "Published" });

  // Get all available tags for the filter modal
  const availableTags = useMemo(() => {
    // Get tags from topics
    const topicTags = _.flatMap(allTopics?.data || [], (topic) => {
      return (topic.tags || []).map((tag) => {
        // Handle both string tags and object tags
        if (typeof tag === "string") {
          return { _id: tag, tag: getTagName(tag) };
        } else {
          const tagId = tag._id?.toString() || "";
          return { _id: tagId, tag: tag.tag || getTagName(tagId) };
        }
      });
    });

    // Add tags from the direct tags query
    const allTags = [...(allTagsQuery.data || [])];

    // Create a unique list of tags by _id
    const uniqueTags = _.uniqBy([...topicTags, ...allTags], (tag) => tag._id);

    // Update the tagIdToTagMap with all available tags
    uniqueTags.forEach((tag) => {
      if (tag._id) {
        tagIdToTagMap.set(tag._id.toString(), tag);
      }
    });

    return uniqueTags;
  }, [allTopics?.data, allTagsQuery.data]);

  useEffect(() => {
    if (categoryId) {
      navigation.setParams({ header: selectedCategoryName });
      setCategorySheetVisible(false);
      setStateSheetVisible(false);
      setTopicSheetVisible(false);
    }
  }, [selectedCategoryName, categoryId]);

  useEffect(() => {
    (async () => {
      mixpanel.trackEvent(
        "Services category screen view (Services Screen)",
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
          category_name: selectedCategoryName || "",
        },
        String(user?._id),
        "v2"
      );
    })();
  }, []);

  const {
    data: serviceListings,
    isLoading: isServiceListingsLoading,
    refetch: refetchServices,
  } = trpc.service.getService.useQuery({
    category: categoryId,
    ...(selectedState && { states: [selectedState, "National"] }),
    ...(activeTopicIds.length && { tags: activeTopicIds }),
  });

  const { data: getCategoriesData, isLoading: getCategoriesLoading } =
    trpc.service.getCategories.useQuery();

  const { data: stateFilterOptions, isLoading: isStateFilterOptionsLoading } =
    trpc.lib.states.useQuery();

  const onSelectCategory = (categoryName: string) => {
    mixpanel.trackEvent(
      "Services filter applied(Services Screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        filter_name: "category filter",
        filter_value: categoryName || "",
      },
      String(user?._id),
      "v2"
    );
    const selectedCategory = getCategoriesData?.find(
      (category) => category.title === categoryName
    );
    if (selectedCategory) {
      setSelectedCategoryName(categoryName);
      setCategorySheetVisible(false);
      navigation.setParams({ categoryId: selectedCategory._id }); // Set the categoryId
    }
  };

  const onSelectState = (stateName: string) => {
    mixpanel.trackEvent(
      "Services filter applied (Services Screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        filter_name: "state filter",
        filter_value: stateName || "",
      },
      String(user?._id),
      "v2"
    );
    setSelectedState(stateName);
    setStateSheetVisible(false);
  };

  const handleDeleteState = () => {
    setSelectedState("");
    setStateSheetVisible(false);
  };

  if (
    isStateFilterOptionsLoading ||
    isServiceListingsLoading ||
    getCategoriesLoading
  )
    return null;

  return (
    <Screen className="flex flex-1">
      <View style={{ gap: 4 }} className="flex-row items-center">
        <Filter
          name={selectedCategoryName || "Category"}
          isVisible={categorySheetVisible}
          onSheetStatusChange={setCategorySheetVisible}
          state={false}
        >
          <AppText className="text-xl font-montserratBold my-2 pb-5">
            Category
          </AppText>
          <ScrollView
            className="h-[40%] mb-5 gap-5"
            showsVerticalScrollIndicator={false}
          >
            {getCategoriesData?.map((category) => (
              <Pressable
                actionTag="service category"
                key={category?._id.toString()}
                onPress={() => onSelectCategory(category.title)}
              >
                <AppText>{category.title}</AppText>
              </Pressable>
            ))}
          </ScrollView>
        </Filter>
        <Filter
          state={false}
          name={selectedState || "State"}
          isVisible={stateSheetVisible}
          onSheetStatusChange={setStateSheetVisible}
          onDeleteState={handleDeleteState}
        >
          <View className="flex-row justify-between items-center">
            <AppText className="text-xl font-montserratBold my-2 pb-5">
              State
            </AppText>

            {/* Close Icon */}
            <Pressable
              actionTag="filter close"
              onPress={handleDeleteState}
              className="flex-row items-center"
            >
              <CloseIcon />
            </Pressable>
          </View>
          <ScrollView
            className=" mb-5 gap-5"
            showsVerticalScrollIndicator={false}
          >
            {stateFilterOptions?.map((state) => (
              <Pressable
                key={state}
                actionTag="service state filter"
                onPress={() => onSelectState(state)}
              >
                <AppText>{state}</AppText>
              </Pressable>
            ))}
          </ScrollView>
        </Filter>
        <Filter
          state={false}
          name={
            activeTopicIds?.length
              ? _.truncate(
                  activeTopicIds.map((id) => getTagName(id)).join(", "),
                  { length: 10 }
                )
              : "Tags"
          }
          isVisible={topicSheetVisible}
          onSheetStatusChange={setTopicSheetVisible}
        >
          <View className="bg-[#b0c7da] w-10 h-1 self-center" />
          <AppText className="text-xl font-montserratBold my-2 pb-5">
            Select Tags
          </AppText>
          <ScrollView
            className="h-[40%] mb-4 gap-5"
            showsVerticalScrollIndicator={false}
          >
            {availableTags?.map((tag) => {
              const tagId = tag._id?.toString() || "";
              const isSelected = selectedTopicIds.includes(tagId);

              return (
                <Pressable
                  key={tagId}
                  onPress={() => {
                    setSelectedTopicIds((prev) =>
                      isSelected
                        ? prev.filter((id) => id !== tagId)
                        : [...prev, tagId]
                    );
                  }}
                >
                  <View className="flex-row mb-4">
                    <AppCheckBox active={isSelected} pointerEvents="none" />
                    <View className="ml-2">
                      <AppText className="text-base">{tag.tag}</AppText>
                    </View>
                  </View>
                </Pressable>
              );
            })}
          </ScrollView>
          <View className="flex-row justify-between my-4 mb-8">
            <AppButton
              variant="outline"
              className="rounded-lg px-4 py-2 m-[4px]"
              onPress={() => {
                setSelectedTopicIds([]);
              }}
            >
              Reset
            </AppButton>
            <AppButton
              className="rounded-lg px-4 py-2 m-[4px]"
              onPress={() => {
                applyTopicsFilter();
                setTopicSheetVisible(false);
              }}
            >
              Apply Filter
            </AppButton>
          </View>
        </Filter>
      </View>
      <>
        {serviceListings?.length ?? 0 > 0 ? (
          <>
            <FlatList
              data={serviceListings || []}
              keyExtractor={(item) => item._id.toString()}
              renderItem={({ item }) => (
                <ServiceCard item={item} categoryName={selectedCategoryName} />
              )} // Render ServiceCard with the item prop
            />
          </>
        ) : (
          <View className="justify-center items-center mt-6">
            <AppText>No Data Found </AppText>
          </View>
        )}
      </>
    </Screen>
  );
}
