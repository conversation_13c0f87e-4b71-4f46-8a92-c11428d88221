import Pressable from "@components/common/Pressable";
import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Platform,
  Dimensions,
  TouchableWithoutFeedback,
  Image,
} from "react-native";
import { RouterOutput } from "../../../../shared";
import TopicLungsIcon from "@assets/svg/TopicLungsIcon";
import _ from "lodash";
import { Cog, EditIcon, PlusIcon } from "lucide-react-native";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { LearnNavParams } from "@navigation/learn-navigator/LearnNavParams";
import TopicBreastIcon from "@assets/svg/TopicBreastIcon";
import { Image as CacheImage } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import constants from "@constants/index";

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 1;

const TOPIC_ICON = {
  Asthma: () => <TopicLungsIcon />,
  "Asthma - 2": () => <TopicLungsIcon />,
  "Breast Cancer": () => <TopicBreastIcon />,
};

interface ModalProps {
  setIsVisible: (v: boolean) => void;
  isVisible: boolean;
  userTopics: RouterOutput["manage"]["getUserTopics"];
  selectedTopic?: string;
  onTopicSelect: (topicId: string) => void;
  fromConnectScreen?: boolean;
}

const ManageSubscribedTopicListCard = ({
  setIsVisible,
  isVisible,
  userTopics,
  selectedTopic,
  onTopicSelect,
  fromConnectScreen,
}: ModalProps) => {
  const userTopicIds = _.map(userTopics, "topic._id");
  const navigation =
    useNavigation<StackNavigationProp<RootNavParams & LearnNavParams>>();
  return (
    <View style={styles.container}>
      <Modal
        visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setIsVisible(false)}
      >
        <TouchableWithoutFeedback onPress={() => setIsVisible(false)}>
          <View style={styles.overlay}>
            <View style={[styles.popup, { width: cardWidth }]}>
              {/* Small Top Tab */}
              <View style={styles.topTab} />

              {/* <View className="flex-row items-center justify-between"> */}
              <View className="pb-6">
                {_.map(userTopics, (userTopic) => {
                  const topicKey = userTopic?.topic
                    ?.name as keyof typeof TOPIC_ICON;
                  const IconComponent = TOPIC_ICON[topicKey];
                  return (
                    <Pressable
                      key={userTopic?.id}
                      className="flex-row items-center justify-between w-full mb-4"
                      onPress={() => {
                        onTopicSelect(String(userTopic?.topic?._id));
                        setIsVisible(false);
                      }}
                    >
                      {/* Topic Icon */}
                      <View
                        className={`p-2 rounded-lg ${
                          userTopic?.topic?._id === selectedTopic
                            ? "border-[2px] border-[#FF8E1C]"
                            : ""
                        }`}
                      >
                        {userTopic?.topic.logo && (
                          // <Image
                          //   source={{ uri: userTopic?.topic.logo }}
                          //   style={{ width: 24, height: 24 }}
                          // />
                          <CacheImage
                            uri={imageKit({
                              imagePath:
                                _.split(
                                  userTopic?.topic.logo,
                                  constants.bucketBaseUrl
                                )[1] || "",
                              transform: ["w-500"],
                            })}
                            transitionDuration={300}
                            style={{
                              width: 24,
                              height: 24,
                            }}
                          />
                        )}
                      </View>

                      {/* Topic Details */}
                      <View className="flex-1 ml-3">
                        <Text className="text-[18px] font-medium text-[#004987] font-montserrat leading-normal">
                          {userTopic?.topic?.name}
                        </Text>
                        <Text className="text-[13px] font-medium text-[#004987] font-montserrat leading-normal">
                          {userTopic?.topic?.name === "Breast Cancer"
                            ? "Triple Negative"
                            : "Eosinophilic Asthma"}
                        </Text>
                      </View>

                      {/* Settings Button */}
                      <TouchableOpacity
                        className="p-2"
                        onPress={() => {
                          setIsVisible(false);
                          navigation.navigate("TopicsListScreen", {
                            fromConnectScreen,
                          });
                        }}
                      >
                        {/* <Cog color="rgba(0, 0, 0, 0.70)" /> */}
                        <Text className="text-[20px]">⚙️</Text>
                      </TouchableOpacity>
                    </Pressable>
                  );
                })}
                <Pressable
                  className="flex-row items-center justify-between mt-0"
                  onPress={() => {
                    setIsVisible(false);
                    navigation.navigate("OnboardingTopics", {
                      fromManageScreen: true,
                    });
                  }}
                >
                  <View
                    className={`p-5 rounded-lg h-[56px] w-[75px] border-[#B4DDFF] border`}
                  >
                    <PlusIcon color="#B4DDFF" className="ml-1 mt-[-4px]" />
                  </View>
                  <View className="flex-1 ml-3">
                    <Text className="text-[18px] text-[#004987] font-montserrat leading-normal">
                      {"New Health Topic"}
                    </Text>
                  </View>
                </Pressable>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  button: {
    backgroundColor: "#004987",
    padding: 12,
    borderRadius: 8,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  popup: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 12,
    alignItems: "center",
    position: "absolute",
    top: "21%",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  topTab: {
    position: "absolute",
    top: -12,
    left: 30,
    width: 0,
    height: 0,
    borderLeftWidth: 20,
    borderRightWidth: 20,
    borderBottomWidth: 25,
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
    borderBottomColor: "white",
  },

  text: {
    fontSize: 16,
    color: "#333",
    marginBottom: 10,
  },
  closeButton: {
    backgroundColor: "#004987",
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  closeButtonText: {
    color: "#fff",
    fontSize: 14,
  },
});

export default ManageSubscribedTopicListCard;
