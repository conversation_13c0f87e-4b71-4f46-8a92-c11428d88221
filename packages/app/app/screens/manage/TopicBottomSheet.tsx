import React from "react";
import { View } from "react-native";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import BottomSheet from "../../components/common/BottomSheet";
import { Text } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { trpc } from "../../providers/RootProvider";

interface TopicBottomSheetProps {
  bottomSheetVisible: boolean;
  hideBottomSheet: () => void;
  topicName: string | undefined;
  selectedTopic: string;
  onUnsubscribeTopic: () => void;
}

const Divider = () => {
  return <View className="h-[1px] bg-[#AFAFAF]" />;
};

const TopicBottomSheet = ({
  bottomSheetVisible,
  hideBottomSheet,
  onUnsubscribeTopic,
  topicName = "",
  selectedTopic,
}: TopicBottomSheetProps) => {
  const navigation = useNavigation();
  const unsubscribeTopic = trpc.manage.unsubscribeUserTopic.useMutation();
  return (
    <BottomSheet
      wrapperClass="p-4 px-2 bg-black/30"
      visible={bottomSheetVisible}
      handler={() => {}}
    >
      <View className="">
        <View className="bg-[#EFEFEF] rounded-xl">
          <View className="h-[56px] items-center justify-center">
            <AppText className="text-center text-[14px] text-[#8D8B8B] font-montserratMedium">
              {topicName}
            </AppText>
          </View>
          <Divider />

          <Pressable
            onPress={() => {
              navigation.navigate("TopicQuestionsList", {
                topicId: selectedTopic,
              });
              hideBottomSheet();
            }}
            className="w-full h-[56px] px-[17px] flex items-center justify-center"
          >
            <Text className="text-[17px] text-[#004987] font-montserratMedium">
              Edit
            </Text>
          </Pressable>

          <Divider />
          <Pressable
            onPress={() => {
              unsubscribeTopic
                .mutateAsync({ topic: selectedTopic })
                .then(() => {
                  hideBottomSheet();
                  onUnsubscribeTopic();
                });
            }}
            className="w-full h-[56px] px-[17px] flex items-center justify-center"
          >
            <Text className="text-[17px] text-[#FF3B30] font-montserratMedium">
              Unsubscribe
            </Text>
          </Pressable>
        </View>

        <Pressable
          onPress={() => {
            hideBottomSheet();
          }}
          className="w-full h-[56px] px-[17px] flex items-center justify-center bg-[#EFEFEF] rounded-xl mt-2 mb-5"
        >
          <Text className="text-[17px] text-[#004987] font-montserratSemiBold">
            Cancel
          </Text>
        </Pressable>
      </View>
    </BottomSheet>
  );
};

export default TopicBottomSheet;
