import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import LoadingScreen from "@components/common/LoadingScreen";
import Screen from "@components/common/Screen";
import ManageCalendarView from "@components/manage/ManageCalendarView";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import { trpc } from "@providers/RootProvider";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import clsx from "clsx";
import {
  endOfMonth,
  endOfWeek,
  format,
  startOfMonth,
  startOfWeek,
  subMonths,
} from "date-fns";
import { ChevronLeft, ListFilter } from "lucide-react-native";
import React, { useMemo, useState, useEffect } from "react";
import { Modal, Pressable, View } from "react-native";
import _ from "lodash";
import ManageCalendar2 from "@components/manage/ManageCalendar2";

const radio = ["All Topics", "Asthma"];

function ManageCalendarScreen() {
  const navigation = useNavigation<StackNavigationProp<ManageNavParams>>();
  const [bottomSheet, setBottomSheet] = useState(false);
  const [filter, setFilter] = useState("All Topics");
  const [selectedFilter, setSelectedFilter] = useState("All Topics");
  const [events, setEvents] = useState({});
  const [startDate, setStartDate] = useState(
    subMonths(startOfMonth(new Date()), 12)
  );
  const [endDate, setEndDate] = useState(endOfWeek(endOfMonth(new Date())));

  const userTopics = trpc.manage.getUserTopics.useQuery(
    {},
    {
      enabled: false,
    }
  );

  const subscribedTopics = useMemo(
    () => [
      { id: null, name: "All Topics" },
      ...(userTopics.data?.map((userTopic) => ({
        _id: userTopic.topic._id,
        name: userTopic.topic.name,
      })) || []),
    ],
    [userTopics.data]
  );

  const selectTopicId = useMemo(() => {
    const topicId = _.get(
      _.find(subscribedTopics, (topic) => topic.name === selectedFilter),
      "_id"
    );
    return topicId;
  }, [filter]);

  const getCalenderEvents = trpc.manage.getCalenderEvents.useMutation();

  useEffect(() => {
    (async () => {
      const data = await getCalenderEvents.mutateAsync({
        startDate,
        endDate,
        topicId: selectTopicId as string,
      });
      setEvents(data?.events);
    })();
  }, [filter]);

  const onDateSelect = (date: Date, topicId?: string) =>
    navigation.navigate("ManageHomeScreen", {
      startOfWeek: String(date),
      topicId:
        topicId?.toString() || userTopics?.data?.[0]?.topic?._id?.toString(),
    });

  return (
    <Screen className="bg-white pt-4">
      {/* Bottom Sheet */}
      <Modal
        visible={bottomSheet}
        onRequestClose={() => setBottomSheet(false)}
        animationType="fade"
        transparent
      >
        <View className="flex-1  bg-black/30">
          <Pressable className="flex-1" onPress={() => setBottomSheet(false)} />
          <View className=" bg-[#f5f7f9] p-4 rounded-t-xl">
            <View className="bg-[#b0c7da] w-10 h-1 self-center" />

            {/* Filter */}
            <View className="mt-6 ">
              <View style={{ gap: 18 }} className="mb-4">
                {subscribedTopics.map((r) => (
                  <Pressable
                    key={r.name}
                    className="flex-row items-center"
                    style={{ gap: 8 }}
                    onPress={() => {
                      setSelectedFilter(r?.name);
                    }}
                  >
                    <View className="border-primary border-2 rounded-full p-1">
                      <View
                        className={clsx(
                          "rounded-full w-2 h-2 bg-primary",
                          selectedFilter !== r?.name && "bg-transparent"
                        )}
                      />
                    </View>
                    <AppText className="text-primary">{r?.name}</AppText>
                  </Pressable>
                ))}
              </View>

              <View className="flex-row mb-8">
                <AppButton
                  className="rounded-lg"
                  onPress={() => {
                    setFilter(selectedFilter);
                    setBottomSheet(false);
                  }}
                >
                  Apply Filter
                </AppButton>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      <View className="flex-1 p-4">
        <View className="flex-row justify-between items-center mb-4">
          <Pressable
            className="flex-row items-center"
            onPress={() => navigation.goBack()}
          >
            <ChevronLeft color={"#004987"} size={22} className="-ml-2" />
            <AppText className="font-montserratMedium text-base text-primary">
              Home
            </AppText>
          </Pressable>
          <Pressable
            className="flex-row items-center"
            style={{ gap: 24 }}
            onPress={() => {
              setBottomSheet(true);
            }}
          >
            <AppText className="font-montserratMedium text-base text-primary">
              {filter}
            </AppText>
            <ListFilter color={"#004987"} size={22} className="-ml-2" />
          </Pressable>
        </View>

        <AppText className="text-3xl font-montserratSemiBold text-primary">
          Calendar
        </AppText>
        {/* <ManageCalendarView
          onMonthChange={({ startOfMonth, endOfMonth }) => {
            setStartDate(startOfWeek(startOfMonth));
            setEndDate(endOfWeek(endOfMonth));
          }}
          onDateSelect={onDateSelect}
          events={events}
          startDate={startDate}
          endDate={endDate}
          topicId={selectTopicId}
        /> */}
        <View className="flex-1">
          <ManageCalendar2
            onDateSelect={onDateSelect}
            events={events}
            startDate={startDate}
            endDate={endDate}
            topicId={selectTopicId as string | undefined}
          />
        </View>
      </View>
    </Screen>
  );
}

export default React.memo(ManageCalendarScreen);
