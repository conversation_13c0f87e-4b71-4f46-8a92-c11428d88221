import React, { useEffect, useState, useMemo } from "react";
import { View, Text, Image, Dimensions, Linking, Platform } from "react-native";
import AirQualityBookIcon from "@assets/svg/AirQualityBookIcon";
import { trpc } from "@providers/RootProvider";
import * as Location from "expo-location";
import _ from "lodash";
import Toast from "react-native-toast-message";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import { StackNavigationProp } from "@react-navigation/stack";
import { useNavigation } from "@react-navigation/native";
import Pressable from "@components/common/Pressable";
import InfoModal from "@components/common/InfoModal";

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.92;

const defaultLat = 38.919;
const defaultLng = -77.013;

export default function ManageAirQualityIndex() {
  const navigation = useNavigation<StackNavigationProp<ManageNavParams>>();
  const [location, setLocation] = useState({});
  const [showLocationPermissionModal, setShowLocationPermissionModal] =
    useState(false);
  const [aqi, setAqi] = useState(0);
  const [aqiLoading, setAqiLoading] = useState(false);
  const [isDenied, setIsDenied] = useState<null | boolean>(null);

  const today = useMemo(() => new Date(), []);

  // useEffect(() => {
  //   if (!showLocationPermissionModal) {
  //     Location.requestForegroundPermissionsAsync()
  //       .then(({ status }) => {
  //         setIsDenied(status === "denied");
  //       });
  //   }
  // }, [showLocationPermissionModal]);

  useEffect(() => {
    (async () => {
      const { status } = await Location.getForegroundPermissionsAsync();
      setIsDenied(
        status === "denied" ? true : status !== "granted" ? null : false
      );
      // Fetch location
      let { coords } = await Location.getCurrentPositionAsync({});
      setLocation({ lat: coords.latitude, lng: coords.longitude });
    })();
  }, []);

  const {
    data: locationIpData,
    isLoading: locationLoading,
    error: locationError,
  } = trpc.lib.locationFromIp.useQuery(
    {},
    {
      enabled: _.isEmpty(location),
    }
  );

  useEffect(() => {
    if (locationIpData) {
      setLocation({
        lat: locationIpData.latitude,
        lng: locationIpData.longitude,
      });
    }
  }, [locationIpData, showLocationPermissionModal]);

  const getAqi = trpc.lib.aqiData.useMutation();

  useEffect(() => {
    (async () => {
      try {
        if (_.size(location)) {
          setAqiLoading(true);
          const aqiData = await getAqi.mutateAsync({
            lat: location?.lat,
            lng: location?.lng,
            date: today,
          });
          setAqi(aqiData);
          setAqiLoading(false);
        }
      } catch (error) {
        setAqiLoading(false);
      }
    })();
  }, [location, locationIpData]);

  // const aqi = {}
  //  trpc.lib.aqiData.useQuery(
  //   {
  //     lat: location?.lat,
  //     lng: location?.lng,
  //     date: today,
  //   },
  //   {
  //     enabled: !!location.lat && !!location.lng,
  //     refetchOnMount: true,
  //     refetchOnWindowFocus: false,
  //     staleTime: 1000 * 60 * 5,
  //   }
  // );
  // console.log({ aqi: aqi.data });

  const handleOpenSettings = () => {
    Linking.openSettings();
    setShowLocationPermissionModal(false);
  };

  const handlePress = () => {
    if (isDenied === null) {
      Location.requestForegroundPermissionsAsync().then(async ({ status }) => {
        setIsDenied(status === "denied");
        let { coords } = await Location.getCurrentPositionAsync({});
        setLocation({ lat: coords.latitude, lng: coords.longitude });
      });
    } else if (isDenied === true) {
      setShowLocationPermissionModal(true);
    }
  };

  return (
    <Pressable onPress={handlePress}>
      <View
        className="relative m-auto mt-1 mb-4 h-[130px] bg-white rounded-lg p-8 shadow-md shadow-primary-light/10"
        style={
          Platform.OS !== "ios"
            ? {
                shadowColor: "#000000",
                shadowOffset: { width: 10, height: 10 },
                shadowOpacity: 6,
                shadowRadius: 6,
                elevation: 4,
                width: cardWidth,
                gap: 12,
              }
            : {
                shadowColor: "#000000",
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.1,
                shadowRadius: 6,
                elevation: 4,
                width: cardWidth,
                gap: 12,
              }
        }
      >
        <View
          className="flex flex-row ml-[-16px] mt-[-16px]"
          style={{ gap: 16 }}
        >
          <AirQualityBookIcon width={28} height={28} color="#FF007A" />
          <Text className="mt-0.5 ml-4 text-[#336D9F] font-montserrat text-[14px] font-bold">
            Air Quality Index (AQI)
          </Text>
        </View>

        <View className="ml-[-22px] flex-row">
          <Image
            resizeMode="contain"
            source={require("../../assets/img/plant.png")}
          />
          <Pressable className="mt-3 ml-3 ">
            <Text className="text-[#336D9F] font-montserrat text-[14px] font-normal leading-[130%]">
              AQI in your area is{" "}
              {aqiLoading ? "Loading..." : aqi?.[0]?.AQI ?? 0}
            </Text>
          </Pressable>
        </View>

        <Image
          source={{ uri: "https://via.placeholder.com/50" }}
          className="w-12 h-12 rounded-md"
        />
        <InfoModal
          visible={showLocationPermissionModal}
          label="Enable Permission"
          description="To get accurate AQI in your area please enable location permission."
          onRequestClose={setShowLocationPermissionModal}
          actionText="Yes"
          showLogo
          handleAction={handleOpenSettings}
          secondaryText="No"
        />
        {(isDenied === true || isDenied === null) && (
          <View className="absolute bottom-2 left-[65px]">
            <Pressable className="mt-3 ml-3 block" onPress={handlePress}>
              <Text className="text-[#336D9F] font-montserrat text-[14px] font-normal leading-[130%]">
                {`AQI in your area is ${
                  aqiLoading ? "Loading..." : aqi?.[0]?.AQI ?? 0
                }. Click here to allow location for precise AQI`}
              </Text>
            </Pressable>
          </View>
        )}
      </View>
    </Pressable>
  );
}
