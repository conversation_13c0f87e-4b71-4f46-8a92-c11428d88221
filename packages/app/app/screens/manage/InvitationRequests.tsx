import React from "react";
import { Modal, View, Text, TouchableOpacity, FlatList } from "react-native";
import { X } from "lucide-react-native";
import { trpc } from "@providers/RootProvider";
import _ from "lodash";
import { RabbleGroup } from "../../../../shared/types/RabbleGroup";
import { useSession } from "@hooks/persistUser";

type PendingRequest = {
  _id?: string;
  group?: string;
  invitedBy?: string;
  requestStatus: "pending" | "accepted" | "rejected";
  rabbleGroup: RabbleGroup;
  requestedBy?: string;
};

type InvitationRequestModalProps = {
  visible: boolean;
  requests: PendingRequest[];
  onAccept?: (group: string) => void;
  onReject?: (group: string) => void;
  onClose: () => void;
};

export default function InvitationRequestModal({
  visible,
  requests,
  onClose,
}: InvitationRequestModalProps) {
  const pendingRequests = requests?.filter(
    (r) => r.requestStatus === "pending"
  );
  const handleRabbleInvite = trpc.rabbleGroups.handleRabbleInvite.useMutation();

  const { user } = useSession();

  const handleAccept = async (req: any) => {
    await handleRabbleInvite.mutateAsync({
      rabbleGroup: req?.rabbleGroup?._id,
      requestedUser: user?._id.toString(),
      requestId: req?._id,
      requestStatus: "accepted",
    });
    if (_.isEmpty(pendingRequests)) {
      onClose();
    }
  };

  const handleReject = async (group: any) => {
    await handleRabbleInvite.mutateAsync({
      rabbleGroup: group?.rabbleGroup?._id,
      requestedUser: user?._id.toString(),
      requestId: req?._id,
      requestStatus: "rejected",
    });
    if (_.isEmpty(pendingRequests)) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible && pendingRequests.length > 0}
      animationType="slide"
      transparent
      onRequestClose={onClose}
    >
      <View className="flex-1 justify-center bg-black/50 px-5">
        <View className="bg-white rounded-2xl p-5">
          <Text className="text-lg font-semibold mb-3 text-gray-800">
            Pending Invitations
          </Text>
          <FlatList
            data={pendingRequests}
            // @ts-expect-error
            keyExtractor={(item) => item.group}
            renderItem={({ item }) => (
              <View className="flex-row items-center justify-between bg-gray-100 px-3 py-2 rounded-lg mb-2">
                <Text className="text-base font-medium text-gray-900 flex-1">
                  {item?.rabbleGroup?.groupName}
                </Text>

                <View className="flex-row items-center space-x-2">
                  <TouchableOpacity
                    className="bg-accent px-3 py-1 rounded-md"
                    onPress={() => handleAccept(item)}
                  >
                    <Text className="text-white text-sm font-medium">
                      Accept
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    className="bg-red-500 p-2 rounded-md"
                    onPress={() => handleReject(item.group)}
                  >
                    <X size={14} color="#fff" />
                  </TouchableOpacity>
                </View>
              </View>
            )}
          />
          <TouchableOpacity className="mt-3 self-center" onPress={onClose}>
            <Text className="text-blue-500 text-sm">Dismiss</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}
