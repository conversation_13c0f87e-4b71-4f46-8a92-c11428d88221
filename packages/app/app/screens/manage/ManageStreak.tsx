import React, { useEffect, useMemo, useState } from "react";
import { View, Text, TouchableOpacity, Dimensions } from "react-native";
import { ChevronLeft } from "lucide-react-native";
import FireIcon from "@assets/svg/FireIcon";
import CheckIcon from "@assets/svg/CheckIcon";
import AppButton from "@components/common/AppButton";
import withPressAnimation from "@components/common/AnimateButton";
import Screen from "@components/common/Screen";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import { addDays, isSameDay, startOfWeek, format } from "date-fns";
import StreakAnimation from "../../components/common/Streak";
import OnboardingLoader from "@components/onboarding/OnboardingGuruLoader";
import { ScrollView } from "react-native-gesture-handler";
import usePersistedUser, { useSession } from "@hooks/persistUser";
import useScreenTracking from "@hooks/useScreenTracking";

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.85;

const streakLabels = [
  "First Streak!",
  "Second Streak!",
  "Third Streak!",
  "Fourth Streak!",
  "Fifth Streak!",
  "Sixth Streak!",
  "Seventh Streak!",
];

const AnimatedAppButton = withPressAnimation(AppButton);

const ManageStreakScreen = () => {
  const navigation = useNavigation<StackNavigationProp<ManageNavParams>>();
  const { params } =
    useRoute<RouteProp<ManageNavParams, "ManageStreakScreen">>();
  const [isOnboardingLoader, setIsOnboardingLoader] = useState(false);
  const { setOnboardingProgressScreen } = useScreenTracking();

  const [isOnboardCheckin, setIsOnboardCheckin] = useState(false);
  const { persistedUser, setPersistedUser } = usePersistedUser();
  const { user } = useSession();

  const loginUser = async () => {
    await setOnboardingProgressScreen.mutateAsync("");

    console.log(params?.isDailyCheckInCompleted && !isOnboardCheckin)
    // @ts-expect-error
    await setPersistedUser.mutateAsync(user);

    console.log(params?.isDailyCheckInCompleted && !isOnboardCheckin)
  };

  const dailyTrackers = params?.dailyTrackers || [];

  // Get Monday as the start of the current week
  const startOfCurrentWeek = useMemo(
    () => startOfWeek(new Date(), { weekStartsOn: 0 }),
    []
  );

  // Get the 7 days of the current week (Monday → Sunday)
  const currentWeekDays = useMemo(() => {
    return Array.from({ length: 7 }, (_, i) => addDays(startOfCurrentWeek, i));
  }, [startOfCurrentWeek]);

  // Create a boolean array for tracked days
  const selectedDays = useMemo(() => {
    return currentWeekDays.map((day) =>
      dailyTrackers.some((tracker: any) =>
        isSameDay(new Date(tracker.date), day)
      )
    );
  }, [dailyTrackers, currentWeekDays]);

  useEffect(() => {
    if (isOnboardingLoader) {
      const timer = setTimeout(() => {
        setIsOnboardingLoader(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [isOnboardingLoader]);

  useEffect(() => {
    if (
      params?.isDailyCheckInCompleted &&
      !isOnboardingLoader &&
      isOnboardCheckin
    ) {
      // navigation.replace("OnboardingDailyCheckInProcessed");
      navigation.replace("OnboardingWelcomeSplashScreen");
    }
  }, [isOnboardingLoader, isOnboardCheckin]);

  const handleContinue = () => {
    if (params?.isDailyCheckInCompleted && !isOnboardCheckin) {
      loginUser()
    } else {
      const routes = navigation.getState().routeNames;
      if (routes?.includes("ManageHomeScreen")) {
        navigation.navigate("ManageHomeScreen");
      } else {
        navigation.goBack();
      }
    }
  };

  if (isOnboardingLoader && params?.isDailyCheckInCompleted) {
    return (
      <OnboardingLoader
        title="GURU OF GROWTH!"
        message="We just completed our first lesson and first daily checkin!"
      />
    );
  }

  return (
    <Screen>
      <ChevronLeft
        color={"#004987"}
        size={22}
        //@ts-ignore
        className="ml-4"
        onPress={() => navigation.goBack()}
      />
      <View className="flex-1 relative items-center justify-start bg-white p-4">
        <ScrollView>
          <View>
            <View className="items-center mb-4">
              <StreakAnimation autoPlay={true} />
              <Text className="text-[128px] font-bold text-[#FF8E1C]">
                {params?.streakCount || 0}
              </Text>
              <Text className="mt-4 mb-8 font-bold text-[#FF8E1C] text-center font-montserrat text-[28px] font-semibold leading-normal">
                {params?.streakCount && Number(params?.streakCount) > 7
                  ? streakLabels[0]
                  : streakLabels[
                      params?.streakCount ? params.streakCount - 1 : 0
                    ]}
              </Text>
            </View>

            {/* Streak Tracker */}
            <View
              className="border border-[#B4DDFF] border-b-[5px] p-4 rounded-lg -mt-4"
              style={{ width: cardWidth }}
            >
              <View className="flex-row justify-between w-[98%]">
                {selectedDays.map((selected, index) => (
                  <View key={index} className="items-center">
                    {/* Weekday Labels */}
                    <Text className="text-[#004987] mb-4 text-center font-montserrat text-[18px] font-semibold leading-normal">
                      {format(currentWeekDays[index], "EEE").slice(0, 2)}{" "}
                      {/* Mon, Tue, Wed... */}
                    </Text>
                    <TouchableOpacity
                      className={`w-6 relative h-6 border rounded-md flex items-center justify-center border-[#FF8E1C]`}
                    >
                      {selected && (
                        <View className="absolute bottom-1 left-0.5">
                          <CheckIcon />
                        </View>
                      )}
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
              <View className="m-auto mt-6 w-[75%] flex items-center">
                <Text className="text-[#004987] font-montserrat text-[18px] font-[500] leading-normal text-center">
                  Practice each day to keep{"\n"}this streak up!
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
        {/* Commit Button */}
        <View className="flex absolute bottom-4 flex-row items-center justify-between">
          <AnimatedAppButton
            btnContainer="flex flex-row"
            title="I’M COMMITTED"
            variant={"new-primary"}
            className="flex-1 rounded-full"
            textClassName="text-[21px]"
            style={{
              shadowColor: "#003366",
              shadowOffset: { width: 0, height: 3 },
              shadowOpacity: 1,
              shadowRadius: 1,
              elevation: 5,
            }}
            onPress={() => handleContinue()}
          />
        </View>
      </View>
    </Screen>
  );
};

export default ManageStreakScreen;
