import React from "react";
import { View, Text, TouchableOpacity, Dimensions } from "react-native";
import GroupIcon from "@assets/svg/GroupIcon";
import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import Pressable from "@components/common/Pressable";

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.92;

export default function NotificationBelle() {
  const navigation = useNavigation<StackNavigationProp<ManageNavParams>>();
  return (
    <Pressable
      className=" m-auto mt-2 flex-row items-center rounded-lg p-4 shadow-md shadow-primary-light/10"
      style={{ width: cardWidth }}
      onPress={() => {
        //@ts-ignore
        navigation.navigate("ConnectNavigator", { screen: "RabbleGroups" });
      }}
    >
      {/* Left Icon */}
      <View className="p-1 bg-[#D9F4FF] rounded-md self-start mt-2 -ml-1">
        <GroupIcon width={28} height={28} color="#18A0FB" />
      </View>

      {/* Text Content */}
      <View className="ml-4 flex-1">
        <Text className="text-[#336D9F] font-montserrat text-[14px] font-bold">
          Notification from Belle
        </Text>
          <Text className="text-[#336D9F] font-montserrat text-[14px] font-normal leading-[130%]">
            Did you know a rabble is a swarm of butterflies? Click here to build
            your team now!
          </Text>
      </View>

      {/* Right Icon */}
      <View className="w-[85px] mr-[-16px] h-[83px] bg-white rounded-lg shadow-md flex items-center justify-center">
        <ButterFlyIcon
          // @ts-ignore
          className="mb-2"
          width={60}
          height={100}
          color="#FFA500"
        />
      </View>
    </Pressable>
  );
}
