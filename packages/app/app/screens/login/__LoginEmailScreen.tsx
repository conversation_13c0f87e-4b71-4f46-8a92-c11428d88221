import LogoSimple from "@assets/svg/LogoOutline";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import AppFormInput from "@components/form/AppFormInput";
import { Entypo } from "@expo/vector-icons";
import { yupResolver } from "@hookform/resolvers/yup";
import usePersistedUser from "@hooks/persistUser";
import { User } from "@models/User";
import { RootNavigationProp } from "@navigation/root-navigator";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { useLoginOtp } from "@services/auth";
import errorHandler from "@utils/errorhandler";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>rovider, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import * as yup from "yup";

const schema = yup
  .object({
    email: yup
      .string()
      .email()
      .required("This is a required field")
      .label("Email Address"),
  })
  .required("This is a required field");

type FormData = yup.InferType<typeof schema>;

export default function LoginEmailScreen() {
  const navigation = useNavigation<RootNavigationProp>();
  const route = useRoute<RouteProp<RootNavParams, "LoginEmailScreen">>().params;

  useEffect(() => {
    if (route?.presentation === "modal")
      navigation.setOptions({ presentation: "modal" });
  }, [route?.presentation, navigation]);

  const { mutateAsync: requestOtp, isLoading: isUserLoading } = useLoginOtp();

  const methods = useForm<FormData>({
    defaultValues: { email: "" },
    resolver: yupResolver(schema),
    mode: "onTouched",
  });

  const { handleSubmit } = methods;

  const { setPersistedUser } = usePersistedUser();
  const onSubmit: SubmitHandler<FormData> = async function (values) {
    try {
      await requestOtp({
        phoneOrEmail: values.email,
        type: "email",
      });

      navigation.navigate("LoginOtpScreen", {
        phoneOrEmail: values.email,
        type: "email",
      });
    } catch (ex) {
      const errorMessage = errorHandler(ex);
      if (errorMessage) methods.setError("email", { message: errorMessage });
    }
  };

  const handleLoginWithMobile = function () {
    navigation.navigate("LoginMobileScreen");
  };

  const handleLoginWithSignup = () => {
    navigation.navigate("SignupScreen");
  };

  return (
    <View className="flex flex-1 px-4 pt-10">
      <AppScrollView>
        <LogoSimple className="self-center" />

        <View className="mb-6">
          <AppText className="text-2xl font-montserratSemiBold text-primary mb-4">
            welcome to myRabble!
          </AppText>

          <AppText className="text-primary-light">
            Please login into your account
          </AppText>
        </View>

        {/* Form */}
        <FormProvider {...methods}>
          <AppFormInput
            keyboardType="email-address"
            returnKeyType="next"
            autoCapitalize="none"
            name="email"
            placeholder="<EMAIL>"
            label="enter your email address"
            wrapperClass="my-4"
            importantForAutofill="yes"
            autoComplete="email"
          />
        </FormProvider>
        <View className="mt-4 flex flex-row">
          <AppButton
            isLoading={isUserLoading || setPersistedUser.isLoading}
            title="continue"
            onPress={handleSubmit(onSubmit)}
          />
        </View>

        <View className="flex-row justify-between">
          <AppText className="mt-4" onPress={handleLoginWithMobile}>
            log in with{" "}
            <AppText className="font-montserratSemiBold text-primary">
              mobile
            </AppText>
          </AppText>
          <AppText
            className="mt-4 font-montserratSemiBold text-primary"
            onPress={handleLoginWithSignup}
          >
            sign up
          </AppText>
        </View>
      </AppScrollView>
    </View>
  );
}
