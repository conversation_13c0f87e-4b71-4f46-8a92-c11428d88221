import LogoSimple from "@assets/svg/LogoOutline";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import AppFormInput from "@components/form/AppFormInput";
import { yupResolver } from "@hookform/resolvers/yup";
import { RootNavigationProp } from "@navigation/root-navigator";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import errorHandler from "@utils/errorhandler";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import * as yup from "yup";
import { trpc } from "@providers/RootProvider";
import { emailRegex } from "@utils/index";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { useSession } from "@hooks/persistUser";
import { AppNavParams } from "@navigation/app-navigator/AppNavParams";
import withPressAnimation from "@components/common/AnimateButton";

const schema = yup
  .object({
    login: yup.string().required("This is a required field"),
  })
  .required("This is a required field");

type FormData = yup.InferType<typeof schema>;

export default function LoginMobileScreen({
  navigationScreen,
}: {
  navigationScreen?: string;
}) {
  const navigation = useNavigation<StackNavigationProp<AppNavParams>>();
  const { params: pageParams } =
    useRoute<RouteProp<RootNavParams, "LoginMobileScreen">>();

  const handleSkip = () =>
    navigation.navigate(navigationScreen ?? "AppNavigator");
  const { user } = useSession();

  const methods = useForm<FormData>({
    defaultValues: { login: "" },
    resolver: yupResolver(schema),
    mode: "all",
  });

  const { handleSubmit } = methods;

  const { mutateAsync: requestOtpAsync, isLoading: isRequestOtpLoading } =
    trpc.auth.requestLoginOtp.useMutation();

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    try {
      // Request OTP using useRequestOtp hook
      await requestOtpAsync({
        ...(emailRegex.test(data.login)
          ? { email: data.login }
          : { phone: data.login }),
        isLogin: true,
        // deviceId: user?.deviceId,
      });

      navigation.navigate("LoginOtpScreen", {
        phoneOrEmail: data.login,
        type: emailRegex.test(data.login) ? "email" : "phone",
        deviceId: user?.deviceId,
      });
    } catch (ex) {
      // @ts-expect-error
      if (ex?.message && ex?.message === "user not found.") {
        methods.setError("login", {
          type: "manual",
          message:
            "This email or mobile number is not registered. Please sign up first.",
        });
      } else {
        errorHandler(ex);
      }
    }
  };

  const AnimatedAppButton = withPressAnimation(AppButton);

  return (
    <View className="flex flex-1 px-4 pt-10">
      <AppScrollView>
        <LogoSimple className="self-center" />

        <View className="mb-6">
          <AppText className="text-2xl font-montserratSemiBold text-primary mb-4">
            welcome to myRabble!
          </AppText>

          <AppText className="text-primary-light">
            Hello, my name is Belle.{"\n"}Log in for the best experience
          </AppText>
        </View>

        {/* Form */}
        <FormProvider {...methods}>
          <AppFormInput
            name="login"
            placeholder="Enter Mobile/Email To Login"
            // label="login or signup"
            wrapperClass="my-4"
            keyboardType="default"
            autoCapitalize="none"
            borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
            className="rounded-lg text-[18px]"
          />
        </FormProvider>
        <View className="mt-4  w-full">
          <AnimatedAppButton
            btnContainer="flex flex-row p-1 mb-4"
            variant="new-primary"
            className="flex-1 rounded-full"
            textClassName="text-[21px]"
            isLoading={isRequestOtpLoading}
            title="NEXT"
            onPress={handleSubmit(onSubmit)}
            style={{
              shadowColor: "#004987",
              shadowOffset: { width: 0, height: 3 },
              shadowOpacity: 1,
              shadowRadius: 1,
              elevation: 5,
            }}
          />
        </View>
        {/* <View className="flex-row self-center">
          <AppText
            className="mt-4 font-montserratSemiBold text-primary"
            onPress={handleSkip}
          >
            skip
          </AppText>
        </View> */}
      </AppScrollView>
    </View>
  );
}
