import ExclamationTriangle from "@assets/svg/ExclamationTriangle";
import AppText from "@components/common/AppText";
import _ from "lodash";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import AppFormOtpInput from "@components/form/AppFormOtpInput";
import constants, { MIXPANEL_TOKEN_V2 } from "@constants/index";
import { yupResolver } from "@hookform/resolvers/yup";
import { useInterval } from "@hooks/useInterval";
import { RootNavigationProp } from "@navigation/root-navigator";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import errorHandler from "@utils/errorhandler";
import { emailRegex, formatStringToUsPhoneNumberFormat } from "@utils/index";
import clsx from "clsx";
import { useState } from "react";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import * as yup from "yup";
import { trpc } from "@providers/RootProvider";
import usePersistedUser from "@hooks/persistUser";
import { OneSignal } from "react-native-onesignal";
import withPressAnimation from "@components/common/AnimateButton";
import mixpanel from "@utils/mixpanel";
import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import { createOrReadAppInstanceId } from "@hooks/useAppInstanceId";
import { isAxiosError } from "axios";

const schema = yup
  .object({
    otp: yup
      .string()
      .length(4)
      .required("This is a required field")
      .label("magic code"),
  })
  .required("This is a required field");

type FormData = yup.InferType<typeof schema>;

export default function LoginOtpScreen() {
  const navigation = useNavigation<RootNavigationProp>();
  const AnimatedAppButton = withPressAnimation(AppButton);

  const {
    phoneOrEmail,
    type,
    deviceId = "",
  } = useRoute<RouteProp<RootNavParams, "LoginOtpScreen">>().params;
  // otp timers
  const [otpTimer, setOtpTimer] = useState(60);
  useInterval(() => setOtpTimer((_) => _ - 1), otpTimer <= 0 ? null : 1000);

  // form methods

  const methods = useForm<FormData>({
    defaultValues: { otp: "" },
    resolver: yupResolver(schema),
  });
  const { handleSubmit } = methods;
  const { requestLoginOtp, validateLoginOtp } = trpc.auth;

  const { mutateAsync: requestOtpAsync } = requestLoginOtp.useMutation();
  const { mutateAsync: validateLoginOtpAsync, isLoading: validatingUser } =
    validateLoginOtp.useMutation();

  const resendOtp = async () => {
    try {
      setOtpTimer(60);
      await requestOtpAsync({
        ...(emailRegex.test(phoneOrEmail)
          ? { email: phoneOrEmail }
          : { phone: phoneOrEmail }),
        // deviceId: deviceId || "",
      });
    } catch (ex) {
      const { message } = errorHandler(ex);
      methods.setError("otp", { message: message });
    }
  };

  const { setPersistedUser } = usePersistedUser();
  const { mutateAsync: saveUserAsync } = setPersistedUser;

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    try {
      const user = await validateLoginOtpAsync(
        {
          otp: data.otp,
          ...(emailRegex.test(phoneOrEmail)
            ? { email: phoneOrEmail }
            : { phone: phoneOrEmail }),
          // deviceId: deviceId || "",
        },
        {
          async onSuccess(user) {
            await saveUserAsync(user);
            try {
              await mixpanel.createUserProfile({
                $distinct_id: String(user._id),
                // @ts-expect-error
                $set: _.mapValues(user, String),
                $token: MIXPANEL_TOKEN_V2 || "",
              });
            } catch (error) {
              console.log("error catch User logged in", error);
            }
            await mixpanel.trackEvent(
              "User logged in",
              {
                email: phoneOrEmail,
                phone: user?.contact?.phone || "",
              },
              String(user?._id),
              "v2"
            );

            const appInstanceId = await createOrReadAppInstanceId();
            try {
              await mixpanel.mergeIdentities(appInstanceId, String(user._id));
              console.log("merged identities", appInstanceId, user._id);
            } catch (ex) {
              if (isAxiosError(ex)) console.error("fffs,", ex.response?.data);
            }
            const hasPermission =
              await OneSignal.Notifications.getPermissionAsync();
            if (!hasPermission) return;

            OneSignal.login(String(user._id));
            OneSignal.User.setLanguage("en");

            return navigation.navigate("SignupScreen");
          },
        }
      );
    } catch (ex) {
      const { message } = errorHandler(ex);
      methods.setError("otp", { message: message });
      // methods.setError("otp", { message: error });
    }
  };

  return (
    <View className="flex flex-1 px-4">
      <AppScrollView>
        <View className="lg:w-[600px] lg:p-8 lg:shadow-sm lg:bg-white lg:self-center lg:rounded-md lg:mt-20">
          <View className="mt-4 flex-row items-center">
            {/* <ExclamationTriangle /> */}
            {/* <AppText className="flex-1 mx-2 text-black font-montserratRegular">
              {`You’ll receive a magic code on your ${
                type === "email" ? "email" : "phone"
              }`}
            </AppText> */}
          </View>
          {/* <AppText className="text-primary font-montserratSemiBold my-6">
            please enter magic code sent to {"\n"}
            {type === "email"
              ? phoneOrEmail
              : `${constants.countryCode} ${formatStringToUsPhoneNumberFormat(
                  phoneOrEmail
                )}`}
          </AppText> */}

          <View className="flex-row items-center">
            <ButterFlyIcon height={140} width={90} />
            <QuestionPrompt message="Great! Please enter your magic code." />
          </View>

          {/* Form */}
          <FormProvider {...methods}>
            <AppFormOtpInput
              name="otp"
              customClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
            />

            <AppText
              className={clsx(
                "text-[#004987]",
                otpTimer <= 0 && "underline text-[#004987] font-montserratBold"
              )}
              onPress={!otpTimer ? resendOtp : undefined}
            >
              resend code {otpTimer > 0 ? " in" : ""}
              {!!otpTimer && (
                <AppText className="text-primary font-montserratMedium">
                  {" "}
                  {otpTimer}s{" "}
                </AppText>
              )}
            </AppText>

            <View className="flex flex-row mt-6 mb-5 ">
              <View className="w-full">
                <AnimatedAppButton
                  btnContainer="flex flex-row p-1 "
                  variant="new-primary"
                  className="flex-1 rounded-full"
                  isLoading={validatingUser}
                  title="NEXT"
                  onPress={handleSubmit(onSubmit)}
                  textClassName="text-[21px]"
                  style={{
                    shadowColor: "#004987",
                    shadowOffset: { width: 0, height: 3 },
                    shadowOpacity: 1,
                    shadowRadius: 1,
                    elevation: 5,
                  }}
                />
              </View>
            </View>
          </FormProvider>
        </View>
      </AppScrollView>
    </View>
  );
}
