import Logo from "@assets/svg/Logo";
import W4 from "@assets/svg/W4";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import Screen from "@components/common/Screen";
import { RootNavigationProp } from "@navigation/root-navigator";
import { CommonActions, useNavigation } from "@react-navigation/native";
import { StyleSheet, View } from "react-native";
import withPressAnimation from "@components/common/AnimateButton";

export default function LoginSplashScreen() {
  const navigation = useNavigation<RootNavigationProp>();

  const handleSkip = () => navigation.navigate("AppNavigator");
  const handleLogin = () => navigation.navigate("LoginMobileScreen");

  const AnimatedAppButton = withPressAnimation(AppButton);

  return (
    <Screen className="items-center m-4">
      <View style={styles.logo}>
        <Logo />
      </View>
      <W4 />
      {/* Content */}
      <View className="flex flex-1 w-full justify-center items-center">
        <AppText className="text-primary-dark text-2xl font-montserratSemiBold text-center">
          engage and support others
        </AppText>
        <AppText className="text-center text-primary text-base mt-4">
          your data, your choice. option to share your experience and
          participate in sponsored surveys
        </AppText>
      </View>
      {/* Bottom Section : buttons and login  */}

      <View className="mb-4 mt-8 w-full">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1 mb-4"
          title="LOGIN/SIGNUP"
          variant="new-primary"
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          onPress={handleLogin}
          style={{
            shadowColor: "#004987",
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 1,
            shadowRadius: 1,
            elevation: 5,
          }}
        />
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  logo: {
    transform: [{ scale: 0.45 }],
    alignSelf: "center",
  },
});
