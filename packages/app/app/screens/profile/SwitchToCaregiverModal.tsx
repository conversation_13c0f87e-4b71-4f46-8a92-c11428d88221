import { Modal, View } from "react-native";
import { DownloadIcon } from "lucide-react-native";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";

type Props = {
    visible: boolean;
    handleChange: any;
    handleRevert: any;
};

export default function SwitchToCareGiverModal({ visible, handleChange, handleRevert }: Props) {

  return (
    <Modal transparent visible={visible}>
      <View className="flex flex-1 bg-neutral-200/50 justify-center items-center">
        <View className="w-[80%] bg-white rounded-3xl p-7">
          <View className="flex-row items-center">
            <AppText className="font-montserrat-semibold text-lg tracking-widest items-center">
                Do you you want to change from a Patient to a Caregiver? This action cannot be undone
            </AppText>
            {/* <DownloadIcon color="gold" className="" /> */}
          </View>

          <View className="flex-row">
            <AppButton
              title="Cancel"
              style={{ marginRight: 10 }}
              onPress={handleRevert}
              className="mt-6 bg-accent-500"
            />
            <AppButton
              title="Change"
              onPress={handleChange}
              className="mt-6 bg-amber-500"
            />
          </View>
        </View>
      </View>
    </Modal>
  );
}
