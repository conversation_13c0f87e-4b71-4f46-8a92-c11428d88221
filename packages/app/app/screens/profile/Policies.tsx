import SectionLink from "@components/profile/ProfileSectionItem";
import { useSession } from "@hooks/persistUser";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import mixpanel from "@utils/mixpanel";
import { View } from "react-native";

interface ContactMethodProps {
  onPress?: () => void;
  name: string;
  value: string;
  icon: JSX.Element;
  wrapperClass?: string;
}

export default function Policies() {
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
    const { user } = useSession();
  

  const handlePrivacyPolicy = () => {
    mixpanel.trackEvent(
      "Privacy detail page view",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        screen_name: "PrivacyPolicy"
      },
      String(user?._id),
      "v2"
    );
    navigation.navigate("PrivacyPolicy");
  };
  const handleTC = () => navigation.navigate("TermsConditions");
  const handleCG = () => navigation.navigate("CommunityGuidelines");

  return (
    <View className="flex flex-1 py-4 px-2">
      <SectionLink onPress={handlePrivacyPolicy} title="Privacy Policy" />
      <SectionLink onPress={handleTC} title="Terms & Conditions" />
      <SectionLink onPress={handleCG} title="Community Guidelines" />
    </View>
  );
}
