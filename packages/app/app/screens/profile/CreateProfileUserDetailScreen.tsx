import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useState } from "react";
import { Dimensions, View, Text, KeyboardAvoidingView, Platform } from "react-native";
import AppButton from "@components/common/AppButton";
import BookIcon from "@assets/svg/BookIcon";
import CompleteDailyCheckInIcon from "@assets/svg/CompleteDailyCheckInIcon";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import AppTextInput from "@components/common/AppTextInput";
import AppFormInput from "@components/form/AppFormInput";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import errorHandler from "@utils/errorhandler";
import { zodResolver } from "@hookform/resolvers/zod";
import AppFormCheckbox from "@components/form/AppFormCheckbox";
import AppText from "@components/common/AppText";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import AppScrollView from "@components/common/AppScrollView";
import mixpanel from "@utils/mixpanel";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const schema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  userName: z.string().min(1, "User name is required"),
});

type Form = z.infer<typeof schema>;

export default function CreateProfileUserDetailScreen() {
  const navigation = useNavigation<RootNavigationProp>();
  const { user } = useSession();

  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: "",
      lastName: "",
      userName: "",
    },
  });

  const { mutateAsync: updateUser, isLoading: isRequestOtpLoading } =
    trpc.auth.requestLoginOtp.useMutation();
  const updateUserPartial = trpc.user.updateUserPartial.useMutation();
  const { handleSubmit } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      console.log({ data });
      // mixpanel.trackEvent(
      //   "ONBOARDING_ACTION",
      //   {
      //     action: "Preferred names selected (Step 12) (Account Creation)",
      //     first_name: data?.firstName,
      //     last_name: data?.lastName,
      //     user_name: data?.userName,
      //   },
      //   "",
      //   "v2"
      // );
      const res = await updateUserPartial.mutateAsync({
        firstname: data?.firstName,
        lastname: data?.lastName,
        username: data?.userName,
        userOrPatientId: user?._id?.toString() as string,
      });
      mixpanel.trackEvent(
        "Name added (Step 2)(Account Creation)",
        {
          first_name: data?.firstName,
          last_name: data?.lastName,
        },
        user?._id?.toString(),
        "v2"
      );

      navigation.navigate("CreateProfileBirthdayScreen");
    } catch (ex) {
      errorHandler(ex);
    }
  });

  return (
    <Screen>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
      >
        <AppScrollView>
          <View className="flex-row items-center mt-8">
            <ChevronLeft
              color={"#004987"}
              size={22}
              // @ts-expect-error
              className="ml-4"
              onPress={() => navigation.goBack()}
            />
            <ProgressBar
              total={3}
              current={1}
              style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
            />
          </View>
          <View className="flex-1 items-center mt-[-12px]">
            <View className="flex-row items-center">
              <ButterFlyIcon height={140} width={90} />
              <QuestionPrompt message="What is your preferred name?" />
            </View>

            <View
              className="flex-1 p-2 mt-[-12px]"
              style={{ width: cardWidth, gap: 8 }}
            >
              <FormProvider {...methods}>
                <AppFormInput
                  name="firstName"
                  fieldClass=""
                  placeholderTextColor="#B4DDFF"
                  placeholder="First name"
                  borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
                  className="rounded-lg text-[18px]"
                />
                <AppFormInput
                  name="lastName"
                  fieldClass=""
                  placeholder="Last name"
                  placeholderTextColor="#B4DDFF"
                  borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
                  className="rounded-lg text-[18px]"
                />
                <AppFormInput
                  name="userName"
                  fieldClass=""
                  placeholder="UserName"
                  placeholderTextColor="#B4DDFF"
                  borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
                  className="rounded-lg text-[18px]"
                />
              </FormProvider>
            </View>
          </View>
        </AppScrollView>
        <View className="flex flex-row items-center justify-between mb-2 mx-4">
          <AnimatedAppButton
            btnContainer="flex flex-row p-1"
            title="CONTINUE"
            variant={"new-primary"}
            className="flex-1 rounded-full"
            textClassName="text-[21px]"
            style={{
              shadowColor: "#003366",
              shadowOffset: { width: 0, height: 3 },
              shadowOpacity: 1,
              shadowRadius: 1,
              elevation: 5,
            }}
            onPress={onSubmit}
          />
        </View>
      </KeyboardAvoidingView>
    </Screen>
  );
}
