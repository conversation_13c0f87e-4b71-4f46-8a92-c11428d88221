import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useState } from "react";
import {
  Dimensions,
  View,
  Text,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import AppButton from "@components/common/AppButton";
import BookIcon from "@assets/svg/BookIcon";
import CompleteDailyCheckInIcon from "@assets/svg/CompleteDailyCheckInIcon";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import AppTextInput from "@components/common/AppTextInput";
import AppFormInput from "@components/form/AppFormInput";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import errorHandler from "@utils/errorhandler";
import { zodResolver } from "@hookform/resolvers/zod";
import AppFormCheckbox from "@components/form/AppFormCheckbox";
import AppText from "@components/common/AppText";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import AppScrollView from "@components/common/AppScrollView";
import mixpanel from "@utils/mixpanel";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;
const currentYear = new Date().getFullYear();
const minYear = currentYear - 12;
const schema = z.object({
  month: z
    .string()
    .min(1, "Month is required")
    .regex(/^(0[1-9]|1[0-2])$/, "Month must be in MM format (01-12)"), // Validates month as MM format
  year: z
    .string()
    .min(1, "Year is required")
    .regex(/^\d{4}$/, "Enter a valid 4-digit year (YYYY).")
    .refine((val) => {
      const year = parseInt(val);
      return year >= 1900;
    }, "Year must be 1900 or later.")
    .refine((val) => {
      const year = parseInt(val);
      return year <= minYear; // minYear = currentYear - 12
    }, "You must be at least 12 years old."),
  termsAccepted: z.boolean().refine((value) => value === true, {
    message: "Please agree to our terms and conditions to proceed.",
  }),
  marketingConsentAccepted: z.boolean({
    required_error: "This is a required field",
  }),
});

type Form = z.infer<typeof schema>;

export default function CreateProfileBirthdayScreen() {
  const navigation = useNavigation<RootNavigationProp>();
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const { user } = useSession();
  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      month: "",
      year: "",
      termsAccepted: false,
      marketingConsentAccepted: false,
    },
  });

  const handleTC = () => navigation.navigate("TermsConditions");
  const handlePP = () => navigation.navigate("PrivacyPolicy");

  const { mutateAsync: updateUser, isLoading: isRequestOtpLoading } =
    trpc.auth.requestLoginOtp.useMutation();
  const updateUserPartial = trpc.user.updateUserPartial.useMutation();
  const updateUserProfile =
    trpc.user.createUpdateUserOrPatientProfile.useMutation();
  const { handleSubmit } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      console.log({ data });
      // mixpanel.trackEvent(
      //   "ONBOARDING_ACTION",
      //   {
      //     action:
      //       "Create profile selectedMonth and year selected (Step 11) (Account Creation)",
      //     month: data?.month,
      //     year: data?.year,
      //   },
      //   "",
      //   "v2"
      // );
      const res = await updateUserPartial.mutateAsync({
        month: data?.month,
        year: data?.year,
        tncConsent: true,
        privacyConsent: true,
        marketingConsent: data?.marketingConsentAccepted,
        userOrPatientId: user?._id?.toString() as string,
      });
      await mixpanel.trackEvent(
        "Date of birth added (Step 3)(Account Creation)",
        {
          date_of_birth: data?.month,
          year: data?.year,
        },
        user?._id?.toString(),
        "v2"
      );
      // await mixpanel.trackEvent(
      //   "User signed up",
      //   {
      //     email: user?.email || "",
      //     phone: user?.contact?.phone || "",
      //     opt_in_tracking: data?.marketingConsentAccepted ||  false,
      //   },
      //   user?._id?.toString(),
      //   "v2"
      // );
      navigation.navigate("SignupScreenV1");
    } catch (ex) {
      errorHandler(ex);
    }
  });

  return (
    <Screen>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
      >
        <AppScrollView>
          <View className="flex-row items-center mt-8">
            <ChevronLeft
              color={"#004987"}
              size={22}
              className="ml-4"
              onPress={() => navigation.goBack()}
            />
            <ProgressBar
              total={3}
              current={1}
              style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
            />
          </View>
          <View className="flex-1 items-center mt-[-8px]">
            <View className="flex-row items-center mb-[-16px]">
              <ButterFlyIcon height={140} width={90} />
              <QuestionPrompt
                dx={8}
                message="Let’s align to your privacy preferences."
              />
            </View>
            <View className="flex-1 p-2" style={{ width: cardWidth }}>
              <Text className="mb-[-2px] text-[#004987] font-montserrat text-[18px] font-medium leading-none">
                When is your birthday?
              </Text>
              <FormProvider {...methods}>
                <AppFormInput
                  name="month"
                  fieldClass=""
                  placeholderTextColor="#B4DDFF"
                  placeholder="Month (MM)"
                  borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
                  className="rounded-lg text-[18px]"
                />
                <AppFormInput
                  name="year"
                  placeholder="Year (YYYY)"
                  placeholderTextColor="#B4DDFF"
                  borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
                  className="rounded-lg text-[18px]"
                />

                <View
                  className="flex-1 justify-between"
                  style={{ minHeight: 400 }}
                >
                  <View className="mt-16">
                    <AppText
                      className="text-[#004987] mb-2 mt-6"
                      style={{
                        fontFamily: "Montserrat",
                        fontSize: 18,
                        fontWeight: "500",
                        lineHeight: 24,
                      }}
                    >
                      Your experience can help others, and it's your choice,
                      always.
                    </AppText>

                    <AppFormCheckbox
                      name="termsAccepted"
                      large
                      label={
                        <AppText
                          className="text-[#004987]"
                          style={{
                            fontFamily: "Montserrat",
                            fontSize: 18,
                            fontWeight: "300",
                            lineHeight: 24,
                          }}
                        >
                          by creating a Rabble Health account, I agree to the
                          Rabble{" "}
                          <AppText
                            className="text-[#004987] underline"
                            onPress={handleTC}
                            style={{
                              fontFamily: "Montserrat",
                              fontSize: 18,
                              fontWeight: "300",
                            }}
                          >
                            Terms and Conditions
                          </AppText>{" "}
                          {" & "}
                          <AppText
                            className="text-[#004987] underline"
                            onPress={handlePP}
                            style={{
                              fontFamily: "Montserrat",
                              fontSize: 18,
                              fontWeight: "300",
                            }}
                          >
                            Privacy Policy
                          </AppText>
                        </AppText>
                      }
                    />
                    <View className="mt-8">
                      <AppFormCheckbox
                        name="marketingConsentAccepted"
                        large
                        label={
                          <AppText
                            className="text-[#004987]"
                            style={{
                              fontFamily: "Montserrat",
                              fontSize: 18,
                              fontWeight: "300",
                              lineHeight: 24,
                            }}
                          >
                            I prefer to have my de-identified data used for
                            research purposes or app enhancements.
                          </AppText>
                        }
                      />
                    </View>
                  </View>
                </View>
              </FormProvider>
            </View>
          </View>
        </AppScrollView>
        <View className="flex flex-row items-center justify-between mx-6">
          <AnimatedAppButton
            btnContainer="flex flex-row p-0 my-6"
            title="CONTINUE"
            variant={"new-primary"}
            className="flex-1 rounded-full"
            textClassName="text-[21px]"
            style={{
              shadowColor: "#003366",
              shadowOffset: { width: 0, height: 5 },
              shadowOpacity: 1,
              shadowRadius: 1,
              elevation: 5,
            }}
            onPress={onSubmit}
          />
        </View>
      </KeyboardAvoidingView>
    </Screen>
  );
}
