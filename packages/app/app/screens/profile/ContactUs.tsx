import Email from '@assets/svg/profile/Email';
import { useEffect } from 'react';
import Phone from '@assets/svg/profile/Phone';
import AppText from '@components/common/AppText';
import Pressable from '@components/common/Pressable';
import { useSession } from '@hooks/persistUser';
import mixpanel from '@utils/mixpanel';
import { useCallback } from 'react';
import { Linking, View } from 'react-native';

const mail = '<EMAIL>';
const phone = '18143008898';

interface ContactMethodProps {
  onPress?: () => void;
  name: string;
  value: string;
  icon: JSX.Element;
  wrapperClass?: string;
}

const ContactMethod = function ({
  name,
  value,
  onPress,
  icon,
  wrapperClass,
}: ContactMethodProps) {
  return (
    <Pressable
      actionTag='contact btn'
      className={wrapperClass}
      onPress={onPress}
    >
      <AppText className='text-sm  font-montserratMedium text-black mb-3 flex flex-row items-center'>
        {name}
      </AppText>
      <View className='flex flex-row items-center pl-1'>
        {icon}
        <AppText className='text-sm font-montserratMedium pl-2 items-center justify-center'>
          {value}
        </AppText>
      </View>
    </Pressable>
  );
};

export const openLinkingUrl = async (prefix: string, url: string, user?: any) => {
  mixpanel.trackEvent(
    `${prefix === 'tel:' ? 'Call number' : 'Email'} clicked (Contact Us)`,
    {
      email: user?.email || "",
      phone: user?.contact?.phone || "",
      ...(prefix === 'tel:' && {call_number: phone}),
      ...(prefix !== 'tel:' && {contact_email: mail})
    },
    String(user?._id),
    "v2"
  );
  const canOpenUrl = await Linking.canOpenURL(prefix + url);
  if (canOpenUrl) Linking.openURL(prefix + url);
};

export default function ContactUs() {
const { user } = useSession();

  useEffect(() => {
    mixpanel.trackEvent(
      "Contact us screen view",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
      },
      String(user?._id),
      "v2"
    );
  }, [])

  return (
    <View className='flex flex-1 p-4'>
      <View className='flex flex-1'>
        <ContactMethod
          name='call us on'
          value={phone}
          icon={<Phone style={{ transform: [{ scale: 1.7 }] }} />}
          wrapperClass='mb-8'
          onPress={() => openLinkingUrl('tel:', phone, user)}
        />
        <ContactMethod
          name='Write email to'
          value='<EMAIL>'
          icon={<Email style={{ transform: [{ scale: 1.7 }] }} />}
          onPress={() => openLinkingUrl('mailto:', mail, user)}
        />
      </View>
      <View className='flex mx-10 gap-y-1 mb-10'>
        <AppText className='text-center text-xs text-neutral-500'>
          myRabble will never sell your personal data
        </AppText>

        <AppText>
          <AppText
            className='text-primary-dark text-xs font-montserratMedium underline'
            onPress={() => openLinkingUrl('mailto:', mail)}
          >
            click here
          </AppText>
          <AppText className='text-center text-xs text-black font-montserratMedium'>
            {' '}
            if you still want to delete your data
          </AppText>
        </AppText>
      </View>
    </View>
  );
}
