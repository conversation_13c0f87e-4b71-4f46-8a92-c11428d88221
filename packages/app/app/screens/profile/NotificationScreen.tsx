import { View } from "react-native";
import { trpc } from "@providers/RootProvider";
import React, { useEffect } from "react";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import { useCallback, useMemo, useState } from "react";

import { NotificationTypes } from "../../../../shared/enums/notification";
import CareGiverApprovalNotification from "@components/notifications/CareGiverApprovalNotification";
import DefaultNotificationView from "@components/notifications/DefaultNotificationView";
import RabbleGroupJoinNotification from "@components/notifications/RabbleGroupJoinNotification";
import { FlatList } from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import LoadingScreen from "@components/common/LoadingScreen";
import { useAppProvider } from "../../providers/AppProvider";

export default function NotificationsScreen(props: any) {
  const {
    data: notifications,
    isLoading,
    refetch: refetchNotifications,
  } = trpc.notification.getNotifications.useQuery();

  const markAsReadNotification =
    trpc.notification.markAsReadNotification.useMutation();

  const { refetchNotificationCount } = useAppProvider();

  useFocusEffect(
    useCallback(() => {
      refetchNotifications();
    }, [])
  );

  useEffect(() => {
    (async () => {
      await markAsReadNotification.mutateAsync();
      refetchNotificationCount();
    })();
  }, []);

  function refetchNotif() {
    refetchNotifications();
    refetchNotificationCount();
  }

  if (isLoading) return <LoadingScreen />;

  return (
    <FlatList
      data={notifications}
      keyExtractor={(item) => item._id.toString()}
      renderItem={({ item: notification }) => {
        switch (notification.notificationType) {
          case NotificationTypes.CAREGIVER_APPROVAL:
            return (
              <CareGiverApprovalNotification
                notification={notification}
                refetchNotifications={refetchNotif}
              />
            );
          case NotificationTypes.RABBLEGROUP_JOIN_REQUEST:
            return (
              <RabbleGroupJoinNotification
                notification={notification}
                refetchNotifications={refetchNotif}
              />
            );
          default:
            return <DefaultNotificationView />;
        }
      }}
    />
  );
}
