import Contactus from "@assets/svg/profile/Contactus";
import ActivityIcon from "@assets/svg/profile/ActivityIcon";
import AppScrollView from "@components/common/AppScrollView";
import AppText from "@components/common/AppText";
import Divider from "@components/common/Divider";
import { useEffect } from "react";
import Screen from "@components/common/Screen";
import LoginCard from "@components/profile/LoginCard";
import Logout from "@components/profile/Logout";
import ProfileDetailsCard from "@components/profile/ProfileDetailsCard";
import SectionLink from "@components/profile/ProfileSectionItem";
import Shield from "@components/profile/Shield";
import { ENV } from "@constants/index";
import usePersistedUser, { useSession } from "@hooks/persistUser";
import useAuthGuard from "@hooks/useAuthGuard";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { queryClient, trpc, useRedirectContext } from "@providers/RootProvider";
import {
  CompositeNavigationProp,
  useFocusEffect,
  useNavigation,
} from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import Constants from "expo-constants";
import { useCallback, useMemo } from "react";
import { View } from "react-native";
import { CaregiverApprovalStatus } from "../../../../shared/enums/user";
import { UserAccount } from "../../../../shared/types/user";
import AsyncStorage from "@react-native-async-storage/async-storage";
import mixpanel from "@utils/mixpanel";
import { createAppInstanceId } from "@hooks/useAppInstanceId";

export default function Profile() {
  let easterEggCounter = 0;
  // will help prefill data

  const [, setRedirect] = useRedirectContext();

  const navigation =
    useNavigation<
      CompositeNavigationProp<
        StackNavigationProp<RootNavParams>,
        StackNavigationProp<ProfileNavParams>
      >
    >();

  const { user, refetch } = useSession();
  const { removePersistedUser } = usePersistedUser();
  const authGuard = useAuthGuard();
  const patients = trpc.user.getCareGiverPatients.useQuery();

  const managedPatients = useMemo(
    () =>
      patients.data?.filter((p) => {
        const managed = p.managedBy;

        return managed?.find(
          (_: any) =>
            _.user === user?._id &&
            _.approvalStatus === CaregiverApprovalStatus.APPROVED
        );
      }),
    [patients]
  );

  const isCaregiver = useMemo(() => user?.isCaregiver, [user]);

  const patientCaregivers = useMemo(() => {
    if (!isCaregiver) {
      return user?.managedBy
        ?.filter(
          (_) =>
            _.approvalStatus === CaregiverApprovalStatus.APPROVED && !!_.user
        )
        .map((_) => _.user);
    }
    return [];
  }, [isCaregiver, user]) as UserAccount[];

  //TODO : will fix later
  useFocusEffect(
    useCallback(() => {
      patients.refetch();
      refetch();
    }, [])
  );

  useEffect(() => {
    mixpanel.trackEvent(
      "Profile screen view",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
      },
      String(user?._id),
      "v2"
    );
  }, []);

  const handleContactNavigation = () => navigation.navigate("ContactScreen");
  const handlePolicyNavigation = async () => {
    await mixpanel.trackEvent(
      "Policies screen view",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
      },
      String(user?._id),
      "v2"
    );
    navigation.navigate("PolicyScreen");
  };
  const logout = async () => {
    await mixpanel.trackEvent(
      "User logged out",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
      },
      String(user?._id),
      "v2"
    );
    await AsyncStorage.removeItem("selectedTopic");
    await createAppInstanceId(); // Reset app instance ID on logout
    setRedirect("LoginMobileScreen");
    removePersistedUser.mutateAsync();
  };

  const handleActivityNavigation = () => {
    navigation.navigate("ActivityScreen");
  };

  return (
    <Screen includeHeader className="mx-4 mt-6">
      <AppScrollView>
        <>
          {user ? (
            <>
              <ProfileDetailsCard
                // flatBottomBorders={isCaregiver}
                className="flex justify-flex-start items-flex-start my-6"
              />
              {/* <PendingApprovalOrRejectedPatients
                userId={user._id?.toString()}
                pendingApprovalOrRejectPatients={patients?.data?.filter((p) => {
                  const managed = p.managedBy;
                  return managed?.find(
                    (_) =>
                      _.user === user?._id &&
                      _.approvalStatus !== CaregiverApprovalStatus.APPROVED
                  );
                })}
              /> */}
              {/* {!!isCaregiver && (
                <AddNewPatient patientsExists={patients.data?.length} />
              )} */}
            </>
          ) : (
            <LoginCard className="my-6" />
          )}
          {user && (
            <>
              {/* {!!managedPatients?.length && (
                <ManagePatient patients={managedPatients} />
              )} */}
              {/* {!!patientCaregivers?.length && (
                <PatientCaregivers caregivers={patientCaregivers} />
              )} */}
            </>
          )}
          {/* try from SLICE */}

          <View className="gap-y-4 flex flex-1">
            {/* <Divider /> */}
            {/* <Rabbles /> */}
            <Divider />
            <SectionLink
              title="contact us"
              icon={<Contactus />}
              onPress={handleContactNavigation}
            />
            {/* <SectionLink
              title="activities"
              icon={<ActivityIcon />}
              onPress={handleActivityNavigation}
            /> */}
            <SectionLink
              onPress={handlePolicyNavigation}
              title="policies"
              icon={<Shield />}
            />
            {/* <ProfileSectionItem title="Settings" icon={<Gear />} /> */}
            {user && (
              <SectionLink title="logout" icon={<Logout />} onPress={logout} />
            )}
          </View>
          <AppText
            className="text-center text-xs mb-1 font-montserratMedium text-neutral-800 mt-10"
            onPress={() => {
              ++easterEggCounter % 7 == 0 &&
                alert(JSON.stringify(ENV, null, 4));
            }}
          >
            App Version {Constants.expoConfig?.version}
          </AppText>
        </>
      </AppScrollView>
    </Screen>
  );
}
