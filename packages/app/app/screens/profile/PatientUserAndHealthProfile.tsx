import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import Screen from "@components/common/Screen";
import { zodResolver } from "@hookform/resolvers/zod";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import {
  CommonActions,
  RouteProp,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { View, Image } from "react-native";
import {
  createUpdateUserOrPatientHealthProfileValidator,
  updateUserFullProfileValidator,
} from "../../../../shared/validators/user.validator";
import { trpc } from "@providers/RootProvider";
import { z } from "zod";
import AppFormSelect from "@components/form/AppFormSelect";
import LoadingScreen from "@components/common/LoadingScreen";
import { Option } from "@components/common/AppSelect";
import { useEffect, useMemo } from "react";
import _, { method, update } from "lodash";
import EmailPhoneUpdate from "app/elements/profile/EmailPhoneUpdate";
import LogoSimple from "@assets/svg/LogoOutline";
import AppFormInput from "@components/form/AppFormInput";
import AppFormDatePicker from "@components/form/AppFormDatePicker";
import Toast from "react-native-toast-message";
import errorHandler from "@utils/errorhandler";
import { s3Paths } from "@constants/index";
import * as ImagePicker from "expo-image-picker";
import { useUploadImage } from "@services/upload";
import { imageKit } from "@utils/index";
import AppText from "@components/common/AppText";
import {
  ApplierKey,
  DiagnoseStageKey,
  Disease,
  GENDER,
  RACE,
} from "../../../../shared/types/user";
import { useSession } from "@hooks/persistUser";
import Pressable from "@components/common/Pressable";
import { PlusCircle } from "lucide-react-native";

const minAge = 12;
const minDate = new Date();
minDate.setFullYear(minDate.getFullYear() - minAge);

type FormData = z.infer<typeof updateUserFullProfileValidator>;

export default function PatientUserHealthProfile() {
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();

  const { userOrPatientId } =
    useRoute<RouteProp<ProfileNavParams, "PatientUserHealthProfile">>().params;

  const { user } = useSession();

  const userProfile = trpc.user.meOrPatientProfile.useQuery(userOrPatientId);

  const createUpdateUserHealthProfile =
    trpc.user.createUpdateUserOrPatientHealthProfile.useMutation();

  const utils = trpc.useUtils();
  const healthProfileOptions = trpc.lib.healthProfileOptions.useQuery();

  const {
    data: me,
    status: meStatus,
    refetch: meRefetch,
  } = trpc.user.me.useQuery(userOrPatientId);

  const methods = useForm<FormData>({
    resolver: zodResolver(updateUserFullProfileValidator),
    mode: "all",
    defaultValues: {
      userOrPatientId,
      healthProfile: {
        cancerJourneyStage: "",
        diagnosis: "",
        diagnosisSubType: "",
        personalizedHelp: undefined,
        additionalApplier: [],
        diagnosisStage: "",
        diagnosisDate: "",
      },
      personalProfile: {
        personal: {
          race: undefined,
          dob: undefined,
          gender: undefined,
        },
        address: {
          city: "",
          state: "",
          zip: "",
        },
      },
      userProfile: {
        username: "",
        firstname: "",
        lastname: "",
        email: "",
        contact: undefined,
      },
    },
  });

  useEffect(() => {
    if (userProfile?.status === "success" && meStatus === "success") {
      // @ts-ignore
    }
  }, [userProfile?.status, meStatus, userProfile.data]);

  const patientData = trpc.user.getUserOrPatientHealthProfile.useQuery(
    { userOrPatientId },
    {
      enabled: userProfile.status === "success",
      onSuccess(data) {
        methods.reset({
          userOrPatientId,
          healthProfile: {
            ..._.omit(data, "_id"),
          },
          userProfile: {
            ...me,
          },
          // @ts-ignore
          personalProfile: {
            ...userProfile?.data,
          },
        });
      },
    }
  );

  const cancerJourney = methods.watch("healthProfile.cancerJourneyStage");
  if (
    typeof cancerJourney === "string" &&
    cancerJourney === "supporting a loved one navigating cancer"
  ) {
    navigation.replace("Caregiver");
  }

  const disease = useMemo(() => {
    const watchedDisease = methods.watch("healthProfile.disease");
    return _.findKey(
      healthProfileOptions.data?.diseaseTypes,
      (v) => v === watchedDisease
    );
  }, [methods.watch("healthProfile.disease")]) as Disease | undefined;

  const diagnosisTypes = useMemo(
    () => (disease && healthProfileOptions.data?.diagnosisTypes[disease]) || [],
    [disease]
  );

  const selectedCancer = useMemo(() => {
    const watchedDiagnosis = methods.watch("healthProfile.diagnosis");
    if (typeof watchedDiagnosis === "string") {
      return diagnosisTypes.find((_) => _.name === watchedDiagnosis);
    } else {
      return undefined; // or handle the case where watchedCancer is not a string
    }
  }, [methods.watch("healthProfile.diagnosis"), diagnosisTypes]);

  const additionalApplier = useMemo(
    () =>
      _.keys(healthProfileOptions.data?.additionalDiagonsisAppliers).find(
        (key) => {
          return methods.watch("healthProfile.diagnosisSubType")?.includes(key);
        }
      ),
    [methods.watch("healthProfile.diagnosisSubType"), healthProfileOptions.data]
  ) as ApplierKey | undefined;

  const diagnosisStageKey = useMemo(
    () =>
      healthProfileOptions.data?.diagnonsisStageFields.find(
        (key) => key === methods.watch("healthProfile.diagnosisSubType")
      ),
    [methods.watch("healthProfile.diagnosisSubType")]
  ) as DiagnoseStageKey | undefined;

  const additionalApplierValues: string[] = useMemo(() => {
    return (
      (additionalApplier &&
        healthProfileOptions.data?.additionalDiagonsisAppliers[
          additionalApplier
        ]) ||
      []
    );
  }, [additionalApplier, healthProfileOptions.data]);

  const { handleSubmit } = methods;

  const uploadImage = useUploadImage();

  const handleSelectImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.5,
    });

    if (!result.canceled) {
      handleUploadImage(result?.assets?.[0]?.uri);
    }
  };

  const handleProfileImageUpload = trpc.user.updateUserPartial.useMutation();

  const handleUploadImage = async (uri: string) => {
    try {
      const formdata = new FormData();
      if (uri) {
        formdata.append("filePath", s3Paths.profilePicture);
        formdata.append("image", {
          uri: uri,
          type: "image/png",
          name: "image.png",
        } as any);

        const {
          data: { s3Path },
        } = await uploadImage.mutateAsync({ formdata });

        await handleProfileImageUpload.mutateAsync(
          {
            profilePicture: s3Path,
            userOrPatientId,
          },
          {
            onSuccess: () => {
              utils.user.me.invalidate(userOrPatientId);
            },
          }
        );
      }

      // toast notification
      Toast.show({
        type: "success",
        text1: "profile picture added successfully",
        visibilityTime: 2000,
      });
    } catch (ex) {
      const err = errorHandler(ex);
      alert(err ? err : "Something went wrong");
    }
  };

  const userProfileOptions = trpc.lib.userProfileOptions.useQuery();
  const states = trpc.lib.states.useQuery();
  const updateUserProfile =
    trpc.user.createUpdateUserOrPatientProfile.useMutation();
  const updateUserFullProfile = trpc.user.updateUserFullProfile.useMutation();

  const onSubmit: SubmitHandler<FormData> = async (payload) => {
    await updateUserFullProfile.mutateAsync(payload);
    utils.user.invalidate();
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: "AppNavigator",
          },
        ],
      })
    );
  };

  if (healthProfileOptions.isLoading) return <LoadingScreen />;
  return (
    <Screen className="mx-4">
      <AppScrollView>
        <FormProvider {...methods}>
          <View className="my-4">
            <AppText className="text-[#004987] font-montserratSemiBold text-lg">
              patient's personal details
            </AppText>
          </View>
          <Pressable
            actionTag="manage profile"
            onPress={handleSelectImage}
            className="mb-6"
          >
            {me?.profilePicture ? (
              <Image
                source={{
                  uri: imageKit({
                    imagePath: me?.profilePicture,
                    transform: ["w-100"],
                  }),
                }}
                style={{
                  borderWidth: 0.2,
                  borderRadius: 50,
                  width: 100,
                  height: 100,
                }}
              />
            ) : (
              <LogoSimple />
            )}
          </Pressable>
          <EmailPhoneUpdate userOrPatientId={userOrPatientId} />
          {/* user profile */}
          <FormProvider {...methods}>
            <View style={{ gap: 8 }}>
              <AppFormInput
                name="userProfile.firstname"
                label="patient first name"
                placeholder="first name"
              />
              <AppFormInput
                name="userProfile.lastname"
                label="patient last name"
                placeholder="last name"
              />
              <AppFormInput
                name="userProfile.username"
                label="patient username"
                placeholder="username"
              />
              <AppFormDatePicker
                name="personalProfile.personal.dob"
                label="Date of birth"
                placeholder="d.o.b"
              />
              <AppFormSelect
                name="personalProfile.personal.race"
                label="Race"
                placeholder="race"
              >
                {(Object.values(userProfileOptions.data?.race || {}) || []).map(
                  (race) => {
                    return <Option key={race} value={race} label={race} />;
                  }
                )}
              </AppFormSelect>
              <AppFormSelect
                name="personalProfile.personal.gender"
                label="Gender"
                placeholder="gender"
              >
                {(
                  Object.values(userProfileOptions.data?.gender || {}) || []
                ).map((gender) => {
                  return <Option key={gender} value={gender} label={gender} />;
                })}
              </AppFormSelect>
              <AppFormSelect
                name="personalProfile.address.state"
                label="State"
                placeholder="state"
              >
                {/* @ts-ignore */}
                {(Object.values(states.data || {}) || []).map((state) => {
                  // @ts-ignore
                  return <Option key={state} value={state} label={state} />;
                })}
              </AppFormSelect>
              <AppFormInput
                name="personalProfile.address.city"
                label="City"
                placeholder="city"
              />
              <AppFormInput
                name="personalProfile.address.zip"
                label="Zip"
                placeholder="zip"
              />
            </View>
          </FormProvider>
        </FormProvider>
        <View className="my-4 flex-row items-center justify-between ">
          <AppText className="text-[#004987] font-montserratSemiBold text-lg">
            patient's health details
          </AppText>
          <Pressable
            className=""
            onPress={() =>
              navigation.navigate("HealthProfileScreen", { userOrPatientId })
            }
          >
            <PlusCircle color={"#004987"} />
          </Pressable>
        </View>
        <HealthProfileList userOrPatientId={userOrPatientId} />
      </AppScrollView>
      <View className="flex flex-row">
        <AppButton
          title="continue"
          className="mb-4"
          onPress={handleSubmit(onSubmit)}
          isLoading={
            createUpdateUserHealthProfile.isLoading ||
            updateUserFullProfile.isLoading ||
            createUpdateUserHealthProfile.isLoading ||
            updateUserProfile.isLoading
          }
        />
      </View>
    </Screen>
  );
}
