import LogoSimple from "@assets/svg/LogoOutline";
import EditIcon from "@assets/svg/profile/EditIcon";
import Email from "@assets/svg/profile/Email";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import { Option, Options } from "@components/common/AppSelect";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import Screen from "@components/common/Screen";
import AppFormDatePicker from "@components/form/AppFormDatePicker";
import AppFormInput from "@components/form/AppFormInput";
import AppFormSelect from "@components/form/AppFormSelect";
import FormMobileInput from "@components/form/elements/FormMobileInput";
import constants from "@constants/index";
import { yupResolver } from "@hookform/resolvers/yup";
import usePersistedUser from "@hooks/persistUser";
import useImagePicker from "@hooks/useImagePicker";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { useRequestOtp, useValidateOtp } from "@services/auth";
import useUpdateProfileImage, { useUpdateProfile } from "@services/profile";
import { useGetStateFilterOptions } from "@services/service";
import useGetProfileOptions from "@services/utilts";
import errorHandler from "@utils/errorhandler";
import { phoneSchema, selectOptionSchema } from "@utils/index";
import clsx from "clsx";
import { format } from "date-fns";
import { useCallback, useState } from "react";
import { useForm, FormProvider, SubmitHandler } from "react-hook-form";
import { Image, StyleSheet, View } from "react-native";
import { Toast } from "react-native-toast-message/lib/src/Toast";
import * as yup from "yup";

const minAge = 12;
const minDate = new Date();
minDate.setFullYear(minDate.getFullYear() - minAge);

const schema = yup
  .object({
    firstName: yup
      .string()
      .required("This is a required field")
      .label("First Name"),
    lastName: yup
      .string()
      .required("This is a required field")
      .label("Last Name"),
    email: yup.string().email().label("Email Address"),
    mobilePhone: phoneSchema,
    otp: yup.string().label("Enter Magic Code"),
    dob: yup
      .date()
      .max(minDate, "You must be at least 12 years old")
      .required("This is a required field")
      .label("Date Of Birth"),
    race: yup
      .array()
      .of(selectOptionSchema)
      .min(1)
      .required("This is a required field")
      .label("Race"),
    gender: yup
      .array()
      .of(selectOptionSchema)
      .min(1)
      .required("This is a required field")
      .label("Gender"),
    state: yup
      .array()
      .of(selectOptionSchema)
      .min(1)
      .required("This is a required field")
      .label("Gender"),
    city: yup.string().required("This is a required field"),
    zipCode: yup.string().required("This is a required field").min(5).max(5),
  })
  .required("This is a required field");

export type EditUserFormData = yup.InferType<typeof schema>;

export default function UserProfileEditScreen() {
  const [showOtpEmailView, setShowOtpEmailView] = useState(false);
  const [showOtpMobileView, setShowOtpMobileView] = useState(false);
  const [successEmailOtp, setSuccessEmailOtp] = useState(false);
  const [successMobileOtp, setSuccessMobileOtp] = useState(false);
  const { mutateAsync: updateProfileImage } = useUpdateProfileImage();
  const { persistedUser } = usePersistedUser();
  const { data: user } = persistedUser;

  const { params } =
    useRoute<RouteProp<ProfileNavParams, "HealthUserEditScreen">>();

  const methods = useForm<EditUserFormData>({
    defaultValues: {
      ...params,
      dob: params?.dob ? new Date(params.dob) : undefined,
    },
    // @ts-ignore
    resolver: yupResolver(schema),
    mode: "all",
  });
  const currentEmailAddress = methods.watch("email");
  const currentPhoneNumber = methods.watch("mobilePhone");
  const otpValue = methods.watch("otp");

  const countryCode = constants.countryCode;
  const userWithEmailWithoutCountryCode =
    user && user?.phoneNumber?.replace(countryCode, "");

  const isEmailSame = user && user.email === currentEmailAddress;
  const isPhoneSame = userWithEmailWithoutCountryCode === currentPhoneNumber;

  const { imagePicker } = useImagePicker();

  const handleUploadImage = useCallback(async () => {
    try {
      const assets = await imagePicker({
        allowsEditing: true,
        quality: 0.4,
        aspect: [1, 1],
      });

      if (assets?.length) {
        const selectedImage = assets[0].uri;

        const formdata = new FormData();

        formdata.append("file", {
          uri: selectedImage,
          type: "image/png",
          name: "image.png",
        } as any);

        await updateProfileImage({ body: formdata, userId: user?.id! });

        Toast.show({
          type: "success",
          text1: "profile updated successfully",
          visibilityTime: 2000,
        });
      }
    } catch (ex) {
      errorHandler(ex);
    }
  }, []);

  const { data: profileOptions } = useGetProfileOptions();
  const { data: states } = useGetStateFilterOptions();

  const raceOptions: Options[] =
    profileOptions?.race.map((_) => ({ label: _.name, value: _.id })) || [];
  const stateOptions: Options[] =
    states?.states.map((_) => ({ label: _.name, value: _.id })) || [];

  const genderOptions =
    profileOptions?.gender.map((_) => ({ label: _.name, value: _.id })) || [];

  const { mutateAsync: updateProfileAsync } = useUpdateProfile();
  const { mutateAsync: requestOtpAsync } = useRequestOtp();
  const { mutateAsync: validateOtp } = useValidateOtp();

  const sendOtp = async (category: string) => {
    try {
      const response = await requestOtpAsync({
        phoneOrEmail:
          category === "email" ? currentEmailAddress : currentPhoneNumber,
        type: category === "email" ? "email" : "phone",
      });
      if (response.status === 200) {
        if (category === "email") {
          setShowOtpEmailView(true);
        } else {
          setShowOtpMobileView(true);
        }
      }
    } catch (ex) {
      const errorMessage = errorHandler(ex);
      if (errorMessage) {
        setShowOtpEmailView(false);
        setShowOtpMobileView(false);
      }
      methods.setError(category === "email" ? "email" : "mobilePhone", {
        message: errorMessage,
      });
    }
  };

  const verifyOtp = async (category: string) => {
    try {
      if (otpValue && (currentEmailAddress || currentPhoneNumber)) {
        const response = await validateOtp({
          emailOrphoneNo:
            category === "email"
              ? currentEmailAddress
              : `${constants.countryCode}${currentPhoneNumber}`,
          otp: otpValue,
        });
        if (response && response.status === 200) {
          if (category === "email") {
            setSuccessEmailOtp(true);
            setShowOtpEmailView(false);
          } else {
            setSuccessMobileOtp(true);
            setShowOtpMobileView(false);
          }
          methods.setValue("otp", "");
        } else {
          methods.setError("otp", { message: response });
        }
      }
    } catch (ex) {
      const errorMessage = errorHandler(ex);
      if (errorMessage) methods.setError("otp", { message: errorMessage });
    }
  };

  const navigation = useNavigation();
  const onSubmit: SubmitHandler<EditUserFormData> = async ({
    firstName,
    lastName,
    email,
    mobilePhone,
    dob,
    gender,
    race,
    city,
    state,
    zipCode,
  }) => {
    try {
      const emailToUse = successEmailOtp ? email : user?.email;

      let mobileToUse = successMobileOtp ? mobilePhone : user?.phoneNumber;
      if (mobileToUse != null) {
        if (!mobileToUse.startsWith(constants.countryCode)) {
          mobileToUse = `${constants.countryCode}${mobileToUse}`;
        }
      }

      const payload = {
        firstName,
        lastName,
        email: emailToUse,
        birthDate: format(dob, "yyyy-MM-dd"),
        race: { id: Number(race[0].value), name: race[0].label },
        sex: { id: Number(gender[0].value), name: gender[0].label },
        state: { id: Number(state[0].value), name: state[0].label },
        id: user?.id!,
        city,
        zipCode,
        consents: [],
      };
      if (mobileToUse) {
        // @ts-ignore
        payload.mobilePhone = mobileToUse;
      }

      // @ts-ignore
      await updateProfileAsync(payload);
      navigation.goBack();

      Toast.show({
        type: "success",
        text1: "Profile updated successfully",
        visibilityTime: 2000,
      });
    } catch (ex) {
      errorHandler(ex);
    }
  };

  return (
    <View className="flex flex-1 m-4">
      <AppScrollView>
        {/* Update Profile Image */}
        <Pressable
          actionTag="manage profile image"
          onPress={handleUploadImage}
          className={clsx(
            "rounded-xl w-24 h-24 mb-4 flex items-center justify-center",
            !user?.avatar?.downloadUrl && "border-2 border-primary"
          )}
        >
          <View className="w-6 h-6 rounded-full bg-white justify-center items-center flex absolute border border-primary-dark -right-3 -bottom-3">
            <EditIcon width={10} height={10} />
          </View>

          {user?.avatar?.downloadUrl ? (
            <Image
              source={{ uri: user?.avatar?.downloadUrl }}
              style={{ ...StyleSheet.absoluteFillObject }}
              className="rounded-lg -z-10"
            />
          ) : (
            <LogoSimple />
          )}
        </Pressable>

        {/* Form */}
        <FormProvider {...methods}>
          <AppFormInput
            returnKeyType="next"
            autoCapitalize="none"
            name="firstName"
            placeholder="first name"
            label="First Name"
            wrapperClass="mt-4"
          />
          <AppFormInput
            returnKeyType="next"
            autoCapitalize="none"
            name="lastName"
            placeholder="last name"
            label="Last Name"
            wrapperClass="mt-4"
          />
          <View className="flex flex-row items-center">
            <View className="flex-1">
              <AppFormInput
                returnKeyType="next"
                autoCapitalize="none"
                name="email"
                placeholder="email address"
                label="Email Address"
                wrapperClass="mt-4"
              />
            </View>
            {!isEmailSame && (
              <View>
                <Pressable
                  actionTag="email otp"
                  className="ml-2 mt-10 px-4 py-2 rounded-xl items-center bg-[#004987]"
                  onPress={() => sendOtp("email")}
                  disabled={successEmailOtp}
                >
                  <AppText className="text-white text-lg">
                    {successEmailOtp ? "verified" : "verify"}
                  </AppText>
                </Pressable>
              </View>
            )}
          </View>
          {showOtpEmailView && (
            <View className="flex flex-row items-center">
              <View className="flex-1">
                <AppFormInput
                  returnKeyType="next"
                  autoCapitalize="none"
                  name="otp"
                  placeholder="******"
                  label="Enter Magic Code"
                  wrapperClass="mt-4"
                />
              </View>
              <View>
                <Pressable
                  actionTag="verifyotp email"
                  className="ml-2 mt-10 p-2 rounded-xl items-center bg-[#004987]"
                  onPress={() => verifyOtp("email")}
                >
                  <AppText className="text-white"> Submit </AppText>
                </Pressable>
              </View>
            </View>
          )}
          <View className="flex flex-row items-center">
            <View className="flex-1">
              <FormMobileInput name="mobilePhone" />
            </View>
            {!isPhoneSame && currentPhoneNumber && (
              <View>
                <Pressable
                  actionTag="verifyotp phone"
                  className="ml-2 mt-5 px-4 py-2 rounded-xl items-center  bg-[#004987]"
                  onPress={() => sendOtp("mobile")}
                  disabled={successMobileOtp}
                >
                  <AppText className="text-white text-lg">
                    {successMobileOtp ? "verified" : "verify"}
                  </AppText>
                </Pressable>
              </View>
            )}
          </View>
          {showOtpMobileView && (
            <View className="flex flex-row items-center">
              <View className="flex-1">
                <AppFormInput
                  returnKeyType="next"
                  autoCapitalize="none"
                  name="otp"
                  placeholder="******"
                  label="Enter Magic Code"
                  wrapperClass="mt-4"
                />
              </View>
              <View>
                <Pressable
                  className="ml-2 mt-10 p-2 rounded-xl items-center bg-[#004987]"
                  onPress={() => verifyOtp("mobile")}
                >
                  <AppText className="text-white"> Submit </AppText>
                </Pressable>
              </View>
            </View>
          )}

          <AppFormDatePicker
            name="dob"
            placeholder="enter dob"
            label="Date of birth"
            wrapperClass="mt-4"
          />
          <AppFormSelect
            name="race"
            placeholder="race"
            label="Race"
            wrapperClass="mt-4"
          >
            {raceOptions.map((_) => (
              <Option key={_.value} {..._} />
            ))}
          </AppFormSelect>
          <AppFormSelect
            name="gender"
            placeholder="gender"
            label="Gender"
            wrapperClass="mt-4"
          >
            {genderOptions.map((_) => (
              <Option key={_.value} {..._} />
            ))}
          </AppFormSelect>
          <AppFormSelect
            name="state"
            placeholder="state"
            label="State"
            wrapperClass="mt-4"
          >
            {stateOptions.map((_) => (
              <Option key={_.value} {..._} />
            ))}
          </AppFormSelect>
          <AppFormInput
            returnKeyType="next"
            autoCapitalize="none"
            name="city"
            placeholder="city"
            label="City"
            wrapperClass="mt-4"
          />
          <AppFormInput
            returnKeyType="next"
            autoCapitalize="none"
            name="zipCode"
            placeholder="ZIP Code"
            label="ZIP Code"
            wrapperClass="mt-4"
            keyboardType="numeric"
            maxLength={5}
          />

          <AppButton
            title="update profile"
            className="mt-4"
            onPress={methods.handleSubmit(onSubmit)}
          />
        </FormProvider>
      </AppScrollView>
    </View>
  );
}
