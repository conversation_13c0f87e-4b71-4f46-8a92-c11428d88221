import React, { useState, useEffect } from 'react';
import { View, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import Screen from '@components/common/Screen';
import AppText from '@components/common/AppText';
import AppScrollView from '@components/common/AppScrollView';
import Pressable from '@components/common/Pressable';
import { trpc } from '@providers/RootProvider';
import { useSession } from '@hooks/persistUser';
import { useNavigation } from '@react-navigation/native';
import { RootNavigationProp } from '@navigation/root-navigator';
import { startOfDay, endOfDay } from 'date-fns';

interface Question {
  _id: string;
  question: string;
  options: {
    _id: string;
    value: string;
    externalId: string;
  }[];
}

const dummyQuestions: Question[] = [
  {
    _id: '680e68b1e240a7642d83cdda',
    question: 'What is an activity that matters to you?',
    options: [
      {
        _id: '680e68ba273ede60ebabf054',
        value: 'I look forward to walking my dog',
        externalId: '111-71-1',
      },
      {
        _id: '680e68c51f07e2c2734df260',
        value: 'Carla looks forward to walking her dog',
        externalId: '111-71-1',
      },
    ],
  },
  {
    _id: '680e68cf46fb0f3995c16b44',
    question: 'Rate your ability to achive your activity goal:',
    // type: 'Slider',
    isSchedule: true,
    delay: 40,
    duration: 1,
    recurringQuestion: true,
    isActivityQuestion: true,
    action: [
      {
        actionType: 'NextQuestion',
        event: 'OptionSelect',
        metadata: {
          optionExternalId: '*',
          nextQuestionExternalId: '111-55',
        },
      },
    ],
    externalId: 111-72,
    options: [
      {
        _id: '680e68dc89b49171e232cbd1',
        value: '1',
        externalId: '111-71-1',
      },
      {
        _id: '680e68e662fc50f3b4078495',
        value: '2',
        externalId: '111-72-1',
      },
      {
        _id: '680e68f165c743b754fba5c1',
        value: '3',
        externalId: '111-72-1',
      },
      {
        _id: '680e68f8f44560473c15412c',
        value: '4',
        externalId: '111-72-1',
      },
      {
        _id: '680e68ff6befcac4d75ab4b9',
        value: '5',
        externalId: '111-72-1',
      },
    ],
  },
];

export default function ActivityScreen() {
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const { user } = useSession();
  const utils = trpc.useUtils();
  const navigation = useNavigation<RootNavigationProp>();
  const upsertUserDailyTracker = trpc.manage.upsertUserDailyTracker.useMutation();

  // Fetch existing answers
  const { data: existingTrackers } = trpc.manage.getUserDailyTrackers.useQuery({
    startDate: startOfDay(new Date()),
    endDate: endOfDay(new Date()),
    topicId: "6703f2a9ab6dd1cba828a588"
  });

  useEffect(() => {
    if (existingTrackers && existingTrackers.length > 0) {
      const existingAnswers = existingTrackers[0].answers;
      const selectedOptionsMap: Record<string, string> = {};
      
      existingAnswers.forEach(answer => {
        if (answer.selectedOption && answer.selectedOption.length > 0) {
          selectedOptionsMap[answer.question] = answer.selectedOption[0];
        }
      });
      
      setSelectedOptions(selectedOptionsMap);
    }
  }, [existingTrackers]);

  const handleOptionSelect = (questionId: string, optionId: string) => {
    setSelectedOptions(prev => ({
      ...prev,
      [questionId]: optionId
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    
    // Only include answers that have been selected
    const answers = Object.entries(selectedOptions).map(([questionId, optionId]) => {
      const question = dummyQuestions.find(q => q._id === questionId);
      const selectedOption = question?.options.find(opt => opt._id === optionId);
      
      return {
        question: questionId,
        answerText: selectedOption?.value || '',
        selectedOption: [optionId],
        rawOptionData: {
          value: selectedOption?.value || '',
          externalId: selectedOption?.externalId,
          answersFromProfile: true
        }
      };
    });

    const payload = {
      topic: "6703f2a9ab6dd1cba828a588",
      date: new Date(),
      answers,
      completed: false
    };

    try {
      await upsertUserDailyTracker.mutateAsync(payload);
      // Clear selections after successful save
      setSelectedOptions({});
      // Navigate back to profile screen
      navigation.goBack();
    } catch (error) {
      console.error('Failed to save responses:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const hasAnyAnswer = Object.keys(selectedOptions).length > 0;

  return (
    <Screen className="mx-4 mt-6">
      <AppScrollView>
        <View className="space-y-6">
          {dummyQuestions.map((question) => (
            <View key={question._id} className="bg-white rounded-lg p-4 shadow-sm">
              <AppText className="text-lg font-montserratMedium mb-4">
                {question.question}
              </AppText>
              <View className="space-y-2">
                {question.options.map((option) => (
                  <Pressable
                    key={option._id}
                    onPress={() => handleOptionSelect(question._id, option._id)}
                    className={`p-3 rounded-md ${
                      selectedOptions[question._id] === option._id
                        ? 'bg-gray-100 border border-[#004987]'
                        : 'bg-gray-100'
                    }`}
                  >
                    <AppText
                      className={`${
                        selectedOptions[question._id] === option._id
                          ? 'text-[#004987]'
                          : 'text-[#004987]'
                      }`}
                    >
                      {option.value}
                    </AppText>
                  </Pressable>
                ))}
              </View>
            </View>
          ))}
        </View>
      </AppScrollView>
      <View className="absolute bottom-4 left-0 right-0 px-4">
        <Pressable
          onPress={handleSave}
          disabled={!hasAnyAnswer}
          className={`p-4 rounded-lg ${
            hasAnyAnswer ? 'bg-[#004987]' : 'bg-gray-300'
          }`}
        >
          {isSaving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <AppText className="text-white text-center font-montserratMedium">
              Save
            </AppText>
          )}
        </Pressable>
      </View>
    </Screen>
  );
} 