import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import { Option, Options } from "@components/common/AppSelect";
import AppStepper from "@components/common/AppStepper";
import Screen from "@components/common/Screen";
import AppFormSelect from "@components/form/AppFormSelect";
import { yupResolver } from "@hookform/resolvers/yup";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { View } from "react-native";
import * as yup from "yup";
import { phoneSchema, selectSchema } from "@utils/index";
import errorHandler from "@utils/errorhandler";
import usePersistedUser from "@hooks/persistUser";
import React from "react";
import AppFormInput from "@components/form/AppFormInput";
import useGetProfileOptions from "@services/utilts";
import { useGetPatientProfileByUserId } from "@services/profile";

const schema = yup.object({
  firstname: yup
    .string()
    .required("This is a required field")
    .label("patient first name"),
  lastname: yup
    .string()
    .required("This is a required field")
    .label("patient last name"),
  phone: phoneSchema,
  email: yup.string().email().label("Patient email"),
  zipcode: yup
    .string()
    .required("This is a required field")
    .label("zip code")
    .min(5)
    .max(5),
  race: selectSchema.required("This is a required field"),
  gender: selectSchema.required("This is a required field"),
});

type FormData = yup.InferType<typeof schema>;

export default function CreatePatient() {
  const { persistedUser } = usePersistedUser();
  const { data: user } = persistedUser;

  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();
  const { params } =
    useRoute<RouteProp<ProfileNavParams, "CreatePatientScreen">>();

  const patientData = params?.patientData?.patient;
  const id = patientData?.id ?? null;
  const firstname = patientData?.firstName ?? null;
  const lastname = patientData?.lastName ?? null;
  const email = patientData?.email ?? null;
  const mobilePhone = patientData?.mobilePhone ?? null;
  const zipCode = patientData?.zipCode ?? null;
  const race = patientData?.race ?? null;
  const sex = patientData?.sex ?? null;
  const methods = useForm<FormData>({
    // @ts-ignore
    resolver: yupResolver(schema),
    mode: "all",
    defaultValues: {
      ...params,
      firstname: firstname || "",
      lastname: lastname || "",
      phone: mobilePhone || "",
      email: email || "",
      zipcode: zipCode || "",
      race: [{ label: race?.name, value: race?.id }] || "",
      gender: [{ label: sex?.name, value: sex?.id }] || "",
    },
  });

  const { handleSubmit } = methods;
  const { data: profileOptions } = useGetProfileOptions();

  const raceOptions: Options[] =
    profileOptions?.race.map((_) => ({ label: _.name, value: _.id })) || [];

  const genderOptions =
    profileOptions?.gender.map((_) => ({ label: _.name, value: _.id })) || [];

  const { data: patientProfile, isLoading: patienProfileLoading } =
    useGetPatientProfileByUserId(user?.id);

  const onSubmit: SubmitHandler<FormData> = async ({
    firstname,
    lastname,
    phone,
    email,
    zipcode,
    race: [race],
    gender: [gender],
  }) => {
    try {
      navigation.navigate("HealthProfileFormScreen", {
        id: id,
        username: "",
        firstName: firstname,
        lastName: lastname,
        mobilePhone: phone,
        email: email,
        zipCode: zipcode,
        race: { id: race.value, name: race.label },
        sex: { id: gender.value, name: gender.label },
        diagnoseDate: patientProfile?.diagnoseDate!,
        disease:
          patientProfile?.diagnosis?.id &&
          patientProfile?.diagnosis?.diseaseTitle?.[0]?.id
            ? [
                {
                  label:
                    patientProfile?.diagnosis?.diseaseTitle?.[0]
                      ?.diseaseTitleName,
                  value: patientProfile?.diagnosis?.diseaseTitle?.[0]?.id,
                },
              ]
            : undefined,
        diagnosis: patientProfile?.diagnosis?.id
          ? [
              {
                label: patientProfile?.diagnosis?.name!,
                value: patientProfile?.diagnosis?.id!,
              },
            ]
          : undefined,
        patientBiopsyReport: patientProfile?.patientBiopsyReport?.id
          ? [
              {
                label: patientProfile?.patientBiopsyReport?.name!,
                value: patientProfile?.patientBiopsyReport?.id!,
              },
            ]
          : undefined,
        stage: patientProfile?.stage?.name
          ? [
              {
                label: patientProfile?.stage?.name!,
                value: patientProfile?.stage?.id!,
              },
            ]
          : undefined,
        patientProfileHer2Statuses: patientProfile?.patientProfileHer2Statuses
          ?.length
          ? patientProfile?.patientProfileHer2Statuses?.map((_) => ({
              label: _.name,
              value: _.id,
            }))
          : undefined,
        personalizedExperience: patientProfile?.helpOnPersonalisedExperience
          ? [
              {
                label: patientProfile?.helpOnPersonalisedExperience
                  ? "Yes"
                  : "No",
                value: patientProfile?.helpOnPersonalisedExperience
                  ? ("yes" as unknown as number) //TODO: fix
                  : ("no" as unknown as number), //TODO: fix
              },
            ]
          : undefined,
      });
    } catch (ex) {
      errorHandler(ex);
    }
  };

  return (
    <Screen className="mx-4">
      <AppStepper
        steps={["Sign up", "User Profile", "Health Profile"]}
        activeStep={1}
        className="mt-4"
      />

      <AppScrollView>
        <AppText className="text-primary font-montserratSemiBold text-xl my-10">
          enter patient details
        </AppText>

        {/* Form */}
        <FormProvider {...methods}>
          <View style={{ gap: 15 }}>
            <AppFormInput
              name="firstname"
              placeholder="enter first name"
              label="patient first name"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormInput
              name="lastname"
              placeholder="enter last name"
              label="patient last name"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormInput
              name="phone"
              placeholder="enter number"
              label="phone number"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormInput
              name="email"
              placeholder="enter email"
              label="email"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormInput
              name="zipcode"
              placeholder="zip code"
              label="zip code"
              keyboardType="default"
              autoCapitalize="none"
            />
            <AppFormSelect name="race" placeholder="race" label="Race">
              {raceOptions.map((_) => (
                <Option key={_.value} {..._} />
              ))}
            </AppFormSelect>
            <AppFormSelect name="gender" placeholder="gender" label="Gender">
              {genderOptions.map((_) => (
                <Option key={_.value} {..._} />
              ))}
            </AppFormSelect>
          </View>
        </FormProvider>
      </AppScrollView>
      <View className="flex-row mt-4">
        <AppButton
          title="continue"
          className="mb-4"
          onPress={handleSubmit(onSubmit)}
        />
      </View>
    </Screen>
  );
}
