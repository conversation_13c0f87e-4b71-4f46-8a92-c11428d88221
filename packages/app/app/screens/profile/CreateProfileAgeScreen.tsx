import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useState } from "react";
import { Dimensions, View, Text } from "react-native";
import AppButton from "@components/common/AppButton";
import BookIcon from "@assets/svg/BookIcon";
import CompleteDailyCheckInIcon from "@assets/svg/CompleteDailyCheckInIcon";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import AppTextInput from "@components/common/AppTextInput";
import AppFormInput from "@components/form/AppFormInput";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import errorHandler from "@utils/errorhandler";
import { zodResolver } from "@hookform/resolvers/zod";
import AppFormCheckbox from "@components/form/AppFormCheckbox";
import AppText from "@components/common/AppText";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const schema = z.object({
  age: z.string().min(1, "Age is required"),
});

type Form = z.infer<typeof schema>;

export default function CreateProfileAgeScreen() {
  const navigation = useNavigation<RootNavigationProp>();
  const { user } = useSession();

  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      age: "",
    },
  });
  const { mutateAsync: updateUser, isLoading: isRequestOtpLoading } =
    trpc.auth.requestLoginOtp.useMutation();
  const updateUserPartial = trpc.user.updateUserPartial.useMutation();
  const { handleSubmit } = methods;
  const onSubmit = handleSubmit(async (data) => {
    try {
      console.log({ data });
      const res = await updateUserPartial.mutateAsync({
        age: data?.age,
        userOrPatientId: user?._id?.toString() as string,
      });
      // await updateUser({
      //   age: data?.age,
      //   deviceId: user?.deviceId,
      //   isUpsert: true,
      // });
      navigation.navigate("CreateProfileUserDetailScreen");
    } catch (ex) {
      errorHandler(ex);
    }
  });

  return (
    <Screen>
      <View className="flex-row items-center">
        <ChevronLeft
          color={"#004987"}
          size={22}
          // @ts-expect-error
          className="ml-4"
          onPress={() => navigation.goBack()}
        />
        <ProgressBar
          total={3}
          current={2}
          style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
        />
      </View>
      <View className="flex-1 items-center mt-8">
        <View className="flex-row items-center mb-8">
          <ButterFlyIcon height={140} width={90} />
          <QuestionPrompt message="How old are you?" />
        </View>

        <View className="flex-1 p-2" style={{ width: cardWidth, gap: 8 }}>
          <FormProvider {...methods}>
            <AppFormInput
              name="age"
              fieldClass=""
              placeholderTextColor="#B4DDFF"
              placeholder="Age"
              borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
              className="rounded-lg text-[18px]"
            />
            <View className="flex-1 mt-8">
              <AnimatedAppButton
                btnContainer="flex flex-row mb-2"
                title="NEXT"
                variant={"new-primary"}
                className="flex-1 rounded-full"
                textClassName="text-[21px]"
                style={{
                  shadowColor: "#003366",
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 1,
                  shadowRadius: 1,
                  elevation: 5,
                }}
                onPress={onSubmit}
              />
            </View>
          </FormProvider>
        </View>
      </View>
    </Screen>
  );
}
