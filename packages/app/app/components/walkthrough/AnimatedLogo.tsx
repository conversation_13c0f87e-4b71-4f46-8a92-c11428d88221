import Logo from "@assets/svg/Logo";
import { useEffect, useState } from "react";
import { Dimensions } from "react-native";
import Animated, {
  Easing,
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

const { height, width } = Dimensions.get("screen");

interface Props {
  onAnimateEnd: () => void;
}
export default function AnimatedLogo({ onAnimateEnd }: Props) {
  const [logoHeight, setLogoHeight] = useState(0);

  const logoPosition = useSharedValue(1);
  const logoAnimatedStyles = useAnimatedStyle(() => ({
    top: interpolate(logoPosition.value, [1, 0], [height / 2, 20]),
    transform: [{ scale: interpolate(logoPosition.value, [1, 0], [1, 0.45]) }],
  }));

  useEffect(() => {
    logoPosition.value = withTiming(
      0,
      { duration: 2000, easing: Easing.ease },
      () => runOnJS(onAnimateEnd)()
    );
  }, [logoPosition, onAnimateEnd]);

  return (
    <Animated.View
      style={[
        {
          position: "absolute",
          top: height / 2,
          transform: [{ translateY: -logoHeight }],
          zIndex: 10,
          justifyContent: "center",
          width: "100%",
        },
        logoAnimatedStyles,
      ]}
    >
      <Logo
        onLayout={(e) => setLogoHeight(e.nativeEvent.layout.height)}
        width={width}
      />
    </Animated.View>
  );
}
