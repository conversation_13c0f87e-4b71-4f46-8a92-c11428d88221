import AppText from "@components/common/AppText";
import clsx from "clsx";
import { View } from "react-native";

interface Props {
  Svg?: JSX.Element;
  Image?: JSX.Element;
  title?: string;
  subTitle?: string;
  contentClassName?: string;
}
export default function Walkthrough({
  Svg,
  Image,
  title,
  subTitle,
  contentClassName,
}: Props) {
  return (
    <View className="w-[100vw]">
      {Svg ? Svg : Image}
      <View className={clsx("mx-4 mt-16", contentClassName)}>
        <AppText className="text-center font-montserratSemiBold text-2xl text-[#023967]">
          {title}
        </AppText>
        <AppText className="text-center text-base mt-4 text-[#004987] adjustsFontSizeToFit">
          {subTitle}
        </AppText>
      </View>
    </View>
  );
}
