import React, { useEffect } from "react";
import { Alert } from "react-native";
import * as Updates from "expo-updates";

export default function ExpoUpdateModal() {
    useEffect(() => {
        async function checkForUpdate() {
            try {
                const update = await Updates.checkForUpdateAsync();
                if (update.isAvailable) {
                    await Updates.fetchUpdateAsync();
                    Alert.alert("Update Available", "Restarting app to apply update.", [
                        { text: "OK", onPress: () => Updates.reloadAsync() }
                    ]);
                }
            } catch (error) {
                console.error("Error fetching update:", error);
            }
        }
        checkForUpdate();
    }, []);

    return <React.Fragment/>;
}
