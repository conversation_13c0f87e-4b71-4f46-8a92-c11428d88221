import React, { useState } from "react";
import { Animated, PressableProps } from "react-native";
import { AppButtonProps } from "./AppButton";

interface WithPressAnimationProps extends AppButtonProps {
  btnContainer?: string;
}

export default function withPressAnimation<T extends WithPressAnimationProps>(
  WrappedComponent: React.ComponentType<T>
) {
  return ({ btnContainer, ...props }: WithPressAnimationProps) => {
    const [scaleValue] = useState(new Animated.Value(1));

    const handlePressIn = () => {
      Animated.spring(scaleValue, {
        toValue: 0.75,
        useNativeDriver: true,
      }).start();
    };

    const handlePressOut = () => {
      Animated.spring(scaleValue, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    };

    return (
      <Animated.View
        style={[{ transform: [{ scale: scaleValue }] }]}
        className={btnContainer}
      >
        <WrappedComponent
          {...(props as T)}
          onPressIn={(e) => {
            handlePressIn();
            props.onPressIn?.(e);
          }}
          onPressOut={(e) => {
            handlePressOut();
            props.onPressOut?.(e);
          }}
        />
      </Animated.View>
    );
  };
}
