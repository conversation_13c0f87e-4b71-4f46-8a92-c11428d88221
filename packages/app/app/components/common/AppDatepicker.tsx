import { Entypo } from "@expo/vector-icons";
import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker";
import { format } from "date-fns";
import React, { useCallback, useState } from "react";
import { View, Platform, Modal } from "react-native";

import AppButton from "./AppButton";
import AppTextInput, { AppTextInputProps } from "./AppTextInput";
import Pressable from "./Pressable";

const PressableWrapper = Platform.OS === "android" ? Pressable : React.Fragment;

function formatTime(v: number) {
  return v < 10 ? `0${v}` : v;
}

function showDateTime(date: Date) {
  return `${format(date, "MM/dd/yyyy")} ${formatTime(
    date.getHours()
  )}:${formatTime(date.getMinutes())}`;
}

export interface AppDatePickerProps extends AppTextInputProps {
  onChangeDate?: (date: Date) => void;
  date?: Date;
  mode?: "date" | "time" | "datetime";
}

function AppDatePicker({
  onChangeDate,
  date,
  mode = "date",
  ...otherProps
}: AppDatePickerProps) {
  const [showDateModal, setShowDateModal] = useState(false);
  const [tempDate, setTempDate] = useState(date || new Date());
  const [isDatePicker, setIsDatePicker] = useState(true);
  const [buttonLabel, setButtonLabel] = useState(
    mode === "datetime" ? "Next" : "Confirm"
  );

  const handleModal = useCallback((status: boolean) => {
    setShowDateModal(status);
  }, []);

  const onChange = useCallback(
    (event: DateTimePickerEvent, selectedDate?: Date) => {
      if (selectedDate) {
        if (mode === "datetime") {
          if (isDatePicker) {
            // Store the date part and switch to the time picker
            setTempDate((prevDate) => {
              const updatedDate = new Date(prevDate);
              updatedDate.setFullYear(
                selectedDate.getFullYear(),
                selectedDate.getMonth(),
                selectedDate.getDate()
              );
              return updatedDate;
            });
            if (Platform.OS === "android") {
              setIsDatePicker(false);
            }
          } else {
            // Update the time part and finalize the Date object
            setTempDate((prevDate) => {
              const updatedDate = new Date(prevDate);
              updatedDate.setHours(
                selectedDate.getHours(),
                selectedDate.getMinutes(),
                selectedDate.getSeconds()
              );
              onChangeDate?.(updatedDate);
              return updatedDate;
            });
            if (Platform.OS === "android") {
              handleModal(false);
            }
          }
        } else {
          if (Platform.OS === "android") {
            handleModal(false);
          }
          onChangeDate?.(selectedDate);
        }
      } else {
        handleModal(false);
      }
    },
    [mode, isDatePicker, onChangeDate, handleModal]
  );

  const openPicker = useCallback(() => {
    if (mode === "datetime") {
      setIsDatePicker(true);
      setButtonLabel("Next");
    }
    handleModal(true);
  }, [mode, handleModal]);

  return (
    <>
      <PressableWrapper
        {...(Platform.OS === "android" && {
          onPress: openPicker,
        })}
      >
        <AppTextInput
          editable={false}
          selectTextOnFocus={false}
          onPressIn={openPicker}
          numberOfLines={1}
          iconRight={
            <Entypo name="chevron-small-down" size={24} color="black" />
          }
          value={
            date &&
            (mode === "time"
              ? formatTime(date.getHours()) +
              ":" +
              formatTime(date.getMinutes())
              : mode === "datetime"
                ? showDateTime(date)
                : format(date, "MM/dd/yyyy"))
          }
          {...otherProps}
        />
      </PressableWrapper>
      {Platform.OS === "ios" ? (
        <Modal
          visible={showDateModal}
          transparent
          onDismiss={() => handleModal(false)}
        >
          <Pressable
            actionTag={`date-time-picker`}
            className="flex flex-1 bg-black/25 justify-center items-center"
            onPress={() => handleModal(false)}
          >
            <View className="bg-white rounded-lg p-3">
              <DateTimePicker
                maximumDate={new Date()}
                value={date || new Date()}
                mode={isDatePicker ? "date" : "time"}
                display={"spinner"}
                onChange={onChange}
              />
              <View className="flex flex-row">
                <AppButton
                  title={buttonLabel}
                  onPress={() => {
                    if (isDatePicker && mode === "datetime") {
                      setIsDatePicker(false);
                      setButtonLabel("Confirm");
                    } else {
                      handleModal(false);
                    }
                  }}
                />
              </View>
            </View>
          </Pressable>
        </Modal>
      ) : (
        showDateModal && (
          <DateTimePicker
            maximumDate={new Date()}
            value={date || new Date()}
            mode={isDatePicker ? "date" : "time"}
            display={"compact"}
            onChange={(...args) => {
              onChange(...args);
            }}
          />
        )
      )}
    </>
  );
}

export default React.memo(AppDatePicker);
