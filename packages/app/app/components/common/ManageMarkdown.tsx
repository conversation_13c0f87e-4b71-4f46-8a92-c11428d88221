// @ts-ignore
import <PERSON> from "react-native-markdown-package";
import { Linking, StyleSheet, TextStyle, View } from "react-native";
import { useMemo } from "react";

export default function ManageMarkdown({
  content,
  style,
}: {
  content: string;
  style?: TextStyle;
}) {
  const markdownStyles = useMemo(
    () => ({
      collectiveMd: {
        text: {
          fontFamily: "Montserrat_400Regular",
          lineHeight: 21,
          fontSize: 18,
          ...style,
        },
        strong: {
          fontFamily: "Montserrat_700Bold",
        },
      },
    }),
    [style]
  );

  return (
    <MD
      styles={markdownStyles.collectiveMd}
      onLink={(url: string) => Linking.openURL(url)}
    >
      {content}
    </MD>
  );
}
