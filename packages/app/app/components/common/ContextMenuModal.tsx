import React from 'react';
import { Modal, Pressable, StyleSheet, View } from 'react-native';
import AppText from './AppText';
import _ from 'lodash';

interface ContextMenuItem {
  label: string;
  onClick?: () => void;
  color?: string;
  icon?: string;
}

interface ContextMenuModalProps {
  visible: boolean;
  onClose: () => void;
  items: ContextMenuItem[];
  width?: number;
}

const ContextMenuModal: React.FC<ContextMenuModalProps> = ({
  visible,
  onClose,
  items,
  width = 300,
}) => {
  return (
    <Modal
      transparent
      visible={visible}
      onRequestClose={onClose}
    >
      <Pressable
        style={[
          StyleSheet.absoluteFillObject,
          { justifyContent: 'center', alignItems: 'center' },
        ]}
        onPress={onClose}
      >
        <View
          style={[
            styles.container,
            { width },
          ]}
        >
          {items?.map((item, index) => (
            <Pressable
              key={index}
              style={[
                styles.menuItem,
                index !== items.length - 1 && styles.menuItemBorder,
              ]}
              onPress={() => {
                if (_.isFunction(item?.onClick)) {
                  item?.onClick();
                }
                onClose();
              }}
            >
              <AppText
                style={[
                  styles.menuItemText,
                  { color: item.color || '#0B79D3' },
                ]}
              >
                {item.label}
              </AppText>
              {item.icon ? (
                <AppText style={styles.menuItemIcon}>{item.icon}</AppText>
              ) : null}
            </Pressable>
          ))}
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  menuItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#D0E8FF',
  },
  menuItemText: {
    fontSize: 18,
  },
  menuItemIcon: {
    fontSize: 18,
  },
});

export default ContextMenuModal; 