import { FlatList, View } from 'react-native';
import NotificationIcon from '@assets/svg/Notification';
import { useNavigation } from '@react-navigation/native';
import { RootNavParams } from '@navigation/root-navigator/RootNavParams';
import { StackNavigationProp } from '@react-navigation/stack';
import MyRabblesLogoHeader from '@assets/svg/MyRabblesLogoHeader';
import useAuthGuard from '@hooks/useAuthGuard';
import AppButton from './AppButton';
import { useEffect, useState } from 'react';

type Item = {
  id: string;
  value: string | undefined;
};

type Props = {
  data: Item[];
  handleChipSelect?: Function;
};

export default function Chips(props: Props) {
  const [chipsSelected, setChipsSelected] = useState<string[] | null>(null);

  useEffect(() => {
    if (chipsSelected) {
      props.handleChipSelect?.(chipsSelected);
    }
  }, [chipsSelected]);

  return (
    <FlatList
      horizontal
      data={props.data}
      className=''
      style={{ marginTop: 20 }}
      renderItem={({ item }) => (
        <AppButton
          className='rounded-3xl'
          title={item.value}
          variant={
            chipsSelected?.includes(item.id) ? 'secondary' : 'disabled-outline'
          }
          size='xs'
          style={{ marginRight: 4 }}
          onPress={() => {
            const index = (chipsSelected || []).findIndex(
              (_chip) => _chip === item.id
            );
            if (index > -1 && chipsSelected) {
              const _chipsSelected = chipsSelected.slice();
              _chipsSelected.splice(index, 1);
              setChipsSelected(_chipsSelected);
            } else {
              setChipsSelected((_chips) =>
                _chips ? [..._chips, item.id] : [item.id]
              );
            }
          }}
        />
      )}
      keyExtractor={(item) => item.id}
    />
  );
}
