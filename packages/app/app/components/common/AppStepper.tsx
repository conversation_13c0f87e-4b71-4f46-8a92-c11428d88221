import AppText from "@components/common/AppText";
import clsx from "clsx";
import { styled } from "nativewind";
import React from "react";
import { StyleSheet, View, ViewProps } from "react-native";

import DashedLine from "./DashedLine";

interface Props extends ViewProps {
  steps: string[];
  activeStep?: number;
}

const styles = StyleSheet.create({
  stepCount: {
    borderRadius: 6,
    overflow: "hidden",
    borderColor: "#FF8E1C",
    borderWidth: 2,
  },
  seperator: { borderStyle: "dotted" },
});

const Wrapper = styled(
  View,
  "flex flex-row items-center justify-between w-full"
);

export default function AppStepper({
  steps = [],
  activeStep = 0,
  ...otherProps
}: Props) {
  return (
    <Wrapper {...otherProps}>
      {steps.map((step, idx) => {
        return (
          <React.Fragment key={idx}>
            {/* render step count and name */}
            <View className="flex items-center flex-row mx-2 ">
              <AppText
                className={clsx(
                  "border-accent bg-accent px-2 py-[3px] text-white text-xs",
                  idx > activeStep && "bg-white text-accent"
                )}
                style={styles.stepCount}
              >
                {idx + 1}
              </AppText>
              <AppText
                className={clsx(
                  "text-primary ml-2 text-xs",
                  idx > activeStep && "text-neutral-400"
                )}
              >
                {step}
              </AppText>
            </View>
            {/* seperator */}
            {idx < steps.length - 1 && (
              <DashedLine
                wrapperClass="flex-1"
                axis="horizontal"
                dashColor="#004987"
                dashGap={4}
                dashLength={6}
                dashThickness={1}
              />
            )}
          </React.Fragment>
        );
      })}
    </Wrapper>
  );
}
