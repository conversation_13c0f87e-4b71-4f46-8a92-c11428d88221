import AppText from "@components/common/AppText";
import clsx from "clsx";
import { useMemo, useRef, useState } from "react";
import { TextInput, View, Clipboard } from "react-native";
import Pressable from "./Pressable";

interface Props {
  otp: string;
  wrapperClass?: string;
  customClass?: string;
  onChangeText?: (text: string) => void;
  error?: string;
}

export default function AppOtp({
  otp,
  wrapperClass,
  onChangeText,
  error,
  customClass,
}: Props) {
  const textInputRef = useRef<TextInput>(null);
  const [longPressTriggered, setLongPressTriggered] = useState(false);

  const handlePress = async () => {
    if (!longPressTriggered) {
      textInputRef.current?.focus();
    }
  };

  const handleLongPress = async () => {
    setLongPressTriggered(true); // Set the flag
    try {
      const clipboardContent = await Clipboard.getString();
      console.log("Clipboard Content:", clipboardContent);

      if (clipboardContent.length === 4 && /^\d+$/.test(clipboardContent)) {
        onChangeText?.(clipboardContent);
      }
    } catch (error) {
      console.error("Failed to read clipboard content:", error);
    } finally {
      setLongPressTriggered(false); 
    }
  };

  const otpArr = useMemo(() => (otp ? otp.split("") : []), [otp]);

  return (
    <View className={clsx(wrapperClass)}>
      <TextInput
        ref={textInputRef}
        placeholder=""
        placeholderTextColor="transparent"
        maxLength={4}
        keyboardType="number-pad"
        className="absolute h-1 w-1 text-transparent -top-[9999px]"
        onChangeText={(text) => {
          text.length <= 4 && onChangeText?.(text);
        }}
        value={otp}
        cursorColor="#fff"
        editable
        selectTextOnFocus
      />

      <Pressable
        className="flex flex-row items-center justify-between"
        onPress={handlePress}
        actionTag={`otp-input`}
        onLongPress={handleLongPress}
      >
        {new Array(4).fill("").map((_, idx) => {
          return (
            <Box
              key={idx}
              content={otpArr[idx]}
              active={idx < otpArr.length}
              error={!!error}
              customClass={customClass}
            />
          );
        })}
      </Pressable>

      <AppText className="text-danger">{error}</AppText>
    </View>
  );
}

interface BoxProps {
  content?: string;
  error?: boolean;
  active?: boolean;
  customClass?: string;
}
function Box({ content, error, active, customClass = "" }: BoxProps) {
  return (
    <View
      className={clsx(
        `w-12 h-12 rounded-lg border-2 border-neutral-400 flex justify-center items-center ${customClass}`,
        active && "border-primary",
        error && "border-danger"
      )}
    >
      <AppText
        className={clsx(
          "text-xl text-neutral-500",
          active && "text-primary",
          error && "text-danger"
        )}
      >
        {content}
      </AppText>
    </View>
  );
}
