import React from "react";
import { View, Alert } from "react-native";
import AppTextInput from "./AppTextInput";
import Pressable from "./Pressable";

interface SearchInputProps {
  placeholder?: string;
  iconLeft?: React.ReactNode;
  onPress?: () => void;
  editable?: boolean;
  borderClass?: string;
  placeholderTextColor?: string;
  className?: string;
  containerClassName?: string;
}

export default function AiSearchInput({
  placeholder = "Ask Belle AI or Search",
  iconLeft,
  onPress,
  editable = false,
  borderClass = "border bg-white border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]",
  placeholderTextColor = "rgba(0, 73, 135, 0.40)",
  className = "h-14 text-[18px]",
  containerClassName = "px-4 mb-[8px]",
}: SearchInputProps) {
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      Alert.alert("Coming Soon");
    }
  };

  return (
    <Pressable onPress={handlePress} className={containerClassName}>
      <AppTextInput
        editable={editable}
        borderClass={borderClass}
        placeholderTextColor={placeholderTextColor}
        className={className}
        placeholder={placeholder}
        iconLeft={iconLeft}
      />
    </Pressable>
  );
}
