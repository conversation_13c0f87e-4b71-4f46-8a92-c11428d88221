import React, { useState } from "react";
import { Modal, Pressable, Text, View, ModalProps } from "react-native";

export default function ModalBottomSheet({
  children,
  onClose,
  ...otherProps
}: React.PropsWithChildren<
  ModalProps & { onClose: (status: boolean) => void }
>) {
  return (
    <Modal transparent animationType="fade" {...otherProps}>
      <Pressable
        className="flex flex-1 bg-neutral-600/40"
        onPress={() => onClose(false)}
      />
      {/* Content */}
      <View className="bg-white px-3 py-4 overflow-hidden">{children}</View>
    </Modal>
  );
}
