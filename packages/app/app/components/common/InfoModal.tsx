import { Image, Modal, View } from "react-native";
import AppText from "./AppText";
import AppButton from "./AppButton";
import Animated, { FadeInDown } from "react-native-reanimated";
import clsx from "clsx";
import RabbleLogoInfoModal from "@assets/svg/connect/RabbleLogoInfoModal";
import React from "react";

interface Props {
  visible?: boolean;
  onRequestClose?: (status: boolean) => void;
  description?: string;
  label?: string;
  actionText?: string;
  handleAction?: () => void;
  secondaryText?: string;
  handleSecondaryAction?: () => void;
  showLogo?: boolean;
}

export default React.memo(function InfoModal({
  description,
  onRequestClose,
  visible,
  label,
  actionText = "okay",
  handleAction,
  handleSecondaryAction,
  secondaryText,
  showLogo,
}: Props) {
  return (
    <Modal
      visible={visible}
      transparent
      className="z-10"
      onRequestClose={() => onRequestClose?.(false)}
      animationType="fade"
    >
      <View className="flex flex-1 justify-center items-center bg-neutral-700/50">
        <Animated.View
          entering={FadeInDown}
          className="p-6 bg-white w-[80%] rounded-xl"
        >
          {showLogo && (
            <View className="items-center mb-4">
              <RabbleLogoInfoModal />
            </View>
          )}
          {label && (
            <AppText className="text-center mb-2 font-montserratSemiBold text-lg">
              {label}
            </AppText>
          )}
          {description && (
            <AppText className={clsx(label && "text-neutral-400")}>
              {description}
            </AppText>
          )}
          <View className="flex-row gap-2 mt-2">
            {!!secondaryText && (
              <AppButton
                variant="disabled-outline"
                size="small"
                title={secondaryText}
                className="mt-4"
                onPress={
                  handleSecondaryAction || (() => onRequestClose?.(false))
                }
                disabled={false}
              />
            )}
            <AppButton
              variant="secondary"
              size="small"
              title={actionText}
              className="mt-4"
              onPress={handleAction || (() => onRequestClose?.(false))}
            />
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
});
