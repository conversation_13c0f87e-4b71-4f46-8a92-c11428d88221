import { useState } from 'react';
import { Modal, View } from 'react-native';
import Pressable from './Pressable';

interface Props {
  handler: React.Dispatch<React.SetStateAction<boolean>>;
  visible?: boolean;
  wrapperClass: string;
}
export default function BottomSheet({
  handler,
  children,
  visible,
  wrapperClass = 'p-4 bg-white'
}: React.PropsWithChildren<Props>) {
  return (
    <Modal transparent visible={visible} animationType='fade'>
      <Pressable
        actionTag='bottom sheet'
        className='flex flex-1 bg-black/30'
        onPress={() => {
          handler(false);
        }}
      />
      <View className={wrapperClass}>{children}</View>
    </Modal>
  );
}
