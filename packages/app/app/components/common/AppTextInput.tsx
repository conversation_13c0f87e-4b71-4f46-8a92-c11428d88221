import AppText from "@components/common/AppText";
import AppFormLabel from "@components/form/AppFormLabel";
import clsx from "clsx";
import React, { useState, useCallback } from "react";
import {
  View,
  TextInput,
  TextInputProps,
  NativeSyntheticEvent,
  TextInputFocusEventData,
} from "react-native";

export interface AppTextInputProps extends TextInputProps {
  label?: string;
  wrapperClass?: string;
  labelClass?: string;
  error?: boolean;
  iconLeft?: JSX.Element;
  iconRight?: JSX.Element;
  onLabelPress?: () => void;
  labelHint?: string;
  borderClass?: string;
}

const AppTextInput = ({
  label,
  wrapperClass,
  labelClass,
  error,
  onFocus,
  onBlur,
  iconLeft,
  iconRight,
  labelHint,
  borderClass = "border border-neutral-200",
  ...otherProps
}: AppTextInputProps) => {
  const [isFocus, setIsFocus] = useState(false);

  const handleFocus = useCallback(
    (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
      setIsFocus(true);
      onFocus?.(e);
    },
    [onFocus]
  );

  const handleBlur = useCallback(
    (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
      setIsFocus(false);
      onBlur?.(e);
    },
    [onBlur]
  );

  return (
    <View className={clsx(wrapperClass)}>
      {label && (
        <AppFormLabel
          label={label}
          labelClass={labelClass}
          labelHint={labelHint}
        />
      )}
      <View
        className={clsx(
          "flex items-center  flex-row justify-between rounded-lg px-2 my-1 font-montserratRegular text-sm",
          isFocus && "border-2",
          error && "border-danger",
          borderClass
        )}
      >
        {/* Optional Left Icon */}
        {iconLeft && <View className="mr-1">{iconLeft}</View>}
        <TextInput
          className={clsx(
            "flex flex-1 flex-row py-4 text-sm tracking-wide font-montserratRegular"
          )}
          placeholderTextColor="#64748B"
          {...otherProps}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
        {/* Optional Right Icon */}
        {iconRight && <View className="mr-1">{iconRight}</View>}
      </View>
      {/* {error && <AppText className="text-xs text-danger">{error}</AppText>} */}
    </View>
  );
};

export default AppTextInput;
