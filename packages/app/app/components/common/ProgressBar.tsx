import React from "react";
import { View, StyleSheet } from "react-native";

interface ProgressBarProps {
  total: number;
  current: number;
  width?: number;
  height?: number;
  backgroundColor?: string;
  progressColor?: string;
  borderRadius?: number;
  style?: object;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  total,
  current,
  width = 150,
  height = 11,
  backgroundColor = "#B4DDFF",
  progressColor = "#0B79D3",
  borderRadius = 5,
  style = {},
}) => {
  const progress = Math.min(Math.max((current / total) * 100, 0), 100);

  return (
    <View
      style={[
        styles.container,
        {
          width,
          height,
          backgroundColor,
          borderRadius,
        },
        style,
      ]}
    >
      <View
        style={[
          styles.progress,
          {
            width: `${progress}%`,
            backgroundColor: progressColor,
            borderRadius,
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: "hidden",
    flexDirection: "row",
  },
  progress: {
    height: "100%",
  },
});

export default ProgressBar;
