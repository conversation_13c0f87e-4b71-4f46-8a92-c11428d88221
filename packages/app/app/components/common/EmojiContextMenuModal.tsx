import React, { ReactNode, useState } from "react";
import {
  Modal,
  Pressable as RNPressable,
  StyleSheet,
  View,
} from "react-native";
import Pressable from "@components/common/Pressable";
import AppText from "@components/common/AppText";
import _ from "lodash";
import EmojiPicker from "rn-emoji-keyboard";

type ContextMenuItem = {
  label: string;
  onClick?: () => void;
  color?: string;
  icon?: string;
};

type EmojiContextMenuModalProps = {
  visible: boolean;
  reactions?: string[];
  contextMenuItems: ContextMenuItem[];
  onReactionSelect: (emoji: string) => void;
  onClose: () => void;
  menuWidth?: number;
  selectedCard?: ReactNode;
  // ✅ New control props
  showReactions?: boolean;
  showContextMenu?: boolean;
};

const DEFAULT_REACTIONS = ["👍", "❤️", "😄", "😮", "🙏"];

export const EmojiContextMenuModal: React.FC<EmojiContextMenuModalProps> = ({
  visible,
  reactions = DEFAULT_REACTIONS,
  contextMenuItems: items,
  onReactionSelect,
  onClose,
  menuWidth = 300,
  showReactions = true,
  showContextMenu = true,
  selectedCard,
}) => {
  const [showEmogiKeysboard, setShowEmogiKeyboard] = useState(false);
  const shouldShowContent = showReactions || showContextMenu;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <RNPressable
        style={[
          StyleSheet.absoluteFillObject,
          { justifyContent: "center", alignItems: "center" },
        ]}
        onPress={onClose}
      >
        {/* Overlay */}
        <View
          style={[
            StyleSheet.absoluteFillObject,
            { backgroundColor: "#F5F7F9", opacity: 0.8 },
          ]}
        />

        {shouldShowContent && (
          <View className="items-center">
            {/* Emoji Picker Card */}
            {showReactions && (
              <View
                className="flex-row items-center px-0 py-2 bg-white rounded-full shadow-md"
                onStartShouldSetResponder={() => true}
              >
                {reactions.map((emoji) => (
                  <Pressable
                    key={emoji}
                    onPress={() => {
                      onReactionSelect(emoji);
                      onClose?.();
                    }}
                    className="mx-3"
                  >
                    <AppText className="text-3xl">{emoji}</AppText>
                  </Pressable>
                ))}
                <Pressable
                  onPress={() => {
                    setShowEmogiKeyboard(true);
                  }}
                  className="mx-3 items-center justify-center h-10 w-10 bg-[#E1F1FF] rounded-full"
                >
                  <AppText className="text-5xl text-[#0B79D3]">+</AppText>
                </Pressable>
              </View>
            )}

            {/* Spacer if both are shown */}
            {showReactions && showContextMenu && (
              <View style={{ height: 20 }} />
            )}

            {selectedCard && <View className={`h-auto w-[310px] mb-1`}>{selectedCard}</View>}
            {/* Context Menu Card */}
            {showContextMenu && (
              <View style={[styles.container, { width: menuWidth }]}>
                {items?.map((item, index) => (
                  <Pressable
                    key={index}
                    style={[
                      styles.menuItem,
                      index !== items.length - 1 && styles.menuItemBorder,
                    ]}
                    onPress={() => {
                      if (_.isFunction(item?.onClick)) {
                        item.onClick();
                      }
                      onClose();
                    }}
                  >
                    <AppText
                      style={[
                        styles.menuItemText,
                        { color: item.color || "#0B79D3" },
                      ]}
                    >
                      {item.label}
                    </AppText>
                    {item.icon ? (
                      <AppText style={styles.menuItemIcon}>{item.icon}</AppText>
                    ) : null}
                  </Pressable>
                ))}
              </View>
            )}
          </View>
        )}
        <EmojiPicker
          open={showEmogiKeysboard}
          onEmojiSelected={(emoji) => {
            onReactionSelect(emoji?.emoji);
            setShowEmogiKeyboard(false);
            setTimeout(() => {
              onClose();
            }, 1000);
          }}
          onClose={() => {
            setShowEmogiKeyboard(false);
          }}
        />
      </RNPressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  menuItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: "#D0E8FF",
  },
  menuItemText: {
    fontSize: 18,
  },
  menuItemIcon: {
    fontSize: 18,
    lineHeight: 24,
    textAlignVertical: "center",
  },
});
