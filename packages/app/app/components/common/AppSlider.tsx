import React, { useCallback, useEffect } from "react";
import { View, ViewStyle } from "react-native";
import {
  PanGestureHand<PERSON>,
  PanGestureHandlerGestureEvent,
} from "react-native-gesture-handler";
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
  interpolate,
  Extrapolate,
} from "react-native-reanimated";
import clsx from "clsx";

export interface AppSliderProps {
  /** Minimum value of the slider */
  min?: number;
  /** Maximum value of the slider */
  max?: number;
  /** Step size for snapping */
  step?: number;
  /** Initial value */
  value?: number;
  /** Callback when value changes */
  onValueChange?: (value: number) => void;
  /** Width of the slider track */
  width?: number;
  /** Height of the slider track */
  trackHeight?: number;
  /** Size of the thumb */
  thumbSize?: number;
  /** Color of the active track */
  activeTrackColor?: string;
  /** Color of the inactive track */
  inactiveTrackColor?: string;
  /** Color of the thumb */
  thumbColor?: string;
  /** Whether to enable haptic feedback */
  hapticFeedback?: boolean;
  /** Custom style for the container */
  style?: ViewStyle;
  /** Custom class name for styling */
  className?: string;
  /** Whether the slider is disabled */
  disabled?: boolean;
}

const AppSlider: React.FC<AppSliderProps> = ({
  min = 0,
  max = 100,
  step = 1,
  value = min,
  onValueChange,
  width = 300,
  trackHeight = 4,
  thumbSize = 24,
  activeTrackColor = "#1E90FF",
  inactiveTrackColor = "#E5E5E5",
  thumbColor = "#1E90FF",
  hapticFeedback = true,
  style,
  className,
  disabled = false,
}) => {
  // Shared values for animations
  const translateX = useSharedValue(0);
  const isPressed = useSharedValue(false);

  // Calculate initial position based on value
  const getPositionFromValue = useCallback(
    (val: number) => {
      "worklet";
      return ((val - min) / (max - min)) * (width - thumbSize);
    },
    [min, max, width, thumbSize]
  );

  // Calculate value from position
  const getValueFromPosition = useCallback(
    (position: number) => {
      "worklet";
      const percentage = position / (width - thumbSize);
      const rawValue = min + percentage * (max - min);

      // Snap to step
      const steppedValue = Math.round(rawValue / step) * step;

      // Clamp to bounds
      return Math.max(min, Math.min(max, steppedValue));
    },
    [min, max, step, width, thumbSize]
  );

  // Initialize position based on initial value
  useEffect(() => {
    translateX.value = getPositionFromValue(value);
  }, [value, getPositionFromValue]);

  // Callback to notify value change
  const notifyValueChange = useCallback(
    (newValue: number) => {
      if (onValueChange) {
        onValueChange(newValue);
      }
    },
    [onValueChange]
  );

  // Pan gesture handler
  const gestureHandler = useAnimatedGestureHandler<
    PanGestureHandlerGestureEvent,
    { startX: number }
  >({
    onStart: (_, context) => {
      if (disabled) return;

      context.startX = translateX.value;
      isPressed.value = true;
    },
    onActive: (event, context) => {
      if (disabled) return;

      const newPosition = context.startX + event.translationX;
      const clampedPosition = Math.max(
        0,
        Math.min(width - thumbSize, newPosition)
      );

      translateX.value = clampedPosition;

      // Calculate and notify value change
      const newValue = getValueFromPosition(clampedPosition);
      runOnJS(notifyValueChange)(newValue);
    },
    onEnd: () => {
      if (disabled) return;

      isPressed.value = false;

      // Snap to nearest step
      const currentValue = getValueFromPosition(translateX.value);
      const snappedPosition = getPositionFromValue(currentValue);

      translateX.value = withSpring(snappedPosition, {
        damping: 15,
        stiffness: 150,
      });
    },
  });

  // Animated styles for the thumb
  const thumbAnimatedStyle = useAnimatedStyle(() => {
    const scale = isPressed.value ? 1.2 : 1;

    return {
      transform: [
        { translateX: translateX.value },
        { scale: withSpring(scale, { damping: 15, stiffness: 150 }) },
      ],
    };
  });

  // Animated styles for the active track
  const activeTrackAnimatedStyle = useAnimatedStyle(() => {
    const activeWidth = translateX.value + thumbSize / 2;

    return {
      width: Math.max(0, activeWidth),
    };
  });

  // Animated styles for the thumb shadow (when pressed)
  const thumbShadowStyle = useAnimatedStyle(() => {
    const opacity = isPressed.value ? 0.3 : 0;
    const scale = isPressed.value ? 1.5 : 1;

    return {
      opacity: withSpring(opacity),
      transform: [
        { translateX: translateX.value },
        { scale: withSpring(scale) },
      ],
    };
  });

  return (
    <View
      style={[{ width, height: Math.max(trackHeight, thumbSize) + 10 }, style]}
      className={clsx(className)}
    >
      <View className="flex-1 justify-center">
        {/* Track container */}
        <View
          style={{
            width,
            height: trackHeight,
            backgroundColor: inactiveTrackColor,
            borderRadius: trackHeight / 2,
          }}
        >
          {/* Active track */}
          <Animated.View
            style={[
              {
                height: trackHeight,
                backgroundColor: activeTrackColor,
                borderRadius: trackHeight / 2,
              },
              activeTrackAnimatedStyle,
            ]}
          />
        </View>

        {/* Thumb shadow */}
        <Animated.View
          style={[
            {
              position: "absolute",
              width: thumbSize,
              height: thumbSize,
              borderRadius: 6, // Rounded square
              backgroundColor: thumbColor,
              opacity: 0.3,
            },
            thumbShadowStyle,
          ]}
          pointerEvents="none"
        />

        {/* Thumb */}
        <PanGestureHandler onGestureEvent={gestureHandler} enabled={!disabled}>
          <Animated.View
            style={[
              {
                position: "absolute",
                width: thumbSize,
                height: thumbSize,
                borderRadius: 6, // Rounded square
                backgroundColor: thumbColor,
                shadowColor: "#000",
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
                elevation: 5,
                justifyContent: "center",
                alignItems: "center",
              },
              thumbAnimatedStyle,
            ]}
          >
            {/* Value text inside thumb */}
            <Animated.Text
              style={[
                {
                  color: "white",
                  fontSize: thumbSize > 24 ? 12 : 10,
                  fontWeight: "bold",
                  textAlign: "center",
                },
              ]}
            >
              {Math.round(getValueFromPosition(translateX.value))}
            </Animated.Text>
          </Animated.View>
        </PanGestureHandler>
      </View>
    </View>
  );
};

// Memoization comparison function to prevent unnecessary re-renders
const areEqual = (prevProps: AppSliderProps, nextProps: AppSliderProps) => {
  return (
    prevProps.min === nextProps.min &&
    prevProps.max === nextProps.max &&
    prevProps.step === nextProps.step &&
    prevProps.value === nextProps.value &&
    prevProps.width === nextProps.width &&
    prevProps.trackHeight === nextProps.trackHeight &&
    prevProps.thumbSize === nextProps.thumbSize &&
    prevProps.activeTrackColor === nextProps.activeTrackColor &&
    prevProps.inactiveTrackColor === nextProps.inactiveTrackColor &&
    prevProps.thumbColor === nextProps.thumbColor &&
    prevProps.hapticFeedback === nextProps.hapticFeedback &&
    prevProps.disabled === nextProps.disabled &&
    prevProps.className === nextProps.className
  );
};

export default React.memo(AppSlider, areEqual);
