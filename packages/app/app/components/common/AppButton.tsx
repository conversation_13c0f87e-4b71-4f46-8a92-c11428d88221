import AppText from '@components/common/AppText';
import { onPressListener } from '@providers/TouchFeedbackProvider';
import clsx from 'clsx';
import { ActivityIndicator, Pressable, PressableProps } from 'react-native';

type Variant =
  | 'primary'
  | 'accent'
  | 'outline'
  | 'disabled'
  | 'secondary'
  | 'disabled-outline'
  | 'secondary-alt'
  | 'text'
  | 'new-primary';
type Size = 'large' | 'small' | 'xs';

export interface AppButtonProps extends PressableProps {
  variant?: Variant;
  rounded?: boolean;
  isLoading?: boolean;
  title?: string;
  size?: Size;
  textClassName?: string;
}

function Button({
  variant = 'primary',
  size = 'large',
  isLoading,
  textClassName = '',
  children,
  title,
  rounded,
  ...pressableProps
}: AppButtonProps) {
  const variants: {
    text: { [key in Variant]: string };
    btn: { [key in Variant]: string };
  } = {
    btn: {
      disabled: 'border-0 bg-[#E1F1FF]',
      outline: 'border-primary',
      primary: 'border-primary bg-primary',
      accent: 'border-[#F18A00] bg-[#F18A00]',
      secondary: 'border-accent bg-accent',
      'secondary-alt': 'border-[#e4e4e4] bg-[#e4e4e4]',
      'disabled-outline': 'bg-white border-neutral-300',
      text: 'bg-transparent text-primary border-0',
      'new-primary': 'border-primary bg-[#0B79D3]',
    },
    text: {
      disabled: 'text-[#0B79D3]',
      primary: 'text-white',
      accent: 'text-white',
      secondary: 'text-white',
      outline: 'text-primary',
      'secondary-alt': 'text-black',
      'disabled-outline': 'text-[#344054]',
      text: 'text-primary',
      'new-primary': 'text-white',
    },
  };

  const sizes: { [key in Size]: string } = {
    large: 'rounded-md py-3',
    small: 'rounded-lg py-2',
    xs: 'rounded-lg py-1',
  };

  return (
    <Pressable
      disabled={variant === 'disabled' || isLoading}
      className={clsx(
        'border rounded-md py-3 flex-1',
        variants.btn[variant],
        rounded && 'rounded-full',
        sizes[size]
      )}
      {...pressableProps}
    >
      {!isLoading ? (
        <AppText
          className={clsx(
            'font-montserratSemiBold text-center px-4',
            variants.text[variant],
            textClassName
          )}
        >
          {title || String(children)}
        </AppText>
      ) : (
        <ActivityIndicator
          size='small'
          color={
            variant === 'disabled'
              ? 'rgb(115, 115, 115)'
              : variant === 'outline'
              ? '#004987'
              : '#fff'
          }
        />
      )}
    </Pressable>
  );
}

const AppButton = onPressListener(Button);
export default AppButton;
