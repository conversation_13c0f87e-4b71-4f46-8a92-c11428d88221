import { Linking, Modal, View } from "react-native";
import useAppUpdates from "../../hooks/useAppUpdate";
import { DownloadIcon } from "lucide-react-native";
import AppText from "./AppText";
import AppButton from "./AppButton";
import Constants from "expo-constants";

export default function AppUpdateModal() {
  const { data, isUpdateAvailable } = useAppUpdates();

  const handleUpdate = () => {
    if (data?.storeUrl) Linking.openURL(data.storeUrl);
  };

  return (
    <Modal transparent visible={!!isUpdateAvailable}>
      <View className="flex flex-1 bg-neutral-200/50 justify-center items-center">
        <View className="w-[80%] bg-white rounded-3xl p-7">
          <View className="flex-row items-center">
            <AppText className="font-montserrat-semibold text-lg tracking-widest items-center">
              Update Available {Constants.expoConfig?.version}{" "}
            </AppText>
            <DownloadIcon color="gold" className="" />
          </View>

          <View className="flex-row">
            <AppButton
              title="Download"
              onPress={handleUpdate}
              className="mt-6 bg-amber-500"
            />
          </View>
        </View>
      </View>
    </Modal>
  );
}
