import clsx from "clsx";
import React, { Dispatch, SetStateAction, useState } from "react";
import { Platform, ScrollView, View } from "react-native";
import AppText from "./AppText";
import Pressable from "./Pressable";
interface Props {
  tabs: (string | JSX.Element | null)[];
  tabState: [number, Dispatch<SetStateAction<number>>];
  shadowStyle?: any;
  selectedbgClass?: string;
}
const Tabs = ({
  tabs,
  tabState: [selectedTab, setSelectedTab],
  shadowStyle,
  selectedbgClass,
}: Props) => {
  return (
    <View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          flexDirection: "row",
          alignItems: "center",
          gap: 4,
          paddingTop: 12,
        }}
      >
        {tabs.filter(Boolean).map((tab, idx) => {
          return (
            <Pressable
              actionTag="tabs"
              className={clsx(
                `rounded-full mb-4 ${
                  !selectedbgClass
                    ? "border-neutral-300 border"
                    : "bg-white px-[4px] py-[1px]"
                }`,
                selectedTab === idx &&
                  `${
                    selectedbgClass
                      ? selectedbgClass
                      : "border-[#ffcc94] bg-[#fff5e5]"
                  }`,
                typeof tab === "object" && "p-1"
              )}
              style={shadowStyle}
              key={idx}
              onPress={() => {
                setSelectedTab(idx);
              }}
            >
              {typeof tab === "string" ? (
                <AppText
                  className={clsx(
                    `px-4 py-1 ${
                      selectedTab === idx && selectedbgClass
                        ? `text-white ${
                            selectedbgClass
                              ? "text-center text-[14px]  font-[600] font-montserratRegular text-[#FFF]"
                              : ""
                          }`
                        : `text-black ${
                            selectedbgClass
                              ? "text-center text-[14px]  font-[500] font-montserratRegular text-[#0B79D3]"
                              : ""
                          }`
                    }`,
                    selectedTab === idx && "font-montserratBold"
                  )}
                >
                  {tab}
                </AppText>
              ) : (
                tab
              )}
            </Pressable>
          );
        })}
      </ScrollView>
    </View>
  );
};

export default Tabs;
