import AnimatedLottieView, { LottieViewProps } from "lottie-react-native";

export default function StreakAnimation({
  width = 150,
  height = 150,
  autoPlay = false,
  ...props
}: Omit<LottieViewProps, "source"> & { height?: number; width?: number, autoPlay?: boolean }) {
  return (
    <AnimatedLottieView
      style={{
        width,
        height,
      }}
      autoPlay={autoPlay}
      loop={autoPlay}
      source={require("../../assets/animations/streak.json")}
      {...props}
    />
  );
}
