import AnimatedLottieView, { LottieViewProps } from 'lottie-react-native';

export default function Confetti(props: Omit<LottieViewProps, 'source'>) {
  return (
    <AnimatedLottieView
      style={{
        width: '100%',
        height: '100%',
        position: 'absolute',
        zIndex: 999,
      }}
      autoPlay
      loop={false}
      source={require('../../assets/animations/confetti.json')}
      {...props}
    />
  );
}
