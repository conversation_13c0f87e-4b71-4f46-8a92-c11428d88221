import { LinearGradient } from "expo-linear-gradient";
import {
  ShimmerPlaceholderProps,
  createShimmerPlaceholder,
} from "react-native-shimmer-placeholder";

const ShimmerPlaceHolder = createShimmerPlaceholder(LinearGradient);

export default function Shimmer({
  children,
  ...otherProps
}: React.PropsWithChildren<ShimmerPlaceholderProps>) {
  return <ShimmerPlaceHolder {...otherProps}>{children}</ShimmerPlaceHolder>;
}
