import AppText from "@components/common/AppText";
import { Entypo, Ionicons } from "@expo/vector-icons";
import clsx from "clsx";
import React, { useState, useCallback, ReactElement, useMemo } from "react";
import { FlatList, Modal, ScrollView, View } from "react-native";
import _ from "lodash";

import AppTextInput, { AppTextInputProps } from "./AppTextInput";
import AppScrollView from "./AppScrollView";
import AppCheckBox from "./AppCheckbox";
import InfoModal from "./InfoModal";
import Pressable from "./Pressable";

export interface Options {
  label: string;
  value: string;
  disabled?: boolean;
  disabledHint?: string;
}
export function Option(props: Options) {
  return null;
}

export interface AppSelectProps extends AppTextInputProps {
  children: ReactElement<Options> | ReactElement<Options>[];
  onOptionSelect?: (option: string[] | string) => void;
  selectedOptions?: string[];
  multiple?: boolean;
}

export default function AppSelect({
  children,
  onOptionSelect,
  multiple,
  selectedOptions = [],
  ...otherProps
}: AppSelectProps) {
  const [showModal, setShowModal] = useState(false);
  const [showInfoModal, setShowInfoModal] = useState(false);
  const [modalDescription, setModalDescription] = useState("");

  const handleModal = useCallback(
    (status: boolean) => setShowModal(status),
    [setShowModal]
  );

  const options = Array.isArray(children)
    ? children.map((_) => _.props)
    : [children.props];

  const handleOptionSelect = ({
    value,
    label,
    disabled,
    disabledHint,
  }: Options) => {
    if (disabled) {
      if (disabledHint) {
        setShowModal(false);
        setShowInfoModal(true);
        setModalDescription(disabledHint);
      }
    } else {
      if (multiple) {
        // check if the label exists in the existing value, if yes remove it or add it
        const index = selectedOptions.findIndex((_) => _ === value);

        if (index > -1) {
          const duplicateOptions = [...selectedOptions];
          _.pullAt(duplicateOptions, index);
          onOptionSelect?.(duplicateOptions);
        } else {
          onOptionSelect?.([...selectedOptions, value]);
        }
      } else {
        onOptionSelect?.(value);
        handleModal(false);
      }
    }
  };

  return (
    <View>
      <Pressable
        className=""
        actionTag={`app select`}
        onPress={() => {
          handleModal(true);
        }}
      >
        <AppTextInput
          pointerEvents="none"
          editable={false}
          selectTextOnFocus={false}
          numberOfLines={1}
          iconRight={
            <Entypo name="chevron-small-down" size={24} color="black" />
          }
          value={selectedOptions
            .filter(Boolean)
            .map((_) => _)
            .join(", ")}
          {...otherProps}
        />
      </Pressable>
      <InfoModal
        visible={showInfoModal}
        description={modalDescription}
        onRequestClose={(status) => {
          setShowInfoModal(status);
          setShowModal(!status);
        }}
      />
      <Modal visible={showModal} transparent animationType="fade">
        <View className="flex-1">
          <Pressable
            className="flex flex-1 bg-neutral-600/40"
            onPress={() => handleModal(false)}
            actionTag="appselect-modal"
          />
          {/* Content */}
          <View className="rounded-t-xl bg-white flex-1 ">
            <AppText className="ml-6 mt-4 text-lg font-montserratMedium">
              {otherProps.label}
            </AppText>
            <FlatList
              data={options}
              keyExtractor={(item, idx) => idx.toString()}
              renderItem={({ item: option, index }) => {
                const isActive = selectedOptions.includes(option.value);

                return (
                  <Pressable
                    actionTag="appselect-item-select"
                    onPress={() => handleOptionSelect(option)}
                    className={clsx(
                      "flex flex-row items-center justify-between py-2 px-6 mb-[1px]",
                      isActive && !multiple && "bg-accent/50",
                      multiple && "justify-start"
                    )}
                  >
                    {multiple && (
                      <View>
                        <AppCheckBox active={isActive} pointerEvents="none" />
                      </View>
                    )}
                    <AppText
                      className={clsx(
                        "text-base text-left",
                        option.disabled && "text-neutral-300"
                      )}
                    >
                      {option.label}
                    </AppText>
                    {isActive && !multiple && (
                      <Ionicons name="checkmark" size={22} color="black" />
                    )}
                  </Pressable>
                );
              }}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}
