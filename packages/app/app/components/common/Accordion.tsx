import { View } from 'react-native';
import AppText from './AppText';
import Animated, { Layout } from 'react-native-reanimated';
import { useState } from 'react';
import clsx from 'clsx';
import Pressable from './Pressable';

interface Props {
  options: {
    title: string;
    content: JSX.Element;
  }[];
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export default function Accordion({ options }: Props) {
  const [selectedIndex, setSelectedIndex] = useState<number | undefined>(0);

  return (
    <View>
      {options.map((_, idx) => {
        return (
          <AnimatedPressable
            layout={Layout.duration(100)}
            className={clsx(
              'border-b border-neutral-200 overflow-hidden bg-neutral-100 shadow-md',
              idx === 0 && 'rounded-t-md',
              idx === options.length - 1 && 'rounded-b-md'
            )}
            onPress={() =>
              setSelectedIndex((_) => (_ === idx ? undefined : idx))
            }
            actionTag={_.title}
          >
            <AppText className='font-montserratMedium text-white p-2 bg-primary'>
              {_.title}
            </AppText>
            {idx === selectedIndex && (
              <Animated.View className='my-2 px-2'>{_.content}</Animated.View>
            )}
          </AnimatedPressable>
        );
      })}
    </View>
  );
}
