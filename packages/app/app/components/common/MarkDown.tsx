// @ts-ignore
import MD from "react-native-markdown-package";
import { Image, Linking, View } from "react-native";
import { ImageZoom } from "@likashefqet/react-native-image-zoom";
import AppText from "./AppText";

const markdownStyles = {
  collectiveMd: {
    text: {
      fontFamily: "Montserrat_400Regular",
      lineHeight: 21,
      textAlign: "left",
      fontSize: 14,
      zIndex: -10,
    },
    heading1: {
      fontSize: 32,
      fontWeight: "bold",
      fontFamily: "Montserrat_700Bold",
      zIndex: -10,
    },
    heading2: {
      fontSize: 28,
      fontWeight: "bold",
      fontFamily: "Montserrat_700Bold",
      zIndex: -10,
    },
    heading3: {
      fontSize: 20,
      fontWeight: "bold",
      fontFamily: "Montserrat_700Bold",
      zIndex: -10,
    },
    heading4: {
      fontSize: 16,
      fontWeight: "bold",
      fontFamily: "Montserrat_700Bold",
      zIndex: -10,
    },
    heading5: {
      fontSize: 14,
      fontWeight: "bold",
      fontFamily: "Montserrat_700Bold",
      zIndex: -10,
    },
    heading6: {
      fontSize: 12,
      fontWeight: "bold",
      fontFamily: "Montserrat_700Bold",
      zIndex: -10,
    },
    strong: {
      fontFamily: "Montserrat_700Bold",
      zIndex: -10,
    },
    paragraph: {
      lineHeight: 21,
      fontFamily: "Montserrat_400Regular",
      zIndex: -10,
    },
    listItem: {
      fontFamily: "Montserrat_400Regular",
      lineHeight: 21,
      zIndex: -10,
    },
    list: {
      fontFamily: "Montserrat_400Regular",
      lineHeight: 21,
      color: "red",
      zIndex: -10,
    },
    subList: {
      fontFamily: "Montserrat_400Regular",
      lineHeight: 21,
      color: "red",
      zIndex: -10,
    },
    listItemNumber: {
      fontFamily: "Montserrat_400Regular",
      lineHeight: 21,
      color: "red",
      zIndex: -10,
    },
    listItemBullet: {
      fontFamily: "Montserrat_700Bold",
      lineHeight: 21,
      zIndex: -10,
    },
  },
};

export default function MarkDown({ content }: { content: string }) {
  const markdownRules = {
    image: {
      react: (node, output, state) => {
        // Return your custom image component

        if (!node.target) return null;
        return (
          <View className="w-full h-[200px]">
            <ImageZoom
              uri={node.target}
              doubleTapScale={3}
              isSingleTapEnabled
              isDoubleTapEnabled
              style={{
                width: "100%",
                height: 200,
                backgroundColor: "black",
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 999,
              }}
              resizeMode="cover"
            />
            <AppText>TESTING</AppText>
          </View>
        );
      },
    },
  };

  return (
    <MD
      rules={markdownRules}
      styles={markdownStyles.collectiveMd}
      onLink={(url: string) => Linking.openURL(url)}
    >
      {content}
    </MD>
  );
}
