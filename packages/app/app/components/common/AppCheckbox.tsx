import AppText from '@components/common/AppText';
import { Entypo } from '@expo/vector-icons';
import clsx from 'clsx';
import { PressableProps, View } from 'react-native';
import Pressable from './Pressable';
export interface AppCheckboxProps extends PressableProps {
  active?: boolean;
  onSelect?: (data: boolean) => void;
  label?: JSX.Element | string;
  error?: string;
  large?: boolean;
}

export default function AppCheckBox({
  active,
  onSelect,
  label,
  error,
  large,
  ...otherProps
}: AppCheckboxProps) {
  return (
    <>
      {error && <AppText className='text-xs text-danger'>{error}</AppText>}
      <Pressable
        onPress={() => onSelect?.(!active)}
        className='flex flex-row'
        actionTag={String(label)}
        {...otherProps}
      >
        <View
          className={clsx(
            'w-5 h-5 flex justify-center items-center border-2 border-primary bg-white border border-[#004987] bg-white rounded-md mt-1 mr-2',
            active && 'bg-primary',
            large && `w-9 h-9`,
            !active && large && 'border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]'
          )}
        >
          {active && <Entypo name='check' size={12} color='white' />}
        </View>
        <View className='flex flex-row flex-1'>
          {label && typeof label === 'string' ? (
            <AppText className=''>{label}</AppText>
          ) : (
            label
          )}
        </View>
      </Pressable>
    </>
  );
}
