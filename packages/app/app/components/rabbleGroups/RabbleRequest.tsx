import LogoSimple from "@assets/svg/LogoOutline";
import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import { getTimeDifference, imageKit } from "@utils/index";
import { View } from "react-native";
import { Image as CacheImage } from "react-native-expo-image-cache";

type RabbleRequestProps = {
  profilePicture?: string;
  fullname: string;
  username: string;
  createdAt: Date;
  onPress: (accept: "accepted" | "rejected") => void;
};

export const RabbleRequest = ({
  fullname,
  username,
  createdAt,
  profilePicture,
  onPress,
}: RabbleRequestProps) => {
  return (
    <View className="mb-3">
      <View className="flex-row gap-4">
        {profilePicture ? (
          <CacheImage
            style={{ width: 40, height: 40, borderRadius: 100 }}
            uri={imageKit({
              imagePath: profilePicture,
              transform: ["w-500"],
            })}
          />
        ) : (
          <LogoSimple width={36} height={36} />
        )}
        <View className="flex-1">
          <AppText>{fullname}</AppText>
          <AppText className="text-xs text-neutral-400">{username}</AppText>
        </View>
        <AppText>{getTimeDifference(createdAt)}</AppText>
      </View>
      <View className="flex-row gap-3 mt-1 justify-between">
        <AppButton
          title="accept"
          onPress={() => onPress("accepted")}
          size="small"
        />
        <AppButton
          title="reject"
          size="small"
          className="bg-neutral-300 border-0"
          onPress={() => onPress("rejected")}
        />
      </View>
    </View>
  );
};
