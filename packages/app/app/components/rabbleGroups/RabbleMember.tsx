import AdminIcon from "@assets/svg/AdminIcon";
import LogoSimple from "@assets/svg/LogoOutline";
import AppText from "@components/common/AppText";
import BottomSheet from "@components/common/BottomSheet";
import Pressable from "@components/common/Pressable";
import SectionLink from "@components/profile/ProfileSectionItem";
import { imageKit } from "@utils/index";
import { DeleteIcon } from "lucide-react-native";
import { useState } from "react";
import { View } from "react-native";
import { Image as CacheImage } from "react-native-expo-image-cache";

type MemberProps = {
  profilePicture?: string;
  fullname: string;
  username: string;
  isAdmin?: boolean;
  userId: string;
  role: string;
  loggedInUserId: string;
  loggedInUserMemberRole: string;
  promoteDemoteUser: (user: string, roleToUpdate: "admin" | "user") => void;
  kickUser: (user: string) => void;
  isMember: boolean;
};

export function RabbleMember({
  fullname,
  username,
  isAdmin,
  profilePicture,
  userId,
  role,
  loggedInUserId,
  loggedInUserMemberRole,
  isMember,
  kickUser,
  promoteDemoteUser,
}: MemberProps) {
  const [BottomSheetOpen, setBottomSheetOpen] = useState(false);
  return (
    <View className="z-10">
      <BottomSheet
        visible={
          isMember &&
          BottomSheetOpen &&
          userId !== loggedInUserId &&
          role !== "owner" &&
          !!userId
        }
        handler={setBottomSheetOpen}
      >
        {/* <AppText className='font-montserratSemiBold'>user setting</AppText> */}
        <View className="mt-3">
          {/* If isAdmin and not himself promote or demote user */}
          {(loggedInUserMemberRole === "owner" ||
            loggedInUserMemberRole === "admin") && (
            <SectionLink
              title="Promote/Demote User"
              icon={<AdminIcon />}
              onPress={() => {
                promoteDemoteUser(userId, role === "user" ? "admin" : "user");
                setBottomSheetOpen(false);
              }}
            />
          )}
          {/* Kick user if not admin */}
          {(loggedInUserMemberRole === "owner" ||
            (loggedInUserMemberRole === "admin" && role === "user")) && (
            <SectionLink
              title="Kick User"
              icon={<DeleteIcon />}
              onPress={() => {
                kickUser(userId);
                setBottomSheetOpen(false);
              }}
            />
          )}
        </View>
      </BottomSheet>
      <Pressable
        actionTag="rabble details bottom sheet"
        onPress={() => setBottomSheetOpen(true)}
        className="flex-row gap-4 mb-3"
      >
        {profilePicture ? (
          <CacheImage
            style={{ width: 40, height: 40, borderRadius: 100 }}
            uri={imageKit({
              imagePath: profilePicture,
              transform: ["w-500"],
            })}
          />
        ) : (
          <LogoSimple width={36} height={36} />
        )}
        <View>
          <AppText>{fullname}</AppText>
          <AppText className="text-xs text-neutral-400">{username}</AppText>
        </View>
        {isAdmin && <AdminIcon />}
      </Pressable>
    </View>
  );
}
