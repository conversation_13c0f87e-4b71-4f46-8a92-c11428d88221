import Pressable from "@components/common/Pressable";
import { RouterOutput } from "../../../../shared";
import { Image } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import AppText from "@components/common/AppText";
import { Globe2, Lock } from "lucide-react-native";
import { StyleSheet, View } from "react-native";

export function RabbleGroupListItem({
  privacy,
  groupName,
  groupDescription,
  image,
  _id,
  onPress,
  isCareGiverManagedGroup,
}: RouterOutput["rabbleGroups"]["getUserCreatedGroups"][number] & {
  onPress: (group: string) => void;
  isCareGiverManagedGroup?: boolean;
}) {
  return (
    <Pressable
      actionTag="group details"
      className="w-[48%] rounded-lg overflow-hidden shadow-lg shadow-neutral-400 bg-white"
      onPress={() => onPress(_id.toString())}
    >
      {image && (
        <Image
          style={styles.groupImage}
          uri={imageKit({
            imagePath: image,
            transform: ["w-500"],
          })}
        />
      )}

      {/* Content */}
      <View className="p-3">
        <AppText className="text-sm font-montserratMedium" numberOfLines={1}>
          {groupName}
        </AppText>
        <View>
          <View className="flex-row items-center gap-2">
            {privacy === "private" ? (
              <Lock color="#aeaeae" size={14} />
            ) : (
              <Globe2 color="#aeaeae" size={14} />
            )}
            <AppText>{privacy}</AppText>
          </View>

          <AppText className="text-xs" numberOfLines={2}>
            {groupDescription}
          </AppText>
        </View>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  groupImage: { width: "100%", height: 110 },
});
