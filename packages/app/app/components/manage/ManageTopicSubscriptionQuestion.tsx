import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import clsx from "clsx";
import { View } from "react-native";
import ManageTextParser from "./ManageTextParser";
import { QuestionTypes } from "../../../../shared/types/manage";

type Props = {
  questionIndex: number;
  question: string;
  optional?: boolean;
  questionId: string;
  questionType?: string;
  onOptionSelect: (data: {
    questionId: string;
    optionId: string;
    answerText: string;
  }) => void;
  options: {
    option: string;
    active?: boolean;
    optionId: string;
  }[];
};

export default function ManageTopicSubscriptionQuestion({
  options,
  question,
  questionIndex,
  onOptionSelect,
  questionId,
  optional,
  questionType,
}: Props) {
  return (
    <View className="p-4 bg-white rounded-2xl shadow-md">
      <View className="flex-row items-center justify-between">
        <View className="w-6 h-6 bg-[#f5f7f9] justify-center items-center rounded-full mb-2">
          <AppText className="text-foundation text-xs">{questionIndex}</AppText>
        </View>
        {optional && (
          <AppText className="text-foundation text-xs uppercase">
            optional
          </AppText>
        )}
      </View>

      <ManageTextParser
        fontSize={18}
        boldFontSize={18}
        boldFontColor="#000"
        color="#000"
        centerText={false}
      >
        {question}
      </ManageTextParser>
      {questionType === QuestionTypes.MultiSelect && (
        <View className={"mt-4"}>
          <AppText>{"(Select all that apply)"}</AppText>
        </View>
      )}
      <View style={{ gap: 8, marginTop: 32 }}>
        {options.map((o, idx) => {
          return (
            <Pressable
              onPress={() => {
                onOptionSelect({
                  answerText: o.option,
                  optionId: String(o.optionId),
                  questionId,
                });
              }}
              key={idx}
              className={clsx(
                "rounded-lg overflow-hidden border-2 border-white",
                o.active && "border-primary"
              )}
            >
              <View className="p-3  bg-foundation-light">
                <AppText className="text-sm font-montserratSemiBold text-[#004987] text-center">
                  {o.option}
                </AppText>
              </View>
            </Pressable>
          );
        })}
      </View>
    </View>
  );
}
