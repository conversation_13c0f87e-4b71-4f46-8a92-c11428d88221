import React, {
  useState,
  useRef,
  useMemo,
  useImperativeHandle,
  forwardRef,
} from "react";
import { View, Text, Animated, PanResponder, StyleSheet } from "react-native";

type Props = {
  disabled: boolean;
  onChange: Function;
};

const ThreeStateSwipeSwitch = ({ onChange, disabled }: Props, ref: any) => {
  const [state, setState] = useState<boolean | null>(null); // Default state is null
  const position = useRef(new Animated.Value(0)).current; // Position of the toggle

  useImperativeHandle(ref, () => ({
    reset: () => {
      position.setValue(0);
      setState(null);
    },

    set: (value: boolean) => {
      setState(value);
      position.setValue(value ? 30 : -30);
    },
  }));

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (evt, gestureState) => {
        // Move the toggle within the bounds of -50 to 50
        const newX = gestureState.dx;
        if (newX >= -50 && newX <= 50) {
          position.setValue(newX);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        const newX = gestureState.dx;

        let newState;
        if (newX > 25) {
          newState = true; // Yes
        } else if (newX < -25) {
          newState = false; // No
        } else {
          newState = null; // Null
        }

        // Animate to the new state position
        Animated.spring(position, {
          toValue: newState === true ? 30 : newState === false ? -30 : 0,
          useNativeDriver: true,
        }).start();

        setState(newState);
        onChange(newState); // Propagate the new state up
      },
    })
  ).current;

  const text = useMemo(() => {
    return state === true ? "Yes" : state === false ? "No" : "";
  }, [state]);

  return (
    <View style={styles.container}>
      <View style={styles.switchContainer}>
        {!text && (
          <Text className="absolute text-[10px] left-[8px] top-[8px]">No</Text>
        )}
        <Animated.View
          style={[
            styles.toggle,
            { transform: [{ translateX: position }] },
            { backgroundColor: disabled ? "green" : "#FF8E1C" },
          ]}
          {...(!disabled ? panResponder.panHandlers : {})}
        >
          <Text className="text-white">{text ? text : "<---->"}</Text>
        </Animated.View>
        {!text && (
          <Text className="absolute text-[10px] right-[8px] top-[8px]">
            Yes
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    width: "100%",
    marginTop: 5,
  },
  label: {
    marginBottom: 10,
    fontWeight: "bold",
    color: "#000",
  },
  switchContainer: {
    width: 110,
    height: 25,
    position: "relative",
    backgroundColor: "white",
    borderRadius: 20,
    justifyContent: "center",
    shadowColor: "#004987",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  toggle: {
    width: 50,
    height: 25,
    borderRadius: 20,
    position: "absolute",
    top: 0,
    left: 30, // Default position in the middle
    justifyContent: "center",
    alignItems: "center",
    elevation: 3, // Add some shadow for a better look
  },
});

export default forwardRef(ThreeStateSwipeSwitch);
