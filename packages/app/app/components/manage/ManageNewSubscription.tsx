import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { PlusCircle } from "lucide-react-native";
import { View } from "react-native";

export const ManageNewSubscription = () => {
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();

  const addTopics = () => navigation.navigate("TopicsListScreen");

  return (
    <View className="flex flex-1 justify-center items-center p-4 m-4 bg-white rounded-2xl shadow-md">
      <Pressable
        onPress={addTopics}
        actionTag="Add Topic"
        className="w-10 h-10 bg-[#e6edf3] justify-center items-center rounded-full"
      >
        <PlusCircle color="#5485af" size={25} />
      </Pressable>
      <AppText className="text-4xl font-montserratSemiBold text-center mt-2 text-[#00284a]">
        Subscribe to
      </AppText>
      <AppText className="text-4xl font-montserratSemiBold text-center mt-2 text-[#00284a]">
        your first topic
      </AppText>
      <AppText className="text-center text-[#00284A] mt-2">
        You're not subscribed to any topics. Tap the button below to subscribe
        to your first topic.
      </AppText>

      <View className="flex-row self-end absolute bottom-0 left-0 right-0">
        <AppButton
          title="Add Topic"
          className="bg-[#e6edf3] border-transparent self-end mx-4 mb-4"
          textClassName="text-primary"
          onPress={addTopics}
        />
      </View>
    </View>
  );
};
