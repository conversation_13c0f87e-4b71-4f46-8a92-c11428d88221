import AppText from "@components/common/AppText";
import { FormProvider, useForm } from "react-hook-form";
import { Modal, Text, View } from "react-native";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import AppButton from "@components/common/AppButton";
import AppFormDatePicker from "@components/form/AppFormDatePicker";
import _ from "lodash";

import {
    Outcomes,
    SymtomTracker,
    Triggers,
} from "../../../../shared/types/manage";
import React, { useEffect, useMemo } from "react";
import { trpc } from "@providers/RootProvider";
import AppFormAutoComplete from "@components/form/AppFormAutocomplete";
import useDebounceValue from "@hooks/useDebounceValue";
import { useSession } from "@hooks/persistUser";
import { AutocompleteDropdownContextProvider } from "react-native-autocomplete-dropdown";
import { zString } from "@utils/schema";
import AppFormSelect from "@components/form/AppFormSelect";
import { Option } from "@components/common/AppSelect";

type Props = {
    visible: boolean;
    handleResult: Function;
};


export default function ConfirmationModal({ visible, handleResult }: Props) {

    return (
        <Modal transparent visible={visible}>
            <View
                className="flex flex-1 justify-center items-center"
                style={{
                    shadowColor: '#004987',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.2,
                    shadowRadius: 4,
                    elevation: 5,
                }}
            >
                <View className="w-[90%] bg-white rounded-3xl p-7 flex justify-start items-center mt-4">
                    <Text>The action will be irreversible, do you wish to continue</Text>
                    <View className="flex flex-row justify-between mt-4">
                        <AppButton
                            title="Yes"
                            variant="accent"
                            size="xs"
                            className="mx-2"
                            onPress={() => handleResult(true)}
                        />
                        <AppButton
                            title="No"
                            variant="disabled-outline"
                            size="xs"
                            className="mx-2"
                            onPress={() => handleResult(false)}
                        />
                    </View>
                </View>
            </View>
        </Modal>
    );
}
