import { View } from "react-native";
import { RouterOutput } from "../../../../shared";
import Pressable from "@components/common/Pressable";
import clsx from "clsx";
import AppText from "@components/common/AppText";

export default function ManageHomeScreenUserTopicBadges({
  userTopics,
  selectedTopic,
  onTopicSelect,
}: {
  userTopics: RouterOutput["manage"]["getUserTopics"];
  selectedTopic?: string;
  onTopicSelect?: (topicId: string) => void;
}) {
  return (
    <View className="flex-row flex-wrap" style={{ gap: 8 }}>
      {userTopics.map((topic) => (
        <Pressable
          onPress={() => onTopicSelect?.(String(topic.topic._id))}
          key={String(topic._id)}
          className={clsx(
            "px-4 py-2 rounded-full bg-white shadow-md shadow-primary-light/10",
            selectedTopic === String(topic.topic?._id) &&
              "border-primary border"
          )}
        >
          <AppText className="text-foundation font-montserratSemiBold uppercase">
            {topic.topic.name}
          </AppText>
        </Pressable>
      ))}
    </View>
  );
}
