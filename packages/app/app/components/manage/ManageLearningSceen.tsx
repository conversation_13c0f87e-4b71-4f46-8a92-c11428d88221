import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import _ from "lodash";
import React, { useEffect, useMemo, useState } from "react";
import { Dimensions, Image, Platform, Text, View } from "react-native";
import ManageTextParser from "./ManageTextParser";
import AppButton from "@components/common/AppButton";
import { X } from "lucide-react-native";
import Screen from "@components/common/Screen";
import AppScrollView from "@components/common/AppScrollView";
import ProgressBar from "@components/common/ProgressBar";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import BookIcon from "@assets/svg/BookIcon";
import withPressAnimation from "@components/common/AnimateButton";
import { trpc } from "@providers/RootProvider";
import AsyncStorage from "@react-native-async-storage/async-storage";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";

const AnimatedAppButton = withPressAnimation(AppButton);

const SCREEN_WIDTH = Dimensions.get("screen").width;
const COMPONENT_WIDTH = SCREEN_WIDTH - 16 * 2;
const learningQuestionsNew = [
  {
    _id: "67979d8eccb0470b5551ab3a",
    question: "Learn about tracking asthma zones",
    type: "select",
    externalId: "111-21",
    options: [
      {
        _id: "67979df5d9127e2de2ebf4d9",
        value: "Green Zone 😊",
        description: `What is the Green Zone for Asthma?
The Green Zone means your asthma is doing great. You can breathe easily, and you're not having any trouble with your chest, coughing, or wheezing.
Here's what it looks like when you're in the Green Zone:
    • You can run and play without coughing or\n      getting tired too fast
    • You're not waking up at night because of\n      your asthma
    • You don't need your rescue inhaler (the one\n      that helps you breathe during an attack)
    • Your breathing feels normal and comfortable
When you're in the Green Zone, you:
    • Take your daily asthma medicine (the controller)\n      just like your doctor says—even if you feel good!
    • Keep doing your normal activities, like school,\n      sports, and playing with friends
Think of it like a traffic light:
    • 🟢 Green = Go! Everything is good
    • 🟡 Yellow = Slow down. Pay attention to your asthma
    • 🔴 Red = Stop! You need help right away
Staying in the Green Zone is the goal! It means your asthma is under control and you're feeling your best.`,
        externalId: "111-21-1",
        color: "#25BA60",
      },
      {
        _id: "67979df5d9127e2de2ebf4da",
        value: "Yellow Zone 😐",
        description: `The Yellow Zone means your asthma is acting up a little. You might not feel your best, and breathing is a bit harder than usual. It's a warning to slow down and take action so things don't get worse.
Here's what it looks like when you're in the Yellow Zone:
    • You're coughing, wheezing, or your chest\n      feels tight
    • You're having a harder time breathing than\n      normal
    • You're waking up at night because of asthma
    • You can't keep up with playing, running, or\n      other activities like you usually do
    • You need to use your rescue inhaler (the\n      quick-relief one)
When you're in the Yellow Zone, you:
    • Use your rescue inhaler just like your doctor\n      or asthma plan says
    • Let a grown-up know how you're feeling
    • Rest and avoid things that make your asthma\n      worse (like smoke, dust, or running too much)
Remember the traffic light:
    • 🟢 Green = Go! You're feeling good
    • 🟡 Yellow = Slow down! Asthma is getting worse
    • 🔴 Red = Stop! Get help now
The Yellow Zone is your body's way of saying, "Hey, I need a little help!" If you act early, you can get back to the Green Zone faster.`,
        externalId: "111-21-2",
        color: "#FF8E1C",
      },
      {
        _id: "67979df5d9127e2de2ebf4db",
        value: "Danger Zone 😟",
        description: `The Red Zone means asthma is very serious right now. Breathing is hard, and medicine may not be working. It's time to get help fast.
Here are signs you're in the Red Zone:
    • You feel very short of breath, even when\n      resting
    • You are coughing a lot or your chest feels\n      tight
    • You are wheezing or making it hard to speak\n      clearly
    • You used your rescue inhaler, but it's not\n      helping
    • Your lips or fingernails may look blue or gray
What to do:
    • Use your quick-relief inhaler right away
    • Tell someone or call for help
    • Call 911 or go to the emergency room if you\n      are not getting better quickly
Think of a stoplight:
    • 🟢 Green = Everything is good
    • 🟡 Yellow = Asthma is starting to get worse
    • 🔴 Red = Danger. Get help now
The Red Zone is an emergency. Act fast to stay safe.`,
        externalId: "111-21-3",
        color: "#E54D4D",
      },
    ],
  },
];

function ManageLearningScreen({}: {}) {
  const navigation = useNavigation<RootNavigationProp>();
  const route = useRoute<RouteProp<ManageNavParams, "ManageLearningScreen">>();
  const [expandedZone, setExpandedZone] = useState<number | null>(null);
  const { user } = useSession();

  const [topicId, setTopicId] = useState<string>("");
  const source = route?.params?.source; // 'onboarding' or 'daily'

  useEffect(() => {
    const getTopicId = async () => {
      const id =
        route?.params?.topicId ||
        (await AsyncStorage.getItem("selectedTopic")) ||
        "";
      setTopicId(id);
    };
    getTopicId();
  }, [route?.params?.topicId]);

  console.log("Previous screen:", source);

  const { data: topics } = trpc.manage.getTopics.useQuery(
    { topicIds: [topicId] },
    {
      enabled: !!topicId,
    }
  );

  // console.log("topicId", topicId, topics);
  // Use learningQuestionsNew instead of trpc query
  const zones = useMemo(() => {
    //topics[0]?.learningQuestions[0]?.options
    return learningQuestionsNew[0]?.options.map((option) => ({
      title: option.value,
      color: option.color,
      content: option.description,
    }));
  }, [topics]);

  const toggleZone = (index: number) => {
    mixpanel.trackEvent(
      "Asthama learning completed (Step 3)(Account Step)",
      {
        email_or_phone: user?.email || user?.contact?.phone || "",
        asthma_status: learningQuestionsNew[0]?.options[index]?.value || "",
        asthama_learning_option:
          learningQuestionsNew[0]?.options[index]?.value || "",
      },
      user?._id?.toString(),
      "v2"
    );
    setExpandedZone(expandedZone === index ? null : index);
  };

  return (
    <Screen>
      <AppScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: source === "onboarding" ? 100 : 20,
        }}
        showsVerticalScrollIndicator={false}
      >
        <View className="flex-row items-center mt-8">
          <X
            color={"#004987"}
            size={22}
            className="ml-4"
            onPress={() => navigation.goBack()}
          />
          <ProgressBar
            total={6}
            current={5}
            style={{ marginLeft: 16, flex: 1, marginRight: 16 }}
          />
        </View>
        <View className="mt-4">
          <Image
            className="w-[178px] h-[40px] ml-4"
            source={require("../../assets/img/logo_myRabble.png")}
          />
        </View>
        <View
          style={{
            width: SCREEN_WIDTH,
            alignItems: "center",
            marginTop: 72,
            paddingBottom: 20,
          }}
        >
          <View
            className={`bg-white rounded-2xl p-4 shadow-md shadow-primary/10`}
            style={
              Platform.OS !== "ios"
                ? {
                    shadowColor: "#000000",
                    shadowOffset: { width: 10, height: 10 },
                    shadowOpacity: 6,
                    shadowRadius: 6,
                    elevation: 8,
                    width: COMPONENT_WIDTH,
                    minHeight: "auto",
                  }
                : {
                    shadowColor: "#000000",
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.2,
                    shadowRadius: 6,
                    elevation: 8,
                    width: COMPONENT_WIDTH,
                    minHeight: "auto",
                  }
            }
          >
            <View className="flex justify-center items-center mb-4">
              <View className="flex-row justify-center">
                <BookIcon />
              </View>
              <AppText className="uppercase text-center text-foundation font-montserratSemiBold color-[#FF8E1C]">
                {"ASTHMA LEARNING"}
              </AppText>
            </View>
            <View className="mb-6">
              <ManageTextParser fontSize={28} boldFontSize={28}>
                {"Learn about tracking asthma zones"}
              </ManageTextParser>
            </View>
            {_.map(zones, (zone, idx) => (
              <View key={idx} className="mb-4">
                <Pressable onPress={() => toggleZone(idx)}>
                  <View
                    className={[
                      `w-full rounded-xl relative flex flex-row justify-center items-center p-3 border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]`,
                      expandedZone === idx ? "bg-[#E1F1FF]" : "",
                    ].join(" ")}
                  >
                    <AppText
                      style={{ color: zone.color }}
                      className="text-base font-montserratSemiBold flex-1 text-center"
                    >
                      {zone.title}
                    </AppText>
                    <Text className="text-[#004987] text-[22px] absolute right-3">
                      {expandedZone === idx ? "-" : "+"}
                    </Text>
                  </View>
                </Pressable>
                {expandedZone === idx && (
                  <View className="p-4 bg-white rounded-xl mt-2 border border-[#B4DDFF] relative">
                    <View
                      style={{
                        width: 8,
                        backgroundColor: "#B4DDFF",
                        position: "absolute",
                        right: -9,
                        top: 16,
                        bottom: 16,
                        borderTopRightRadius: 5,
                        borderBottomRightRadius: 5,
                        borderRightWidth: 1,
                        borderTopWidth: 1,
                        borderBottomWidth: 1,
                        borderColor: "#B4DDFF",
                      }}
                    />
                    <AppText
                      style={{
                        color: "#004987",
                        fontFamily: "Montserrat",
                        fontSize: 12,
                        fontWeight: "600",
                        lineHeight: 16,
                      }}
                    >
                      {zone.content}
                    </AppText>
                  </View>
                )}
              </View>
            ))}
          </View>
        </View>
      </AppScrollView>
      {source === "onboarding" && (
        <View className="flex flex-row items-center justify-between mx-4 mb-4">
          <AnimatedAppButton
            btnContainer="flex flex-row p-1"
            title="CONTINUE"
            variant={expandedZone === null ? "disabled" : "new-primary"}
            className="flex-1 rounded-full"
            textClassName="text-[21px]"
            style={
              expandedZone !== null
                ? {
                    shadowColor: "#003366",
                    shadowOffset: { width: 0, height: 3 },
                    shadowOpacity: 1,
                    shadowRadius: 1,
                    elevation: 5,
                  }
                : {}
            }
            disabled={expandedZone === null}
            onPress={() => {
              if (expandedZone !== null) {
                if (source === "onboarding") {
                  navigation.navigate("OnboardingDailyCheckIn");
                } else {
                  navigation.goBack();
                }
              }
            }}
          />
        </View>
      )}
    </Screen>
  );
}

export default React.memo(ManageLearningScreen);
