import AppText from '@components/common/AppText';
import Pressable from '@components/common/Pressable';
import { useNavigation } from '@react-navigation/native';
import clsx from 'clsx';
import { ChevronLeft } from 'lucide-react-native';
import { styled } from 'nativewind';
import { View, ViewProps } from 'react-native';

const Wrapper = styled(View, 'flex flex-row items-center justify-between');

export default function ManageHeader({
  className,
  onCancelPress,
  ...rest
}: ViewProps & { onCancelPress?: () => void }) {
  const navigation = useNavigation();

  return (
    <Wrapper {...rest}>
      <Pressable
        onPress={() => navigation.goBack()}
        className='flex-row items-center'
      >
        <ChevronLeft color={'#004987'} size={22} className='-ml-2' />
        <AppText className='font-montserratMedium text-base text-primary'>
          Back
        </AppText>
      </Pressable>
      <Pressable
        onPress={() => (onCancelPress ? onCancelPress() : navigation.goBack())}
      >
        <AppText className='font-montserratMedium text-base text-primary'>
          Cancel
        </AppText>
      </Pressable>
    </Wrapper>
  );
}
