import AppText from '@components/common/AppText';
import Pressable from '@components/common/Pressable';


import {
  Image,
  View,
} from 'react-native';
import { Entypo } from '@expo/vector-icons';
import Chips from '@components/common/Chips';
import { StyleSheet } from 'react-native';

const numOflineToShow = 2;

type Item = {
    id: string;
    value: string | undefined;
  };
  

type Props = {
    title: string;
    chips: Array<Item>;
    onClick?: Function;
    desciption?: string;
    children?: any;
}

export default function ManageCard({ title, chips, desciption, children, onClick  }: Props) {

  return (
    <Pressable
      className='relative bg-white h-[100px] rounded-lg p-4 mt-2 mx-2'
      style={{
        elevation: 4,
        shadowColor: '#004987',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      }}
      onPress={() => onClick?.()}
    >
      <View className='flex flex-row'>
        {children}
        <View className='flex flex-col justify-start max-w-[80%]'>
          <AppText
            numberOfLines={1}
            ellipsizeMode='tail'
            className='text-base text-black font-montserratSemiBold overflow-hidden whitespace-nowrap'
          >
            {title}
          </AppText>
          <View className='flex flex-row my-2'>
            {chips.map((chip) => <View key={chip.id} style={styles.box} className='flex items-center justify-center mr-2 rounded-3xl w-[100px]'><AppText className='text-[12px] font-normal'>{chip.value}</AppText></View>)}
          </View>
          {desciption && <AppText
            numberOfLines={1}
            ellipsizeMode='tail'
            className='text-base text-primary font-montserratSemiBold overflow-hidden whitespace-nowrap'
          >
            {desciption}
          </AppText>}
        </View>
        <View className='flex flex-1 flex-row justify-end items-center'>
            <Entypo name='chevron-small-right' size={24} />
        </View>
       </View>
    </Pressable>
  );
}


const styles = StyleSheet.create({
    box: {
      backgroundColor: '#fff', // Example background color
      shadowColor: 'rgba(0, 73, 135, 0.15)',
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 1,
      shadowRadius: 11,
      // Use the next property for Android
      elevation: 5,
    },
  });