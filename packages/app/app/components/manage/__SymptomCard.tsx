import AppText from '@components/common/AppText';
import Pressable from '@components/common/Pressable';
import { ServicesNavParams } from '@navigation/services-navigator/profile-navigator/ServicesNavParams';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import theme from '@constants/theme';


import {
  Image,
  View,
} from 'react-native';
import { Entypo } from '@expo/vector-icons';
import Chips from '@components/common/Chips';
import { StyleSheet } from 'react-native';
import { formatDate, getTimeDifference } from '@utils/index';

const numOflineToShow = 2;

type Item = {
    id: string;
    value: string | undefined;
  };
  

type Props = {
    symptom: string;
    timestamp: Date;
    onClick?: Function;
}

export default function SymptomCard({ symptom, timestamp, onClick }: Props) {
  return (
    <Pressable
      className='relative bg-white rounded-lg p-4 mt-2 mx-2'
      style={{
        elevation: 4,
        shadowColor: '#004987',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      }}
      onPress={() => onClick?.()}
    >
      <View className='flex flex-row'>
        <View className='flex flex-col justify-center max-w-[80%]'>
          <AppText
            numberOfLines={1}
            ellipsizeMode='tail'
            className='text-base font-montserratBold text-[16px] text-black overflow-hidden whitespace-nowrap'
          >
            {symptom}
          </AppText>
       </View>
       <View className='flex-1 flex-row items-center justify-end w-[20%]'>
        <AppText
          className='text text-[16px] font-normal p-2'
        >
          {formatDate(timestamp)}
        </AppText>
      </View>
       </View>
    </Pressable>
  );
}


const styles = StyleSheet.create({
    box: {
      backgroundColor: '#fff', // Example background color
      shadowColor: 'rgba(0, 73, 135, 0.15)',
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 1,
      shadowRadius: 11,
      // Use the next property for Android
      elevation: 5,
    },
  });