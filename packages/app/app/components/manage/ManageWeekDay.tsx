import AppText from "@components/common/AppText";
import clsx from "clsx";
import { isAfter, isFuture } from "date-fns";
import { Check } from "lucide-react-native";
import { Dimensions, Pressable } from "react-native";

const MARGIN = 16;
const GAP = 3;
const SCREEN_WIDTH = Dimensions.get("screen").width - MARGIN * 2;
const DAY_WIDTH = SCREEN_WIDTH / 7 - GAP * 2;

export const ManageWeekDay = ({
  date,
  day,
  disabled,
  active,
  showCheckIcon,
  partiallyAnswered,
  onPress,
  dateObject,
}: {
  day: string;
  date: string;
  disabled?: boolean;
  active?: boolean;
  showCheckIcon?: boolean;
  partiallyAnswered?: boolean;
  dateObject?: Date;
  onPress?: ({ date }: { date: Date }) => void;
}) => {
  return (
    <Pressable
      style={{ width: DAY_WIDTH, height: 50, marginHorizontal: GAP }}
      className={clsx(
        "items-center rounded-xl border-2 border-transparent justify-start pt-1",
        showCheckIcon && "bg-white shadow-sm shadow-primary/20",
        active && "border-[#2e7bbf]"
      )}
      onPress={() => onPress?.({ date: new Date(date) })}
      disabled={disabled}
    >
      <AppText
        className={clsx(
          "uppercase text-[10px] font-montserratMedium color-[#336D9F] leading-3",
          dateObject && isFuture(dateObject) && "color-[#8AABC8]"
        )}
      >
        {day}
      </AppText>
      <AppText
        className={clsx(
          "uppercase text-xs color-[#00284A]",
          dateObject && isFuture(dateObject) && "color-[#8AABC8]",
          !showCheckIcon && "text-base"
        )}
      >
        {date}
      </AppText>
      {showCheckIcon && (
        <Check size={15} color={!partiallyAnswered ? "#25BA60" : "#B0C7DA"} />
      )}
    </Pressable>
  );
};
