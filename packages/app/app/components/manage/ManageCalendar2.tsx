import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import { trpc } from "@providers/RootProvider";
import clsx from "clsx";
import {
  addMonths,
  eachMonthOfInterval,
  endOfMonth,
  format,
  isFuture,
  isToday,
  parse,
  parseISO,
  startOfMonth,
  subMonths,
} from "date-fns";
import _ from "lodash";
import React, { useCallback, useEffect, useState } from "react";
import { ActivityIndicator, Dimensions, StyleSheet, View } from "react-native";
import { CalendarList, DateData, LocaleConfig } from "react-native-calendars";
import { DayProps } from "react-native-calendars/src/calendar/day";
import { Theme } from "react-native-calendars/src/types";

// Configure English locale
LocaleConfig.locales["en"] = {
  monthNames: [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ],
  monthNamesShort: [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ],
  dayNames: [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ],
  dayNamesShort: ["SU", "MO", "TU", "WE", "TH", "FR", "SA"], // Using your required short format
  today: "Today",
};

// Set default locale to English
LocaleConfig.defaultLocale = "en";

const DayWidth = (Dimensions.get("screen").width - 20 * 2) / 7;

const PAST_SCROLL_RANGE = 12;
const FUTURE_SCROLL_RANGE = 1;
const initialDate = format(new Date(), "yyyy-MM-dd");

interface Props {
  horizontalView?: boolean;
  /**event key should be in yyyy-mm-dd format and value is a color arr */
  events?: Record<string, string[]>;
  onDateSelect?: (date: Date, topic?: string) => void;
  startDate: Date;
  endDate: Date;
  topicId: string | undefined;
}

const ManageCalendar2 = ({
  topicId,
  endDate,
  startDate,
  events,
  horizontalView,
  onDateSelect,
}: Props) => {
  const [loading, setLoading] = useState(true);
  const [calendarData, setCalendarData] = useState<Record<string, any>>({});
  const [selected, setSelected] = useState(initialDate);

  const [months] = useState(
    eachMonthOfInterval({
      start: subMonths(startOfMonth(new Date()), PAST_SCROLL_RANGE),
      end: endOfMonth(addMonths(new Date(), 1)),
    }).map((date) => format(date, "yyyy-MM-dd"))
  );

  const onDayPress = useCallback((day: DateData) => {
    setSelected(day.dateString);
  }, []);

  const getCalenderWeeksAndColor =
    trpc.manage.getCalenderWeeksAndColor.useMutation();

  const fetchCalendarData = async (startDateOfMonth: string) => {
    try {
      const result = await getCalenderWeeksAndColor.mutateAsync({
        startDate,
        endDate,
        startDateOfMonth,
        topicId,
      });
      setCalendarData((prev) => ({ ...prev, [startDateOfMonth]: result }));
      return result;
    } catch (error) {
      console.error(
        `Error fetching calendar data for ${startDateOfMonth}:`,
        error
      );
    }
  };

  useEffect(() => {
    setLoading(true);
    const fetchAllCalendarData = async () => {
      if (months) {
        try {
          await Promise.all(months.map((month) => fetchCalendarData(month)));
        } catch (error) {
          console.error("Error fetching calendar data for all months:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchAllCalendarData();
  }, [topicId]);

  const Day = useCallback(
    function ({ date }: DayProps) {
      // @ts-expect-error
      const dateString = date.dateString as string;
      const _date = parse(dateString, "yyyy-MM-dd", new Date());

      const dateEvent = events?.[dateString];
      let daysColorsInPercent: { color: string; percent: number }[] = [];
      if (dateEvent) {
        daysColorsInPercent = dateEvent.map((color) => ({
          color,
          percent: (1 / dateEvent.length) * 100,
        }));
      }

      const event = dateEvent ?? [];
      const color = event.filter(Boolean)?.[0];
      const _isToday = isToday(_date);
      const isFutureDate = isFuture(_date);
      // const isPrevMonth = !isSameMonth(
      //   _date,
      //   new Date(_date)
      // );
      return (
        <Pressable
          className="items-center"
          onPress={() => onDateSelect?.(_date, topicId)}
        >
          <AppText
            className={clsx(
              "text-[#00427B] font-montserratMedium text-sm text-center items-center justify-center",
              _isToday && "text-[#00427B] font-montserratSemiBold",
              isFutureDate && "text-[#8AABC8]"
              // isPrevMonth && "text-transparent"
            )}
          >
            {format(_date, "d")}
          </AppText>
          {!!color && (
            <View
              className="rounded-lg overflow-hidden flex-row"
              style={[
                styles.dot,
                {
                  flexDirection: "row",
                  // alignItems: "center",
                  // justifyContent: "center",
                },
              ]}
            >
              {_.map(daysColorsInPercent, ({ color, percent }, index) => {
                return (
                  <View
                    key={index}
                    style={{
                      borderRadius: topicId ? 6 : "unset",
                      backgroundColor: color,
                      height: "100%",
                      width: topicId ? 8 : `${percent}%`, // Ensure parent has defined width for percentage
                    }}
                  />
                );
              })}
            </View>
          )}
        </Pressable>
      );
    },
    [events]
  );

  const CalendarHeader = useCallback(
    function (date: any) {
      const _date = startOfMonth(new Date(date));
      const dateStr = format(_date, "yyyy-MM-dd");

      // todo:fix month headers
      const monthDateStr = format(
        startOfMonth(addMonths(new Date(date), 0)),
        "yyyy-MM-dd"
      );

      const data = calendarData[monthDateStr] || {};
      const { colorsInPercent = [] } = data;

      return (
        <View className="flex flex-row items-center justify-between w-[100%] p-3">
          <View className="flex-row items-center ">
            <AppText className="text-2xl font-montserratSemiBold text-[#336D9F] ml-[-32px] mr-2">
              {format(parseISO(dateStr), "MMMM")}{" "}
              <AppText className="text-base text-[#5485AF]">
                {format(parseISO(dateStr), "yyyy")}
              </AppText>
            </AppText>

            {loading && <ActivityIndicator color="#004987" />}
          </View>

          {/* Monthly Stats */}
          <View className="w-[55px] h-[12px] rounded-lg overflow-hidden flex-row">
            {colorsInPercent.map(({ color, percent }, index) => {
              return (
                <View
                  key={index}
                  style={{
                    width: `${percent}%`,
                    backgroundColor: color,
                  }}
                />
              );
            })}
          </View>
        </View>
      );
    },
    [calendarData, loading]
  );

  return (
    <CalendarList
      current={initialDate}
      pastScrollRange={PAST_SCROLL_RANGE}
      futureScrollRange={FUTURE_SCROLL_RANGE}
      onDayPress={onDayPress}
      renderHeader={!horizontalView ? CalendarHeader : undefined}
      calendarHeight={!horizontalView ? 330 : undefined}
      theme={theme}
      horizontal={horizontalView}
      pagingEnabled={horizontalView}
      staticHeader={horizontalView}
      dayComponent={Day}
      calendarStyle={{}}
      firstDay={1}
    />
  );
};

const theme: Theme = {
  textSectionTitleColor: "rgba(51, 109, 159, 1)",
  textDayHeaderFontSize: 14,
  stylesheet: {
    calendar: {},
  },
};

export default ManageCalendar2;

const styles = StyleSheet.create({
  day: { width: DayWidth },
  dot: {
    width: 20,
    height: 7,
    borderRadius: 4,
    marginTop: 4,
    flexDirection: "row",
    justifyContent: "center",
    position: "absolute",
    top: 18,
  },
});
