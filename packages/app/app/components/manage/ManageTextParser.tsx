import AppText from "@components/common/AppText";
import { parseRichText } from "@utils/index";
import clsx from "clsx";
import { TextProps, View } from "react-native";
import { memo } from "react";

function ManageTextParser({
  children,
  boldFontSize = 16,
  fontSize = 16,
  boldFontColor = "#004987",
  color = "#00284A",
  centerText = true,
}: React.PropsWithChildren<{
  fontSize?: number;
  boldFontSize?: number;
  color?: string;
  boldFontColor?: string;
  centerText?: boolean;
}>) {
  if (typeof children !== "string")
    throw new Error("children must be a string");

  const parts = children.split("**");

  return (
    <AppText
      className={color === "#004987" ? "font-montserratSemiBold" : ""}
      style={{
        fontSize,
        color,
        lineHeight: fontSize * 1.2,
        textAlign: centerText ? "center" : "start",
        
      }}
    >
      {parts.map((part, index) => {
        return index % 2 === 1 ? (
          <AppText
            key={index}
            className="font-montserratMedium"
            style={{
              fontSize: boldFontSize,
              lineHeight: boldFontSize * 1.2,
            }}
          >
            {part}
          </AppText>
        ) : (
          part
        );
      })}
    </AppText>
  );
}

export default memo(ManageTextParser);
