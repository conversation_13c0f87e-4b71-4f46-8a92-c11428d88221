import AppText from "@components/common/AppText";
import LoadingScreen from "@components/common/LoadingScreen";
import Pressable from "@components/common/Pressable";
import { trpc } from "@providers/RootProvider";
import clsx from "clsx";
import {
  addMonths,
  eachMonthOfInterval,
  endOfMonth,
  format,
  isFuture,
  isSameMonth,
  isToday,
  startOfMonth,
  startOfToday,
  subMonths,
} from "date-fns";
import _ from "lodash";
import { MID } from "rabble_be/src/@types/common";
import React, { useEffect, useRef, useState } from "react";
import { Dimensions, FlatList, Platform, StyleSheet, View } from "react-native";

const days = ["mo", "tu", "we", "th", "fr", "sa", "su"];

type Props = {
  /**event key should be in yyyy-mm-dd format and value is a color arr */
  events?: Record<string, string[]>;
  onDateSelect?: (date: Date, topic?: string) => void;
  startDate: Date;
  endDate: Date;
  topicId: MID | undefined;
  // calenderData: any;
  onMonthChange?: ({
    endOfMonth,
    startOfMonth,
  }: {
    startOfMonth: Date;
    endOfMonth: Date;
  }) => void;
};

const CONTENT_HEIGHT = Dimensions.get("screen").height - 45;

const SectionHeader = ({
  title,
  monthWiseColorPercent,
}: {
  title: Date;
  monthWiseColorPercent: [];
}) => {
  return (
    <View className="bg-white">
      <View className="flex-row justify-between items-center mb-2 mt-[40px]">
        <AppText className="text-2xl font-montserratSemiBold text-[#336D9F]">
          {format(title, "MMMM")}{" "}
          <AppText className="text-base text-[#5485AF]">
            {format(title, "yyyy")}
          </AppText>
        </AppText>

        {/* Monthly Stats */}
        <View className="w-[55px] h-[12px] rounded-lg overflow-hidden flex-row">
          {monthWiseColorPercent.map(({ color, percent }, index) => {
            return (
              <View
                key={index}
                style={{
                  width: `${percent}%`,
                  backgroundColor: color,
                }}
              />
            );
          })}
        </View>
      </View>
      <View className="flex-row items-center justify-between mb-5">
        {days.map((day) => {
          return (
            <AppText
              className="flex-1 text-center uppercase text-[10px] text-[#336D9F] font-montserratSemiBold"
              key={day}
            >
              {day}
            </AppText>
          );
        })}
      </View>
    </View>
  );
};

function ManageCalendarView({
  onDateSelect,
  onMonthChange,
  events,
  startDate,
  endDate,
  topicId,
}: Props) {
  const [loading, setLoading] = useState(true);
  const flatlistRef = useRef<FlatList>(null);
  const [selectedDate, setSelectedDate] = useState(startOfToday());
  const [calendarData, setCalendarData] = useState<Record<string, any>>({});
  const [months] = useState<string[]>(
    eachMonthOfInterval({
      start: subMonths(
        startOfMonth(new Date()),
        Platform.OS === "android" ? 13 : 13
      ),
      end: addMonths(endOfMonth(new Date()), 1),
    })
      .map((date) => format(date, "yyyy-MM-dd"))
      .reverse()
  );

  const getCalenderWeeksAndColor =
    trpc.manage.getCalenderWeeksAndColor.useMutation();

  const fetchCalendarData = async (startDateOfMonth: string) => {
    try {
      const result = await getCalenderWeeksAndColor.mutateAsync({
        startDate,
        endDate,
        startDateOfMonth,
        topicId,
      });
      setCalendarData((prev) => ({ ...prev, [startDateOfMonth]: result }));
      return result;
    } catch (error) {
      console.error(
        `Error fetching calendar data for ${startDateOfMonth}:`,
        error
      );
    }
  };

  useEffect(() => {
    setLoading(true);
    const fetchAllCalendarData = async () => {
      if (months) {
        try {
          await Promise.all(months.map((month) => fetchCalendarData(month)));
        } catch (error) {
          console.error("Error fetching calendar data for all months:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchAllCalendarData();
  }, [months, topicId]);

  if (loading) {
    return <LoadingScreen />;
  }

  const renderItem = ({ item: startDateOfMonth }: { item: string }) => {
    const data = calendarData[startDateOfMonth] || {};
    const { week = [], colorsInPercent = [] } = data;
    return (
      <View>
        <SectionHeader
          title={new Date(startDateOfMonth)}
          monthWiseColorPercent={colorsInPercent}
        />
        <View className="flex-row items-center justify-between flex-wrap">
          {week.map((d: any) => {
            const dateUTC = new Date(d);

            const correctedDate = new Date(
              Date.UTC(
                dateUTC.getFullYear(),
                dateUTC.getMonth(),
                dateUTC.getDate()
              )
            );

            const _isToday = isToday(correctedDate);
            const _selectedDate = selectedDate === correctedDate;
            const isFutureDate = isFuture(correctedDate);
            const isPrevMonth = !isSameMonth(
              correctedDate,
              new Date(startDateOfMonth)
            );

            const eventKey = format(correctedDate, "yyyy-MM-dd");
            // if (eventKey === "2024-12-17") {

            let daysColorsInPercent: any[] = [];
            if (events?.[eventKey]) {
              daysColorsInPercent = _.map(events[eventKey], (color) => ({
                color,
                percent: (1 / events[eventKey].length) * 100,
              })).filter(Boolean);
            }

            const event = events?.[eventKey] ?? [];
            const color = event.filter(Boolean)?.[0];

            return (
              <Pressable
                key={String(d)}
                disabled={isFutureDate}
                className="text-center flex-row justify-center items-center h-10 flex-wrap"
                onPress={() => onDateSelect?.(correctedDate, topicId)}
              >
                <AppText
                  style={styles.day}
                  className={clsx(
                    "text-[#00427B] font-montserratMedium text-sm text-center items-center justify-center",
                    _isToday && "text-[#00427B] font-montserratSemiBold",
                    isFutureDate && "text-[#8AABC8]",
                    isPrevMonth && "text-transparent"
                  )}
                >
                  {format(correctedDate, "d")}
                </AppText>

                {color && !isPrevMonth && (
                  // <View
                  //   style={[
                  //     styles.dot,
                  //     { backgroundColor: color, width: topicId ? 6 : 20 },
                  //   ]}
                  // />
                  <View
                    className="rounded-lg overflow-hidden flex-row"
                    style={[
                      styles.dot,
                      {
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "center",
                      },
                    ]}
                  >
                    {_.map(daysColorsInPercent, ({ color, percent }, index) => {
                      return (
                        <View
                          key={index}
                          style={{
                            borderRadius: topicId ? 6 : "unset",
                            backgroundColor: color,
                            height: "100%",
                            width: topicId ? 8 : `${percent}%`, // Ensure parent has defined width for percentage
                          }}
                        />
                      );
                    })}
                  </View>
                )}
              </Pressable>
            );
          })}
        </View>
      </View>
    );
  };

  return (
    <View className="flex-1">
      <FlatList
        initialScrollIndex={0.65}
        getItemLayout={({ item }, index) => ({
          length: CONTENT_HEIGHT / 2,
          offset: (CONTENT_HEIGHT / 2) * index,
          index,
        })}
        inverted
        ref={flatlistRef}
        data={months}
        keyExtractor={(item, index) => String(item)}
        renderItem={renderItem}
      />
    </View>
  );
}

const DayWidth = (Dimensions.get("screen").width - 20 * 2) / 7;

const styles = StyleSheet.create({
  day: { width: DayWidth },
  dot: {
    width: 20,
    height: 7,
    borderRadius: 4,
    marginTop: 4,
    position: "absolute",
    bottom: 13,
  },
});

export default React.memo(ManageCalendarView);
