import React, { useEffect, useState } from "react";
import { Dimensions, View } from "react-native";
import CompletedIcon from "../../assets/svg/CompletedIcon";
import Pressable from "../common/Pressable";
import AppText from "@components/common/AppText";

const COMPONENT_WIDTH = Dimensions.get("screen").width - 16 * 2;

type Props = {
  onEditResponseClick: () => void;
  subText?: string;
};

export default function ManageCompletedQuestion({
  onEditResponseClick,
  subText,
}: Props) {
  return (
    <View
      className="bg-[#76AFE1] rounded-2xl p-[16px] shadow-md shadow-primary/10 h-[320px] -z-10"
      style={{ width: COMPONENT_WIDTH }}
    >
      <View className="flex-1 justify-center items-center">
        <CompletedIcon />
        <AppText className="text-[32px] text-white font-montserratBold my-[16px] leading-[34px] ">
          Completed 🎉
        </AppText>
        <AppText className="text-[12px]  text-white font-montserratRegular w-[80%] text-center">
          {subText ??
            "Completing this daily helps you track your journey and receive a more personalized experience in the app."}
        </AppText>
      </View>
      <View>
        <Pressable
          onPress={() => onEditResponseClick()}
          style={{ backgroundColor: "rgba(245,247,249,0.20)" }}
          className="bg-[rgba(245,247,249,0.20)] flex justify-center items-center p-2 rounded-lg"
        >
          <AppText className="text-[16px] text-white font-montserratSemiBold">
            Edit Responses
          </AppText>
        </Pressable>
      </View>
    </View>
  );
}
