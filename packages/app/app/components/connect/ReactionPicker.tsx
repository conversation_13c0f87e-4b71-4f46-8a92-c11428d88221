import React from "react";
import { Modal, View, Pressable as RNPressable } from "react-native";
import Pressable from "@components/common/Pressable";
import AppText from "@components/common/AppText";

type ReactionPickerProps = {
  visible: boolean;
  reactions?: string[];
  onSelect: (emoji: string) => void;
  onClose?: () => void;
};

const DEFAULT_REACTIONS = ["👍", "❤️", "😄", "😮", "🙏"];

const ReactionPicker: React.FC<ReactionPickerProps> = ({
  visible,
  reactions = DEFAULT_REACTIONS,
  onSelect,
  onClose,
}) => {
  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
      // className="w-[250px]"
    >
      {/* Full-screen background pressable */}
      <RNPressable
        className="flex-1 my-[60%] items-center "
        onPress={onClose}
      >
        {/* Emoji bar — prevent propagation */}
        <View
          className="flex-row items-center px-4 py-2 bg-white rounded-full shadow-md"
          onStartShouldSetResponder={() => true} // prevents background press
        >
          {reactions.map((emoji) => (
            <Pressable
              key={emoji}
              onPress={() => {
                onSelect(emoji);
                onClose?.();
              }}
              className="mx-3"
            >
              <AppText className="text-3xl">{emoji}</AppText>
            </Pressable>
          ))}
        </View>
      </RNPressable>
    </Modal>
  );
};

export default ReactionPicker;
