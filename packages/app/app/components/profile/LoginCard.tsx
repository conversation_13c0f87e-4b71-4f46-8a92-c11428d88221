import ProfileAccount from '@assets/svg/profile/ProfileAccount';
import AppText from '@components/common/AppText';
import Pressable from '@components/common/Pressable';
import useAuthGuard from '@hooks/useAuthGuard';
import { styled } from 'nativewind';
import { PressableProps, View } from 'react-native';

const Wrapper = styled(
  Pressable,
  'flex flex-row items-center py-8 px-6 border border-primary rounded-lg'
);

export default function LoginCard(props: PressableProps) {
  const authGuard = useAuthGuard();
  const handleLogin = () => authGuard();

  return (
    <Wrapper {...props} onPress={handleLogin}>
      <ProfileAccount />

      <View className='ml-2'>
        <AppText className='font-montserratBold text-primary'>
          Sign up / Login
        </AppText>
        <AppText className='text-sm text-primary'>
          To keep track of your health
        </AppText>
      </View>
    </Wrapper>
  );
}
