import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import theme from "@constants/theme";
import { Entypo } from "@expo/vector-icons";
import { styled } from "nativewind";
import { PressableProps, View } from "react-native";
const Wrapper = styled(Pressable, "");

interface Props extends PressableProps {
  icon?: JSX.Element;
  title: string;
  showRightArrow?: boolean;
}

export default function SectionLink({
  icon,
  title,
  showRightArrow = true,
  ...otherProps
}: Props) {
  return (
    <Wrapper className="flex items-center flex-row py-2" {...otherProps}>
      <View className="flex items-center flex-row flex-1">
        {icon}
        <AppText className="text-black ml-4 font-montserratMedium text-sm">
          {title}
        </AppText>
      </View>
      {showRightArrow && (
        // @ts-ignore
        <Entypo name="chevron-small-right" size={24} color={theme?.primary} />
      )}
    </Wrapper>
  );
}
