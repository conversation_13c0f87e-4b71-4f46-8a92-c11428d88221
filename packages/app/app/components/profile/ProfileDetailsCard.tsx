import LogoSimple from "@assets/svg/LogoOutline";
import EditIcon from "@assets/svg/profile/EditIcon";
import Email from "@assets/svg/profile/Email";
import Phone from "@assets/svg/profile/Phone";
import AppText from "@components/common/AppText";
import { ENV, s3Paths } from "@constants/index";
import { useSession } from "@hooks/persistUser";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import errorHandler from "@utils/errorhandler";
import { formatStringToUsPhoneNumberFormat, imageKit } from "@utils/index";
import { styled } from "nativewind";
import { useCallback, useState } from "react";
import { Image, PressableProps, StyleSheet, View } from "react-native";
import Toast from "react-native-toast-message";
import { Image as CacheImage } from "react-native-expo-image-cache";
import clsx from "clsx";
import { HelpingHand } from "lucide-react-native";
import Pressable from "@components/common/Pressable";

const Wrapper = styled(
  Pressable,
  "flex flex-row items-center py-4 px-4 bg-white rounded-xl my-6"
);

export default function ProfileDetailsCard(props: any) {
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();
  const { user } = useSession();

  const handleNavigation = () => {
    navigation.navigate("CreateProfileScreen", {
      userOrPatientId: user?._id as string,
    });
  };

  const isCaregiver = props.flatBottomBorders;

  if (!user) return null;
  return (
    <Wrapper
      {...props}
      style={[
        styles.container,
        props.flatBottomBorders && styles.flatBottomBorders,
      ]}
    >
      <View>
        {user?.profilePicture ? (
          <CacheImage
            uri={imageKit({
              imagePath: user?.profilePicture,
              transform: ["w-100"],
            })}
            preview={{
              uri: imageKit({
                imagePath: user?.profilePicture,
                transform: ["w-30", "bl-6"],
              }),
            }}
            tint="dark"
            transitionDuration={300}
            style={{
              borderWidth: 0.2,
              borderRadius: 50,
              width: 100,
              height: 100,
            }}
          />
        ) : (
          <LogoSimple />
        )}
        {isCaregiver && (
          <View className="w-7 h-7 absolute -bottom-0 -right-0 bg-[#e5f4ff] justify-center items-center rounded-full border-white border-2">
            <HelpingHand className="w-5 h-5 text-primary" />
          </View>
        )}
      </View>

      <Pressable
        actionTag="profile card"
        className="ml-2 flex-1"
        onPress={handleNavigation}
      >
        <View className="flex-row justify-between">
          <AppText
            className="font-montserratBold text-black mx-2 text-base"
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {`${user.firstname} ${user.lastname}`}
          </AppText>
          <View className="bg-blue-50 p-1 rounded-lg">
            <EditIcon style={{ transform: [{ scale: 1.0 }] }} />
          </View>
        </View>
        <View className="flex flex-row items-center">
          {user.email && (
            <>
              <View className="bg-blue-50 p-2 rounded-lg mx-2">
                <Email style={{ transform: [{ scale: 1.7 }] }} />
              </View>
              <View className="flex-1 flex-row items-center overflow-hidden">
                <AppText
                  className="text-black my-1 flex-1"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {user.email}
                </AppText>
              </View>
            </>
          )}
        </View>
        <View className="flex flex-row items-center">
          {!!user.contact &&
            !`${user.contact.phone}`.toLowerCase().includes("null") && ( //TODO: handle this frontend
              <>
                <View className="bg-blue-50 p-2 rounded-lg mx-2">
                  <Phone style={{ transform: [{ scale: 1.5 }] }} />
                </View>
                <AppText className="flex-1 text-black">
                  {ENV.EXPO_PUBLIC_COUNTRY_CODE}
                  {formatStringToUsPhoneNumberFormat(user.contact.phone)}
                </AppText>
              </>
            )}
        </View>
      </Pressable>
    </Wrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    shadowColor: "#004987",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  flatBottomBorders: {
    borderBottomStartRadius: 0,
    borderBottomEndRadius: 0,
  },
});
