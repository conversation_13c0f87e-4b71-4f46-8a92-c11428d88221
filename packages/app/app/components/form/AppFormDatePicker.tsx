import React from "react";
import { View } from "react-native";
import { Controller, useFormContext } from "react-hook-form";
import AppDatePicker, {
  AppDatePickerProps,
} from "@components/common/AppDatepicker";
import AppText from "@components/common/AppText";
import { ErrorMessage } from "@hookform/error-message";
import { getMessageByPath } from "@utils/message";

interface Props extends AppDatePickerProps {
  name: string;
}

export default function AppFormDatePicker({
  name,
  onChangeDate,
  mode = "date",
  ...otherProps
}: Props) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const msg = getMessageByPath(errors, name);

  return (
    <>
      <Controller
        control={control}
        rules={{ required: true }}
        render={({ field: { onChange, value } }) => (
          <View className="w-full">
            <AppDatePicker
              error={!!msg}
              onChangeDate={(date) => {
                onChange(date);
                onChangeDate?.(date);
              }}
              date={value}
              mode={mode}
              {...otherProps}
            />
            <ErrorMessage
              errors={errors}
              name={name}
              render={({ message }) => (
                <AppText className="text-red-500">{message}</AppText>
              )}
            />
          </View>
        )}
        name={name}
      />
    </>
  );
}
