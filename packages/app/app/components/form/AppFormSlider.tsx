import React from 'react';
import { View } from 'react-native';
import { Controller, useFormContext } from 'react-hook-form';
import { ErrorMessage } from '@hookform/error-message';
import AppText from '@components/common/AppText';
import AppFormLabel from './AppFormLabel';
import AppSlider, { AppSliderProps } from '@components/common/AppSlider';
import clsx from 'clsx';

export interface AppFormSliderProps extends Omit<AppSliderProps, 'value' | 'onValueChange'> {
  /** Form field name */
  name: string;
  /** Label for the slider */
  label?: string;
  /** Additional label hint */
  labelHint?: string;
  /** Custom class for the wrapper */
  wrapperClass?: string;
  /** Custom class for the label */
  labelClass?: string;
  /** Whether to show the current value */
  showValue?: boolean;
  /** Custom value formatter */
  valueFormatter?: (value: number) => string;
  /** Custom class for the value display */
  valueClass?: string;
}

const AppFormSlider: React.FC<AppFormSliderProps> = ({
  name,
  label,
  labelHint,
  wrapperClass,
  labelClass,
  showValue = true,
  valueFormatter,
  valueClass,
  min = 0,
  max = 100,
  step = 1,
  ...sliderProps
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const formatValue = (value: number): string => {
    if (valueFormatter) {
      return valueFormatter(value);
    }
    return value.toString();
  };

  return (
    <View className={clsx(wrapperClass)}>
      {label && (
        <AppFormLabel
          label={label}
          labelClass={labelClass}
          labelHint={labelHint}
        />
      )}
      
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, value } }) => (
          <View>
            <AppSlider
              {...sliderProps}
              min={min}
              max={max}
              step={step}
              value={value || min}
              onValueChange={onChange}
            />
            
            {showValue && (
              <View className="mt-2 items-center">
                <AppText className={clsx('text-lg font-semibold text-gray-700', valueClass)}>
                  {formatValue(value || min)}
                </AppText>
              </View>
            )}
          </View>
        )}
      />
      
      <ErrorMessage
        errors={errors}
        name={name}
        render={({ message }) => (
          <AppText className="text-red-500 text-sm mt-1">{message}</AppText>
        )}
      />
    </View>
  );
};

export default AppFormSlider;
