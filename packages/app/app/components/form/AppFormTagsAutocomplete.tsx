import React, { useState, useMemo, useCallback, useEffect } from "react";
import { View, ScrollView, TextInput } from "react-native";
import { Controller, useFormContext } from "react-hook-form";

import _ from "lodash";

import AppText from "@components/common/AppText";

import Pressable from "@components/common/Pressable";
import AppFormLabel from "./AppFormLabel";
import { getMessageByPath } from "@utils/message";
import { trpc } from "@providers/RootProvider";
import {
  filterTagsBySearch,
  tagExists,
  isValidTagName,
  sanitizeTagName,
  createRabbleGroupTag,
} from "@utils/tagHelpers";

interface Tag {
  _id: any; // Can be string or ObjectId from MongoDB
  tag: string;
  scope?: string[];
}

interface AppFormTagsAutocompleteProps {
  name: string;
  label: string;
  placeholder?: string;
  maxTags?: number;
  className?: string;
}

export default function AppFormTagsAutocomplete({
  name,
  label,
  placeholder = "Type to search or create tags...",
  maxTags = 10,
  className = "",
}: AppFormTagsAutocompleteProps) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const [inputText, setInputText] = useState("");
  const [debouncedInputText, setDebouncedInputText] = useState("");
  const [isCreatingTag, setIsCreatingTag] = useState(false);

  // Debounce input text to avoid excessive filtering
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedInputText(inputText);
    }, 300);

    return () => clearTimeout(timer);
  }, [inputText]);

  // Fetch all available tags from the blog tags API
  const { data: allTags = [], isLoading } = trpc.blog.tags.useQuery();

  // Create tag mutation
  const createTagMutation = trpc.blog.createBlogTag.useMutation();

  const errorMessage = getMessageByPath(errors, name);

  // Filter tags based on debounced input text
  const filteredTags = useMemo(() => {
    if (!debouncedInputText.trim() || debouncedInputText.length < 2) return [];

    return filterTagsBySearch(allTags as Tag[], debouncedInputText).slice(0, 6); // Limit to 6 suggestions
  }, [allTags, debouncedInputText]);

  // Check if current input would create a new tag
  const isNewTag = useMemo(() => {
    if (
      !debouncedInputText.trim() ||
      debouncedInputText.length < 2 ||
      !isValidTagName(debouncedInputText)
    )
      return false;
    return !tagExists(allTags as Tag[], debouncedInputText);
  }, [allTags, debouncedInputText]);

  // Show suggestions when input has enough characters and has suggestions or can create new tag
  const shouldShowSuggestions = useMemo(() => {
    return (
      debouncedInputText.length >= 2 && (filteredTags.length > 0 || isNewTag)
    );
  }, [debouncedInputText, filteredTags.length, isNewTag]);

  const handleCreateTag = useCallback(
    async (tagName: string) => {
      const sanitizedName = sanitizeTagName(tagName);
      if (!sanitizedName || !isValidTagName(sanitizedName)) return null;

      setIsCreatingTag(true);
      try {
        const tagData = createRabbleGroupTag(sanitizedName);
        const newTag = await createTagMutation.mutateAsync(tagData);
        return newTag;
      } catch (error) {
        console.error("Failed to create tag:", error);
        return null;
      } finally {
        setIsCreatingTag(false);
      }
    },
    [createTagMutation]
  );

  const handleTagSelect = useCallback(
    async (
      tag: Tag | string,
      selectedTags: string[],
      onChange: (tags: string[]) => void
    ) => {
      let tagToAdd: string;

      if (typeof tag === "string") {
        // Creating a new tag
        const newTag = await handleCreateTag(tag);
        if (!newTag) return;
        tagToAdd = newTag.tag;
      } else {
        // Selecting existing tag
        tagToAdd = tag.tag;
      }

      // Check if tag is already selected
      if (selectedTags.includes(tagToAdd)) return;

      // Check max tags limit
      if (selectedTags.length >= maxTags) return;

      // Add tag to selection
      onChange([...selectedTags, tagToAdd]);
      setInputText("");
      setDebouncedInputText("");
    },
    [handleCreateTag, maxTags]
  );

  const handleTagRemove = useCallback(
    (
      tagToRemove: string,
      selectedTags: string[],
      onChange: (tags: string[]) => void
    ) => {
      onChange(selectedTags.filter((tag) => tag !== tagToRemove));
    },
    []
  );

  const renderSelectedTags = useCallback(
    (selectedTags: string[], onChange: (tags: string[]) => void) => (
      <View className="flex-row flex-wrap mt-2" style={{ gap: 8 }}>
        {selectedTags.map((tag, index) => (
          <View
            key={index}
            className="bg-blue-100 px-3 py-1 rounded-full flex-row items-center"
          >
            <AppText className="text-blue-800 text-sm mr-2">{tag}</AppText>
            <Pressable
              onPress={() => handleTagRemove(tag, selectedTags, onChange)}
              className="w-5 h-5 bg-blue-200 rounded-full items-center justify-center"
            >
              <AppText className="text-blue-800 text-xs font-bold">×</AppText>
            </Pressable>
          </View>
        ))}
      </View>
    ),
    [handleTagRemove]
  );

  const renderSuggestionChip = useCallback(
    (tag: Tag, selectedTags: string[], onChange: (tags: string[]) => void) => {
      const isSelected = selectedTags.includes(tag.tag);

      return (
        <Pressable
          key={tag._id}
          onPress={() =>
            !isSelected && handleTagSelect(tag, selectedTags, onChange)
          }
          className={`px-3 py-2 rounded-full border mr-2 ${
            isSelected
              ? "bg-gray-100 border-gray-300"
              : "bg-orange-50 border-orange-200"
          }`}
          disabled={isSelected}
        >
          <AppText
            className={`text-sm ${
              isSelected ? "text-gray-500" : "text-orange-700"
            }`}
          >
            {tag.tag}
            {isSelected && " ✓"}
          </AppText>
        </Pressable>
      );
    },
    [handleTagSelect]
  );

  const renderCreateNewChip = useCallback(
    (
      tagName: string,
      selectedTags: string[],
      onChange: (tags: string[]) => void
    ) => (
      <Pressable
        onPress={() => handleTagSelect(tagName, selectedTags, onChange)}
        className="px-3 py-2 rounded-full bg-green-50 border border-green-200 mr-2"
        disabled={isCreatingTag}
      >
        <AppText className="text-sm text-green-700">
          {isCreatingTag ? "Creating..." : `+ Create "${tagName}"`}
        </AppText>
      </Pressable>
    ),
    [handleTagSelect, isCreatingTag]
  );

  const renderInlineSuggestions = useCallback(
    (selectedTags: string[], onChange: (tags: string[]) => void) => {
      if (!shouldShowSuggestions) return null;

      return (
        <View className="mt-2">
          <AppText className="text-xs text-gray-600 mb-2">
            Suggestions: ({filteredTags.length} found)
          </AppText>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="flex-row"
          >
            {filteredTags.map((tag) =>
              renderSuggestionChip(tag, selectedTags, onChange)
            )}
            {isNewTag &&
              debouncedInputText.trim() &&
              renderCreateNewChip(
                debouncedInputText.trim(),
                selectedTags,
                onChange
              )}
          </ScrollView>
        </View>
      );
    },
    [
      shouldShowSuggestions,
      filteredTags,
      isNewTag,
      debouncedInputText,
      renderSuggestionChip,
      renderCreateNewChip,
    ]
  );

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={[]}
      render={({ field: { onChange, value = [] } }) => (
        <View className={className}>
          {label && <AppFormLabel label={label} />}

          {/* Input Field */}
          <View className="border border-gray-300 rounded-lg p-3 bg-white">
            <TextInput
              value={inputText}
              onChangeText={setInputText}
              placeholder={placeholder}
              className="text-base"
              style={{ minHeight: 20 }}
              autoCorrect={false}
              autoCapitalize="none"
            />
          </View>

          {/* Inline Suggestions */}
          {renderInlineSuggestions(value, onChange)}

          {/* Selected Tags */}
          {value.length > 0 && renderSelectedTags(value, onChange)}

          {/* Max tags indicator */}
          {value.length > 0 && (
            <AppText className="text-xs text-gray-500 mt-1">
              {value.length}/{maxTags} tags selected
            </AppText>
          )}

          {/* Loading indicator for search */}
          {isLoading && debouncedInputText.length >= 2 && (
            <AppText className="text-xs text-gray-500 mt-1">
              Loading suggestions...
            </AppText>
          )}

          {/* Error Message */}
          {errorMessage && (
            <AppText className="text-red-500 text-sm mt-1">
              {errorMessage}
            </AppText>
          )}
        </View>
      )}
    />
  );
}
