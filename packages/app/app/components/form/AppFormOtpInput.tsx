import AppOtp from "@components/common/AppOtp";
import { Controller, useFormContext } from "react-hook-form";

interface Props {
  name: string;
  wrapperClass?: string;
  customClass?: string;
}
export default function AppFormOtpInput({
  name,
  wrapperClass,
  customClass,
}: Props) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <Controller
      control={control}
      rules={{
        required: true,
      }}
      render={({ field: { onChange, onBlur, value } }) => {
        return (
          <AppOtp
            otp={value}
            error={errors[name]?.message as string}
            onChangeText={onChange}
            wrapperClass={wrapperClass}
            customClass={customClass}
          />
        );
      }}
      name={name}
    />
  );
}
