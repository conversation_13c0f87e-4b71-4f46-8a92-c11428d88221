import {
  formatStringToUsPhoneNumberFormat,
  formatUsPhoneNumberToPlainString,
} from "@utils/index";
import AppFormInput, { AppFormInputProps } from "../AppFormInput";
import AppText from "@components/common/AppText";
import { ENV } from "@constants/index";

export default function FormMobileInput({
  name,
  ...otherProps
}: AppFormInputProps) {
  return (
    <AppFormInput
      name={name}
      placeholder="enter your mobile number"
      label="mobile no"
      wrapperClass="my-4"
      iconLeft={<AppText>{ENV.EXPO_PUBLIC_COUNTRY_CODE}</AppText>}
      transform={formatStringToUsPhoneNumberFormat}
      deform={formatUsPhoneNumberToPlainString}
      keyboardType="numeric"
      labelHint="text and data rates may apply."
      {...otherProps}
    />
  );
}
