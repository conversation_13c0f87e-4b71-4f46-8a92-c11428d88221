import AppTextInput, {
  AppTextInputProps,
} from "@components/common/AppTextInput";
import { useEffect, useMemo, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { View } from "react-native";
import { ErrorMessage } from "@hookform/error-message";
import AppText from "@components/common/AppText";
import { getMessageByPath } from "@utils/message";
import _ from "lodash";

export interface AppFormInputProps extends AppTextInputProps {
  name: string;
  transform?: (data: string) => string;
  deform?: (data: string) => string;
  handleBlur?: Function;
  fieldClass?: string;
}
export default function AppFormInput({
  name,
  transform,
  deform,
  onLabelPress,
  handleBlur = _.noop,
  fieldClass = "flex-1",
  ...otherProps
}: AppFormInputProps) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const msg = getMessageByPath(errors, name);

  return (
    <Controller
      control={control}
      rules={{
        required: true,
      }}
      render={({ field: { onChange, onBlur, value } }) => {
        return (
          <View className={fieldClass}>
            <AppTextInput
              onBlur={() => {
                handleBlur();
                onBlur();
              }}
              onChangeText={(text) => onChange(deform?.(text) || text)}
              value={transform?.(value) || value}
              error={!!msg}
              {...otherProps}
            />
            <ErrorMessage
              errors={errors}
              name={name}
              render={({ message }) => {
                return <AppText className="text-red-500">{message}</AppText>;
              }}
            />
          </View>
        );
      }}
      name={name}
    />
  );
}
