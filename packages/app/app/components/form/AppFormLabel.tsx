import AppText from "@components/common/AppText";
import { MaterialIcons } from "@expo/vector-icons";
import clsx from "clsx";
import { useState } from "react";
import { View } from "react-native";
import InfoModal from "@components/common/InfoModal";

interface Props {
  labelClass?: string;
  label: string;
  labelHint?: string;
}

export default function AppFormLabel({ labelClass, label, labelHint }: Props) {
  const [showModal, setShowModal] = useState(false);

  return (
    <View className="flex flex-row">
      <AppText className={clsx("mb-2 text-[#191D23] mr-2", labelClass)}>
        {label}
      </AppText>
      {labelHint && (
        <>
          <MaterialIcons
            name="info-outline"
            size={24}
            color="#FF8E1C"
            onPress={() => setShowModal(true)}
          />
          <InfoModal
            visible={showModal}
            onRequestClose={setShowModal}
            description={labelHint}
          />
        </>
      )}
    </View>
  );
}
