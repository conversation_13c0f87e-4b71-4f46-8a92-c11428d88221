import AppCheckBox, { AppCheckboxProps } from '@components/common/AppCheckbox';
import { Controller, useFormContext } from 'react-hook-form';

interface Props extends AppCheckboxProps {
  name: string;
}
export default function AppFormCheckbox({ name, ...otherProps }: Props) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <Controller
      control={control}
      rules={{
        required: true,
      }}
      render={({ field: { onChange, value } }) => (
        <AppCheckBox
          onSelect={onChange}
          active={value}
          error={errors[name]?.message as string}
          {...otherProps}
        />
      )}
      name={name}
    />
  );
}
