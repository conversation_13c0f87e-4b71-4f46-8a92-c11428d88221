import AppSelect, {
  Options,
  AppSelectProps,
} from "@components/common/AppSelect";
import { ReactElement } from "react";
import { Controller, useFormContext } from "react-hook-form";

interface Props extends AppSelectProps {
  name: string;
  children: ReactElement<Options> | ReactElement<Options>[];
  transform?: (value: string) => any;
  onFormValueChange?: () => void;
}
export default function AppFormSelect({
  name,
  children,
  selectedOptions,
  onFormValueChange,
  ...otherProps
}: Props) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const errorMessage = name
    .split(".")
    // @ts-ignore
    .reduce((acc, part) => acc && acc[part], errors)?.message;

  return (
    <Controller
      control={control}
      rules={{
        required: true,
      }}
      render={({ field: { onChange, value } }) => {
        return (
          <AppSelect
            error={errorMessage}
            selectedOptions={Array.isArray(value) ? value : [value]}
            {...otherProps}
            onOptionSelect={(data) => {
              onChange(data);
              onFormValueChange?.();
              otherProps.onOptionSelect?.(data);
            }}
          >
            {children}
          </AppSelect>
        );
      }}
      name={name}
    />
  );
}
