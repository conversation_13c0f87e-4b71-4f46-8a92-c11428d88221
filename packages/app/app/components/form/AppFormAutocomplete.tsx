import { AutocompleteDropdown } from "react-native-autocomplete-dropdown";
import { Controller, useFormContext } from "react-hook-form";
import { View } from "react-native";
import { ErrorMessage } from "@hookform/error-message";
import AppText from "@components/common/AppText";
import { getMessageByPath } from "@utils/message";
import _ from "lodash";
import { AppTextInputProps } from "@components/common/AppTextInput";
import AppFormLabel from "./AppFormLabel";
import React from "react";

export interface AppFormInputProps extends AppTextInputProps {
  name: string;
  label: string;
  transform?: (data: string) => string;
  deform?: (data: string) => string;
  handleBlur?: Function;
  data: string[];
  onSubmit: Function;
  loading: boolean;
}

export default function AppFormAutoComplete({
  name,
  transform,
  deform,
  label,
  onSubmit = _.noop,
  onChangeText = _.noop,
  handleBlur = _.noop,
  data,
  loading,
  placeholder,
  ...otherProps
}: AppFormInputProps) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const msg = getMessageByPath(errors, name);

  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: true,
      }}
      render={({ field: { onChange, value } }) => {
        return (
          <View
            className="flex-1 relative z-1 w-full"
            style={{ position: "relative", backgroundColor: "white" }}
          >
            {label && <AppFormLabel label={label} />}
            <AutocompleteDropdown
              controller={(controller) => {
                controller?.setInputText(value);
              }}
              loading={loading}
              initialValue={{ id: value }}
              inputContainerStyle={{
                backgroundColor: "white",
                borderWidth: 1,
                paddingVertical: 4,
                paddingHorizontal: 4,
                borderColor: "#e5e7eb",
                borderRadius: 8,
              }}
              textInputProps={{
                placeholder,
                style: {
                  color: "black",
                },
              }}
              containerStyle={{ width: "100%" }}
              rightButtonsContainerStyle={{ display: "none" }}
              clearOnFocus={false}
              closeOnBlur={true}
              onBlur={() => {
                onSubmit();
              }}
              closeOnSubmit={true}
              onChangeText={(text) => onChange(text)}
              onSelectItem={(item) => {
                onChange(item?.id);
                onSubmit();
              }}
              // @ts-expect-error
              onSubmit={onSubmit}
              EmptyResultComponent={<React.Fragment />}
              dataSet={data.map((d) => ({ id: d, title: d }))}
              suggestionsListContainerStyle={{
                backgroundColor: "white",
                borderWidth: 0,
                borderColor: "white",
              }}
              suggestionsListTextStyle={{
                color: "black",
              }}
              ItemSeparatorComponent={() => <React.Fragment />}
              {...otherProps}
            />
            <ErrorMessage
              errors={errors}
              name={name}
              render={({ message }) => {
                return <AppText className="text-red-500">{message}</AppText>;
              }}
            />
          </View>
        );
      }}
    />
  );
}
