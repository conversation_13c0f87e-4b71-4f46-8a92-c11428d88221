import { trpc } from '@providers/RootProvider';
import AppText from '@components/common/AppText';

import { Notification } from '../../../../shared/types/notification';
import { CaregiverApprovalStatus } from '../../../../shared/enums/user';
import NotificationWithActions from './NotificationWithActions';
import mixpanel from '@utils/mixpanel';
import { NotificationTypes } from '../../../../shared/enums/notification';

type NotificationWidgetProps = {
  notification?: Notification;
  refetchNotifications: Function;
};

export default function CareGiverApprovalNotification(
  props: NotificationWidgetProps
) {
  const updateApprovalStatus =
    trpc.user.updateCaregiverApprovalStatus.useMutation();
  const user = props.notification?.requestedUserId;

  const onAccept = (callback: Function) => {
    updateApprovalStatus.mutateAsync(
      {
        caregiverId: user._id,
        status: CaregiverApprovalStatus.APPROVED,
      },
      {
        onSuccess: () => {
          callback(CaregiverApprovalStatus.APPROVED, { _id: props.notification?._id });
          // Todo
          mixpanel.trackEvent(
            'NOTIFICATION_ACTION',
            {
              action: 'accepted',
              content: '',
              notificationType: NotificationTypes.CAREGIVER_APPROVAL,
            },
            user._id
          );
        },
        onError: (err) => alert(err),
      }
    );
  };

  const onReject = (callback: Function) => {
    updateApprovalStatus.mutateAsync(
      {
        caregiverId: user._id,
        status: CaregiverApprovalStatus.REJECTED,
      },
      {
        onSuccess: () => {
            callback(CaregiverApprovalStatus.REJECTED, { _id: props.notification?._id }),
            mixpanel.trackEvent(
                'NOTIFICATION_ACTION',
                {
                  action: 'rejected',
                  content: '',
                  notificationType: NotificationTypes.CAREGIVER_APPROVAL,
                },
                user._id
              );
        },

        onError: (err) => alert(err),
      }
    );
  };

  return (
    <NotificationWithActions
      onAccept={onAccept}
      onReject={onReject}
      user={user}
      refetchNotifications={props.refetchNotifications}
      createdAt={props.notification?.createdAt}
    >
      <AppText
        style={{ flexWrap: 'wrap', flex: 1, maxWidth: 300, paddingBottom: 20 }}
        className='text-neutral-500 font-montserratMedium p-2'
      >
        <AppText className='font-montserratBold'>
          {user?.firstname} {user?.lastname}
        </AppText>{' '}
        requested to manage your account on your behalf. Allow them to manage
        your account
      </AppText>
    </NotificationWithActions>
  );
}
