import { View } from "react-native";
import AppText from "@components/common/AppText";

import { Notification } from "../../../../shared/types/notification";

type NotificationWidgetProps = {
    notification?: Notification;
};

export default function DefaultNotificationView(props: NotificationWidgetProps) {
    return (
      <View style={{ height: 150 }} className="justify-between items-center">
        <AppText className="text-sm font-montserratMedium ml-4">
          {props.notification?.content}
        </AppText>
      </View>
    );
  }
  