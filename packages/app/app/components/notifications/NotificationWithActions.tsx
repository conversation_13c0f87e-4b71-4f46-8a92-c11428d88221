import { StyleSheet, View } from 'react-native';
import AppButton from '@components/common/AppButton';
import { useState } from 'react';
import { UserAccount } from '../../../../shared/types/user';
import { Image as CacheImage } from 'react-native-expo-image-cache';
import { getTimeDifference, imageKit } from '@utils/index';
import LogoSimple from '@assets/svg/LogoOutline';
import AppText from '@components/common/AppText';
import { trpc } from '@providers/RootProvider';
import { HelpingHand } from 'lucide-react-native';

type Props = {
  onAccept: Function;
  onReject: Function;
  children: any;
  user: UserAccount;
  createdAt: Date | undefined;
  refetchNotifications: Function;
};

export default function NotificationWithActions(props: Props) {
  const [approvalStatus, setApprovalStatus] = useState('');
  const { user, createdAt } = props;

  const removeNotification = trpc.notification.removeNotification.useMutation();

  function handleAction(status: string, query: Record<string, any>) {
    setApprovalStatus(status);
    removeNotification.mutateAsync(
      { filters: query },
      {
        onSuccess: () => props.refetchNotifications(),
        onError: (err: any) => alert(err),
      }
    );
  }

  return (
    <View style={styles.container}>
      {user?.profilePicture ? (
        <View style={styles.profilePicture}>
          <CacheImage
            uri={imageKit({
              imagePath: user?.profilePicture,
              transform: ['w-100'],
            })}
            preview={{
              uri: imageKit({
                imagePath: user?.profilePicture,
                transform: ['w-30', 'bl-6'],
              }),
            }}
            tint='dark'
            transitionDuration={300}
            style={styles.profilePicture}
          />
          {user.isCaregiver && (
            <View className='w-7 h-7 absolute -bottom-0 -right-0 bg-[#e5f4ff] justify-center items-center rounded-full border-white border-2'>
              <HelpingHand className='w-5 h-5 text-primary' />
            </View>
          )}
        </View>
      ) : (
        <LogoSimple />
      )}
      <View style={{ flex: 5 }}>
        {props.children}
        <View className='flex-row justify-between mx-2'>
          {approvalStatus ? (
            <AppButton variant='disabled-outline' title={approvalStatus} />
          ) : (
            <>
              <AppButton
                title='Accept'
                rounded
                size='small'
                style={styles.actionBtn}
                onPress={() => {
                  props.onAccept(handleAction);
                }}
              />
              <AppButton
                style={styles.actionBtn}
                title='Reject'
                size='small'
                rounded
                variant='secondary-alt'
                onPress={() => {
                  props.onReject(handleAction);
                }}
              />
            </>
          )}
        </View>
      </View>
      <View className='flex-1' style={{ minWidth: 5 }}>
        <AppText
          className='text-neutral-500 font-montserratSmall p-2'
          style={{ fontSize: 12 }}
        >
          {getTimeDifference(createdAt)}
        </AppText>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginHorizontal: 8,
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderColor: '#c8d6ea',
    padding: 0,
    paddingVertical: 10,
    minHeight: 150,
    marginBottom: 2,
  },
  actionBtn: {
    marginHorizontal: 4,
    alignSelf: 'center',
  },
  profilePicture: {
    borderWidth: 0.2,
    borderRadius: 50,
    width: 60,
    height: 60,
  },
});
