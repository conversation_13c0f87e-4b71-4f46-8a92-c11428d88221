import { trpc } from '@providers/RootProvider';
import AppText from '@components/common/AppText';
import mixpanel from '@utils/mixpanel';

import { Notification } from '../../../../shared/types/notification';
import NotificationWithActions from './NotificationWithActions';
import { RABBLEINVITE_STATUS } from '../../../../shared/validators/rabblegroup.validator';
import { NotificationTypes } from '../../../../shared/enums/notification';

type NotificationWidgetProps = {
  notification?: Notification;
  refetchNotifications: Function;
};

export default function RabbleGroupJoinNotification(
  props: NotificationWidgetProps
) {
  const groupId = props.notification?.metadata?.groupId;
  const requestId = props.notification?.metadata?.requestId;
  const groupInfo = trpc.rabbleGroups.getGroupInfo.useQuery({ group: groupId });

  const handleRabbleInvite = trpc.rabbleGroups.handleRabbleInvite.useMutation();
  const user = props.notification?.requestedUserId;

  const onAccept = (callback: Function) => {
    handleRabbleInvite.mutateAsync(
      {
        rabbleGroup: groupId,
        requestedUser: user._id,
        requestStatus: RABBLEINVITE_STATUS.accepted,
        requestId,
      },
      {
        onSuccess: () => {
          callback(RABBLEINVITE_STATUS.accepted, {
            'metadata.groupId': groupId,
            requestedUserId: String(user._id),
          });
          mixpanel.trackEvent(
            'NOTIFICATION_ACTION',
            {
              action: 'accepted',
              content: '',
              notificationType: NotificationTypes.RABBLEGROUP_JOIN_REQUEST,
            },
            user._id
          );
        },
      }
    );
  };

  const onReject = (callback: Function) => {
    handleRabbleInvite.mutateAsync(
      {
        rabbleGroup: groupId,
        requestedUser: user._id,
        requestStatus: RABBLEINVITE_STATUS.rejected,
        requestId,
      },
      {
        onSuccess: () => {
          callback(RABBLEINVITE_STATUS.rejected, {
            'metadata.groupId': groupId,
            requestedUserId: user,
          });
          mixpanel.trackEvent(
            'NOTIFICATION_ACTION',
            {
              action: 'rejected',
              content: '',
              notificationType: NotificationTypes.RABBLEGROUP_JOIN_REQUEST,
            },
            user._id
          );
        },
      }
    );
  };

  return (
    <NotificationWithActions
      onAccept={onAccept}
      onReject={onReject}
      user={user}
      refetchNotifications={props.refetchNotifications}
      createdAt={props.notification?.createdAt}
    >
      <AppText
        style={{ flexWrap: 'wrap', flex: 1, maxWidth: 300, paddingBottom: 20 }}
        className='text-neutral-500 font-montserratMedium p-2'
      >
        <AppText className='font-montserratBold'>
          {user?.firstname} {user?.lastname}
        </AppText>{' '}
        requested to join private group{' '}
        <AppText className='font-montserratBold'>
          {' '}
          {groupInfo.data?.groupName}{' '}
        </AppText>
      </AppText>
    </NotificationWithActions>
  );
}
