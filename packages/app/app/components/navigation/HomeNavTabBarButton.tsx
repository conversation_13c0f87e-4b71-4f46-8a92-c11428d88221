import ConnectBottomLogo from "@assets/svg/ConnectBottomLogo";
import ConnectBottomLogoFilled from "@assets/svg/ConnectBottomLogoFilled";
import HomeIcon from "@assets/svg/navigation/HomeIcon";
import { HomeIconFilled } from "@assets/svg/navigation/HomeIconFilled";
import LearnIcon from "@assets/svg/navigation/LearnIcon";
import LearnIconFilled from "@assets/svg/navigation/LearnIconFilled";
import ProfileIcon from "@assets/svg/navigation/ProfileIcon";
import ProfileIconFilled from "@assets/svg/navigation/ProfileIconFilled";
import ServicesIcon from "@assets/svg/navigation/ServicesIcon";
import ServicesIconFilled from "@assets/svg/navigation/ServicesIconFilled";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import { AppNavParams } from "@navigation/app-navigator/AppNavParams";
import { BottomTabBarButtonProps } from "@react-navigation/bottom-tabs";
import { RouteProp, useIsFocused, useRoute } from "@react-navigation/native";
import clsx from "clsx";
import { View } from "react-native";

interface TabBarButtonProps extends BottomTabBarButtonProps {
  name: string;
  component: JSX.Element;
  active?: boolean;
}

function TabBarButton({
  component,
  name,
  active,
  ...otherProps
}: TabBarButtonProps) {
  return (
    <Pressable
      className="justify-center"
      {...otherProps}
      actionTag={`bottom-tabs-${name}`}
    >
      {component}
      <AppText className={clsx("text-xs mt-2", active && "text-accent")}>
        {name}
      </AppText>
    </Pressable>
  );
}

export default function HomeNavTabBarButton(props: BottomTabBarButtonProps) {
  const isFocused = useIsFocused();
  const { name: screen } = useRoute<RouteProp<AppNavParams>>();

  if (screen === "ConnectNavigator")
    return (
      <TabBarButton
        active={isFocused}
        component={
          isFocused ? <ConnectBottomLogoFilled /> : <ConnectBottomLogo />
        }
        name="Rabbles"
        {...props}
      />
    );
  else if (screen === "ProfileNavigator")
    return (
      <TabBarButton
        active={isFocused}
        component={isFocused ? <ProfileIconFilled /> : <ProfileIcon />}
        name="Profile"
        {...props}
      />
    );
  else if (screen === "ManageNavigator")
    return (
      <TabBarButton
        active={isFocused}
        component={isFocused ? <HomeIconFilled /> : <HomeIcon />}
        name="Home"
        {...props}
      />
    );
  else if (screen === "LearnNavigator")
    return (
      <TabBarButton
        active={isFocused}
        component={isFocused ? <LearnIconFilled /> : <LearnIcon />}
        name="Learn"
        {...props}
      />
    );
  else if (screen === "ServicesNavigator")
    return (
      <TabBarButton
        active={isFocused}
        component={isFocused ? <ServicesIconFilled /> : <ServicesIcon />}
        name="Services"
        {...props}
      />
    );
  return null;
}
