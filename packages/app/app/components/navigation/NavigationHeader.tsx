import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import { StackHeaderProps } from "@react-navigation/stack";
import { View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Custom header component
export default function NavigationHeader(props: StackHeaderProps) {
  const { top } = useSafeAreaInsets();

  const title =
    // @ts-ignore
    props.route.params?.header ||
    (typeof props.options?.headerTitle === "string"
      ? props.options.headerTitle
      : props.route.name);
  const HeaderLeft = props.options?.headerLeft || null;
  // const HeaderRight = props.options?.headerRight || null;

  const handleHeaderLeft = () => {
    const canGoBack = props.navigation.canGoBack();
    if (canGoBack) props.navigation.goBack();
  };

  if (!props.options.headerShown) return null;

  return (
    <View
      style={{ paddingTop: top }}
      className="bg-[#F8FAFC] flex flex-row items-center justify-between"
    >
      <View className="m-3 w-5">
        {props.navigation.canGoBack() && HeaderLeft && (
          <Pressable
            actionTag="go-back icon"
            onPress={handleHeaderLeft}
            hitSlop={10}
          >
            {/* @ts-ignore */}
            <HeaderLeft />
          </Pressable>
        )}
      </View>
      <AppText className="my-3 font-montserratMedium">{title}</AppText>
      <View className="m-3 w-5">
        {/* {HeaderRight && (
            <Pressable onPress={handleHeaderRight}>
              <HeaderRight />
            </Pressable>
          )} */}
      </View>
    </View>
  );
}
