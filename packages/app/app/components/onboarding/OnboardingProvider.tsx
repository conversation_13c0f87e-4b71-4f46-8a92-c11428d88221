import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useState, useEffect, useMemo } from "react";
import { Dimensions, View, Text } from "react-native";
import AppButton from "@components/common/AppButton";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import ProfileIcon from "@assets/svg/ProfileIcon";
import CallIcon from "@assets/svg/CallIcon";
import WebView from "react-native-webview";
import RecommendedIcon from "@assets/svg/RecomendedIcon";
import useScreenTracking from "@hooks/useScreenTracking";
import { RootNavParams } from "../../navigation/root-navigator/RootNavParams";
import OnboardingW2 from "@assets/svg/OnboardingW2";
import EllipseIcon from "@assets/svg/EllipseIcon";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const checkInOptions = [
  {
    icon: <ProfileIcon />,
    title: "View the patient experience",
    description:
      "Explore the first lesson of the Asthma\n learning module and daily check-in",
    isRecommended: false,
    action: null,
    comingSoonLabel: "(coming soon)",
  },
  {
    icon: <CallIcon />,
    title: "Schedule a discovery call",
    description:
      "Explore how other providers use\n myRabble to support patients",
    isRecommended: true,
    action: "https://calendly.com/rabblehealth/30min",
  },
];

export default function OnboardingProvider() {
  const navigation = useNavigation<RootNavigationProp>();
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [webViewUrl, setWebViewUrl] = useState<string | null>(null);
  const { user } = useSession();
  const { setOnboardingProgressScreen } = useScreenTracking();
  const { mutateAsync: saveProgressScreen } = setOnboardingProgressScreen;

  const params =
    useRoute<RouteProp<RootNavParams, "OnboardingProvider">>()?.params;

  const { fromManageScreen } = useMemo(() => params || {}, [params]);

  useEffect(() => {
    if (!fromManageScreen) {
      saveProgressScreen("OnboardingProvider");
    }
  }, [fromManageScreen]);

  const handleOptionSelect = (index: number) => {
    setSelectedOption(index);
  };

  const onSubmit = () => {
    // const selectedAction = checkInOptions[selectedOption]?.action;
    // if (selectedAction) {
      mixpanel.trackEvent(
        "Schedule a Discovery call(Step 8)(Schedule a Meeting)",
        {
          email_or_phone: user?.email || user?.contact?.phone || "",
        },
        user?._id?.toString(),
        "v2"
      );
      setWebViewUrl("https://calendly.com/rabblehealth/30min");
    // }
  };

  return (
    <Screen>
      <View className="flex-row items-center mt-8">
        <ChevronLeft
          color={"#004987"}
          size={22}
          className="ml-4"
          onPress={() =>
            webViewUrl ? setWebViewUrl(null) : navigation.goBack()
          }
        />
        {/* {!webViewUrl && (
          <ProgressBar
            total={5}
            current={6}
            style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
          />
        )} */}
      </View>

      {webViewUrl ? (
        // Render WebView if URL is set
        <WebView
          source={{ uri: webViewUrl }}
          javaScriptEnabled
          style={{ flex: 1 }}
        />
      ) : (
        // Render options if no WebView is active
        <>
          {/* <View className="flex-1 items-center mt-[-16px]">
            <View className="flex-row items-center gap-x-2 mb-[-8px]">
              <ButterFlyIcon height={140} width={90} />
              <QuestionPrompt
                dx={10}
                message="Where would you like to start?"
              />
            </View>

            <View className="flex-1 p-2" style={{ width: cardWidth }}>
              {checkInOptions.map((option, index) => (
                <Pressable
                  key={index}
                  className={`relative flex-row items-center bg-white p-4 mb-4 rounded-lg border border-[#B4DDFF] ${selectedOption === index ? "bg-[#E1F1FF]" : ""
                    }  border-b-[5px] border-b-[#B4DDFF]`}
                  onPress={() => handleOptionSelect(index)}
                >
                  {option?.isRecommended && (
                    <View className="absolute right-0 top-0">
                      <RecommendedIcon />
                    </View>
                    // <Text
                    //   className="absolute right-0 top-0 h-[20px] bg-[#0B79D3] rounded-l-full rounded-r-lg pl-2 pr-2 px-2 text-[15px] color-white"
                    // >
                    //   recommended
                    // </Text>
                  )}
                  {option.icon}
                  <View className="ml-4 mt-2">
                    <Text className="text-base text-[18px] text-[#004987] font-montserrat text-[18px] [font-feature-settings:'liga'_off,'clig'_off]">
                      {option.title}
                    </Text>
                    <Text
                      style={{
                        lineHeight: 15,
                      }}
                      className="text-[#004987] text-base text-[13px] font-montserrat [font-feature-settings:'liga'_off,'clig'_off]"
                    >
                      {option.description}
                    </Text>
                    <Text className="text-blue-600 text-xs">
                      {option?.comingSoonLabel}
                    </Text>
                  </View>
                </Pressable>
              ))}
            </View>
          </View> */}
          <View className="flex-1">
            <View className="flex-1 justify-center item-center">
              <View className="ml-24">
                <OnboardingW2
                  message="Would you like to explore how myRabble can help    your patients?"
                  xMargin={-10}
                  yMargin={20}
                />
              </View>
              <View className="flex mb-12 justify-center item-center">
                <ButterFlyIcon
                  className="self-center mt-[-16px] min-h-[250px]"
                  style={{ transform: [{ scale: 0.68 }] }}
                />
                <View className="self-center mt-[-32px] mr-8">
                  <EllipseIcon />
                </View>
              </View>
            </View>
          </View>
          <View className="flex flex-row items-center justify-between mx-4 mb-4">
            <AnimatedAppButton
              btnContainer="flex flex-row p-1"
              title="SCHEDULE A DISCOVERY CALL"
              variant={"new-primary"}
              className="flex-1 rounded-full"
              textClassName="text-[18.5px] font-montserrat font-bold"
              // disabled={!!selectedOption}
              onPress={onSubmit}
              style={{
                shadowColor: "#003366",
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 1,
                shadowRadius: 1,
                elevation: 5,
              }}
            />
          </View>
        </>
      )}
    </Screen>
  );
}
