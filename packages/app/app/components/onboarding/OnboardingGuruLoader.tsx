import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import EllipseIcon from "@assets/svg/EllipseIcon";
import OnboardingWelcome from "@assets/svg/OnboardingWelcome";
import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import Screen from "@components/common/Screen";
import React, { useEffect } from "react";
import { RootNavigationProp } from "@navigation/root-navigator";
import { useNavigation } from "@react-navigation/native";
import { View } from "react-native";
import { ChevronLeft } from "lucide-react-native";

interface OnBoardingBuildingProps {
  message?: string;
  title?: string;
  onLoad?: () => void;
}

OnboardingLoader.defaultProps = {
  message:
    "Get ready to join other people who are focusing on asthma with myRabble!",
  title: "BUILDING IT OUT...",
};

export default function OnboardingLoader({
  message,
  title,
  onLoad,
}: OnBoardingBuildingProps) {
  const navigation = useNavigation<RootNavigationProp>();

  useEffect(() => {
    onLoad?.();
  }, []);

  return (
    <Screen className="flex-1 relative">
      <ChevronLeft
        color={"#004987"}
        size={22}
        // @ts-expect-error
        className="ml-4 mt-8"
        onPress={() => navigation.goBack()}
      />
      <View className="flex-1 justify-center items-center gap-8">
        <ButterFlyIcon />
        <EllipseIcon />
      </View>

      <View className="bottom-40 w-[232px] items-center self-center mb-8">
        <AppText className="text-center text-[#004987] text-center font-montserrat text-[18px] font-normal leading-normal mb-6">
          {title}
        </AppText>
        <AppText className="text-center text-[#004987] text-center font-montserrat text-[15px] font-normal leading-normal">
          {message}
        </AppText>
      </View>
    </Screen>
  );
}
