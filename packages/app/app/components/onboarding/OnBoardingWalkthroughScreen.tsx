import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import OnboardingW1 from "@assets/svg/OnboardingW1";
import AppButton from "@components/common/AppButton";
import Screen from "@components/common/Screen";
import { RootNavigationProp } from "@navigation/root-navigator";
import { useNavigation } from "@react-navigation/native";
import { ChevronLeft } from "lucide-react-native";
import { Platform, View } from "react-native";
import React, { useEffect, useState } from "react";
import { trpc } from "@providers/RootProvider";
import usePersistedUser from "@hooks/persistUser";
import EllipseIcon from "@assets/svg/EllipseIcon";
import OnboardingW2 from "@assets/svg/OnboardingW2";
import * as Application from "expo-application";
import withPressAnimation from "@components/common/AnimateButton";
import mixpanel from "@utils/mixpanel";
import { useAppProvider } from "@providers/AppProvider";

const AnimatedAppButton = withPressAnimation(AppButton);

const getDeviceId = async () => {
  let deviceId = "";
  if (Platform.OS === "android") {
    deviceId = await Application.getAndroidId();
    console.log("Android Device ID:", deviceId);
  } else if (Platform.OS === "ios") {
    deviceId = (await Application.getIosIdForVendorAsync()) || "";
    console.log("iOS Device ID:", deviceId);
  }
  return deviceId;
};

export default function OnBoardingWalkthroughScreen() {
  const navigation = useNavigation<RootNavigationProp>();

  const { requestLoginOtp, validateLoginOtp } = trpc.auth;
  const [scrollBelleScreen, setScrollBelleScreen] = useState(true);
  const [isEmptyScreen, setEmptyScreen] = useState(false);

  const { mutateAsync: requestOtpAsync, isLoading: isRequestOtpLoading } =
    requestLoginOtp.useMutation();
  const { mutateAsync: validateLoginOtpAsync, isLoading: validatingUser } =
    validateLoginOtp.useMutation();

  const { setPersistedUser } = usePersistedUser();
  const { mutateAsync: saveUserAsync } = setPersistedUser;

  const { setTopics } = useAppProvider();
  const topicsResult = trpc.manage.getTopics.useQuery({});

  useEffect(() => {
    console.log("topics result Effect");
    if (topicsResult?.data) {
      console.log("updated topics context1");
      setTopics({ data: topicsResult.data });
    }
  }, [topicsResult.data]);

  useEffect(() => {
    (async () => {
      setEmptyScreen(true);
      const deviceId = await getDeviceId();
      const res = await requestOtpAsync({
        deviceId,
      });
      // @ts-expect-error
      if (res.otp) {
        await validateLoginOtpAsync(
          {
            // @ts-expect-error
            otp: res.otp,
            deviceId,
          },
          {
            async onSuccess(user) {
              await saveUserAsync(user);
              setEmptyScreen(false);
            },
          }
        );
      }
      topicsResult.refetch();
    })();
  }, []);

  const handleNextScreen = () => {
    mixpanel.trackEvent(
      "Continue clicked (Step 1)(Account Creation)",
      {},
      "",
      "v2"
    );
    navigation.navigate("CreateProfileUserDetailScreen");
  };

  return (
    <Screen className="flex-1">
      {!isEmptyScreen && (
        <ChevronLeft
          color={"#004987"}
          size={22}
          // @ts-expect-error
          className="ml-4 mt-8"
          onPress={() => {
            navigation.navigate("OnBoardingSplashScreen");
          }}
        />
      )}

      <View className="flex-1">
        <View className="flex-1 justify-center item-center">
          {
            <View
              className={`${scrollBelleScreen ? "ml-[96px]" : "ml-[108px]"}`}
            >
              {scrollBelleScreen ? (
                <OnboardingW2
                  height={175}
                  dx={5}
                  dy={2}
                  message="Hi! I’m Belle. Just a few quick steps to make this a personalized experience for you!"
                  style={{ position: "absolute", top: -50 }}
                />
              ) : (
                <OnboardingW1 style={{ position: "absolute", top: -20 }} />
              )}
            </View>
          }
          <View className="flex justify-center item-center">
            <ButterFlyIcon
              // @ts-expect-error
              className="self-center mt-16 min-h-[250px]"
              style={{ transform: [{ scale: 0.68 }] }}
            />
            {
              <View className="self-center mt-[-36px] mr-4">
                <EllipseIcon />
              </View>
            }
          </View>
        </View>
      </View>

      <View className="flex flex-row items-center justify-between mx-4 mb-1">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1 mb-2"
          title="CONTINUE"
          variant={
            validatingUser || isRequestOtpLoading ? "disabled" : "new-primary"
          }
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          style={
            !validatingUser && !isRequestOtpLoading
              ? {
                  shadowColor: "#003366",
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 1,
                  shadowRadius: 1,
                  elevation: 5,
                }
              : {}
          }
          disabled={validatingUser || isRequestOtpLoading}
          onPress={handleNextScreen}
        />
      </View>
    </Screen>
  );
}
