import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React from "react";
import { Dimensions, View, Text } from "react-native";
import AppButton from "@components/common/AppButton";
import DevelopIcon from "@assets/svg/DevelopIcon";
import LearnIcon from "@assets/svg/LearnIcon";
import SpeakIcon from "@assets/svg/SpeakIcon";
import ProgressIcon from "@assets/svg/ProgressIcon";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import withPressAnimation from "@components/common/AnimateButton";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import AppText from "@components/common/AppText";
import mixpanel from "@utils/mixpanel";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const goalOptions = [
  {
    icon: <DevelopIcon />,
    title: "Develop a check-in habit",
    description: "Let's make daily check-ins second nature",
  },
  {
    icon: <LearnIcon />,
    title: "Build my knowledge",
    description: "Learn tips to stay in control",
  },
  {
    icon: <SpeakIcon />,
    title: "Speak with confidence",
    description: "Share your needs and insights with others and with doctors",
    customize: true,
  },
  {
    icon: <ProgressIcon />,
    title: "Track my progress",
    description: "Small steps, big progress!",
  },
];

export default function OnboardingGoals() {
  const navigation = useNavigation<RootNavigationProp>();
  const { topicId, currentIndex, questionAnswer } =
    useRoute<RouteProp<RootNavParams, "OnboardingGoals">>()?.params || {};
  return (
    <Screen>
      <View className="flex-row items-center mt-4">
        <ChevronLeft
          color={"#004987"}
          size={22}
          // @ts-expect-error
          className="ml-4"
          onPress={() => {
            if (currentIndex !== undefined && questionAnswer) {
              navigation.navigate("OnboardingQuestion", {
                topicId,
                currentIndex,
                questionAnswer,
              });
            } else {
              navigation.goBack();
            }
          }}
        />
        <ProgressBar
          total={6}
          current={5}
          style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
        />
      </View>
      <View className="flex-1 items-center">
        <View className="flex-row items-center">
          <ButterFlyIcon height={140} width={90} />
          <QuestionPrompt message="Here’s what we can achieve in 1 month!" />
        </View>

        <View className="flex-1 p-2" style={{ width: cardWidth }}>
          {goalOptions.map((goal, index) => (
            <View
              key={index}
              className={`flex-row items-center bg-white p-4 mb-4 rounded-lg  ${
                goal?.customize ? "ml-[-16px]" : ""
              }`}
            >
              <View className={`text-left`}>{goal.icon}</View>
              <View className="ml-4 flex-1">
                <AppText className="text-base font-montserratSemiBold text-[18px] text-[#004987]">
                  {goal.title}
                </AppText>
                <AppText className="text-[13px] text-[#004987] font-medium">
                  {goal.description}
                </AppText>
              </View>
            </View>
          ))}
        </View>
      </View>
      <View className="flex flex-row items-center justify-between mx-4 mb-4">
        {/* <View className="flex flex-row p-1 mb-2"> */}
        <AnimatedAppButton
          btnContainer="flex flex-row p-1"
          title="CONTINUE"
          variant={"new-primary"}
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          style={{
            shadowColor: "#003366",
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 1,
            shadowRadius: 1,
            elevation: 5,
          }}
          onPress={() => {
            // mixpanel.trackEvent(
            //   'ONBOARDING_ACTION',
            //   {
            //     action: "Final step completed (Step 9) (Account Creation)",
            //   },
            //    '', 
            //   'v2'
            // );
            navigation.replace("OnboardingLastQuestion");
          }}
        />
        {/* </View> */}
      </View>
    </Screen>
  );
}
