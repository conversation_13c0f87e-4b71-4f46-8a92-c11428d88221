import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import OnboardingW1 from "@assets/svg/OnboardingW1";
import AppButton from "@components/common/AppButton";
import Screen from "@components/common/Screen";
import { RootNavigationProp } from "@navigation/root-navigator";
import { useNavigation } from "@react-navigation/native";
import { ChevronLeft, X } from "lucide-react-native";
import { Platform, View } from "react-native";
import React, { useEffect, useState } from "react";
import { trpc } from "@providers/RootProvider";
import usePersistedUser from "@hooks/persistUser";
import EllipseIcon from "@assets/svg/EllipseIcon";
import OnboardingW2 from "@assets/svg/OnboardingW2";
import * as Application from "expo-application";
import withPressAnimation from "@components/common/AnimateButton";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import MissingQuestionPrompts from "@assets/svg/MissingQuestionPrompt";

const AnimatedAppButton = withPressAnimation(AppButton);


export default function OnboardingMissedQuestion() {
  const navigation = useNavigation<RootNavigationProp>();

  const handleNextScreen = () => {
    navigation.navigate("OnboardingLearningQuestion");
  };

  return (
    <Screen className="flex-1">
      <X
        color={"#004987"}
        size={22}
        // @ts-expect-error
        className="ml-4 mt-8"
        onPress={() => navigation.goBack()}
      />

      <View className="flex-1">
        <View className="flex-1 my-16 mx-[-40px]">
          <View className="ml-32 mt-24 absolute top-20 left-8">
            {/* <MissingQuestionPrompts /> */}
            <QuestionPrompt message="Not quite. Let’s correct the question we missed!" />
          </View>
          <View className="flex mb-12 mt-32 flex-start">
            <ButterFlyIcon
              // @ts-expect-error
              className="min-h-[250px]"
              style={{ transform: [{ scale: 0.68 }] }}
            />
          </View>
        </View>
      </View>

      <View className="flex flex-row items-center justify-between mx-4 mb-1">
        {/* <View className="flex flex-row p-1 mb-2"> */}
        <AnimatedAppButton
          btnContainer="flex flex-row p-1 mb-2"
          title="CONTINUE"
          variant={"new-primary"}
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          style={{
            shadowColor: "#003366",
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 1,
            shadowRadius: 1,
            elevation: 5,
          }}
          onPress={handleNextScreen}
        />
        {/* </View> */}
      </View>
    </Screen>
  );
}
