import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import AppButton from "@components/common/AppButton";
import Screen from "@components/common/Screen";
import { RootNavigationProp } from "@navigation/root-navigator";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { ChevronLeft } from "lucide-react-native";
import { View } from "react-native";
import React, { useEffect, useState } from "react";
import EllipseIcon from "@assets/svg/EllipseIcon";
import OnboardingW2 from "@assets/svg/OnboardingW2";
import withPressAnimation from "@components/common/AnimateButton";
import usePersistedUser, { useSession } from "@hooks/persistUser";
import { useAppProvider } from "@providers/AppProvider";
import { trpc } from "@providers/RootProvider";
import useScreenTracking from "@hooks/useScreenTracking";
import OnboardingLoader from "./OnboardingGuruLoader";
import mixpanel from "@utils/mixpanel";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import _ from "lodash";

const AnimatedAppButton = withPressAnimation(AppButton);

export default function OnboardingWelcomeUserScreen() {
  const navigation = useNavigation<RootNavigationProp>();
  const { groupId = "", invitedContacts = [] } =
      useRoute<RouteProp<RootNavParams, "OnboardingWelcomeUserScreen">>()?.params;
  const { setOnboardingProgressScreen } = useScreenTracking();
  const { persistedUser, setPersistedUser } = usePersistedUser();
  const { user } = useSession();

  const groupInfo = trpc.rabbleGroups.getGroupInfo.useQuery(
    { group: groupId },
    { enabled: !!groupId }
  );

  const loginUser = async () => {
    await setOnboardingProgressScreen.mutateAsync("");
    // @ts-expect-error
    await setPersistedUser.mutateAsync(user);
  };

  const handleNext = async () => {
    console.log({ invitedContacts })
    // navigation.navigate("OnboardingDailyCheckIn");
    mixpanel.trackEvent(
      "Rabble community created (Step 7)(Inviting)",
      {
        community_name: groupInfo?.data?.groupName || "",
        community_desc: groupInfo?.data?.groupDescription || "",
        community_guideline: groupInfo?.data?.groupGuidelines || "",
        community_status: groupInfo?.data?.privacy || "",
        email_or_phone: user?.email || user?.contact?.phone || "",
        tags: _.join(groupInfo?.data?.tags, ","),
        contact_access: "granted",
        community_created: "yes",
        invited_contacts: _.join(invitedContacts, ",") || "",
      },
      user?._id?.toString(),
      "v2"
    );
    await loginUser();
  };

  return (
    <Screen className="flex-1">
      <View className="flex-1">
        <View className="flex-1 justify-center item-center">
          <View className="ml-24">
            <OnboardingW2
              message={`Your new Rabble has been successfully created!  Let’s create the first post.`}
              xMargin={8}
              yMargin={20}
              dx={8}
              dxMarginPercent="7.5%"
            />
          </View>
          <View className="flex mb-12 justify-center item-center">
            <ButterFlyIcon
              className="self-center mt-[-16px] min-h-[250px]"
              style={{ transform: [{ scale: 0.68 }] }}
            />
            <View className="self-center mt-[-32px] mr-8">
              <EllipseIcon />
            </View>
          </View>
        </View>
      </View>

      <View className="flex flex-row items-center justify-between mx-4 mb-2">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1"
          title="CONTINUE"
          variant="new-primary"
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          style={{
            shadowColor: "#003366",
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 1,
            shadowRadius: 1,
            elevation: 5,
          }}
          onPress={handleNext}
        />
      </View>
    </Screen>
  );
}
