import withPressAnimation from "@components/common/AnimateButton";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import usePersistedUser from "@hooks/persistUser";
import useScreenTracking from "@hooks/useScreenTracking";
import { RootNavigationProp } from "@navigation/root-navigator";
import { trpc } from "@providers/RootProvider";
import { useNavigation } from "@react-navigation/native";
import ManageDailyTrackersQuestionsSection from "app/elements/manage/ManageDailyTrackersQuestionsSection";
import { endOfDay, startOfToday, startOfWeek } from "date-fns";
import * as Location from "expo-location";
import _, { set } from "lodash";
import { X } from "lucide-react-native";
import React, { useEffect, useState } from "react";
import { Image, View } from "react-native";
import OnboardingLoader from "./OnboardingGuruLoader";
import OnboardingDailyCheckInProcessed from "./OnboardingDailyCheckInProcessed";

const AnimatedAppButton = withPressAnimation(AppButton);

export default function OnboardingDailyCheckInQuestion() {
  const navigation = useNavigation<RootNavigationProp>();
  const [userSelectedTopic, setUserSelectedTopic] = useState<string>();
  const [isCarePartner, setIsCarePartner] = useState(false);
  const [location, setLocation] = useState({});
  const [isDailyCheckInCompleted, setDailyCheckInCompleted] = useState(false);
  const [isOnboardingLoader, setIsOnboardingLoader] = useState(false);

  const { setPersistedUser, user } = usePersistedUser();
  const { data: userData } = user;

  const { setOnboardingProgressScreen } = useScreenTracking();
  const { mutateAsync: saveProgressScreen } = setOnboardingProgressScreen;

  useEffect(() => {
    saveProgressScreen("OnboardingDailyCheckInQuestion");
  }, []);

  const handleNext = async () => {
    console.log({ isDailyCheckInCompleted });
    if (isDailyCheckInCompleted) {
      setIsOnboardingLoader(true);
    }
  };


  useEffect(() => {
    (async () => {
      // Fetch location
      let { coords } = await Location.getCurrentPositionAsync({});
      setLocation({ lat: coords.latitude, lng: coords.longitude });
    })();
  }, []);

  const userTopics = trpc.manage.getUserTopics.useQuery(
    {},
    {
      onSuccess(data) {
        if (!data.length) return;
        setUserSelectedTopic((prev) => prev || String(data[0].topic._id));
        setIsCarePartner(!!data[0].isCarePartner);
      },
    }
  );

  const {
    data: locationIpData,
    isLoading: locationLoading,
    error: locationError,
  } = trpc.lib.locationFromIp.useQuery(
    {},
    {
      enabled: _.isEmpty(location),
    }
  );

  const streak = trpc.manage.getStreak.useQuery(
    { topicId: userSelectedTopic?.toString() || "" },
    { enabled: isDailyCheckInCompleted && !!userSelectedTopic }
  );
  const userDailyTrackersKey = {
    startDate: startOfWeek(new Date(), { weekStartsOn: 0 }),
    endDate: endOfDay(new Date()),
    topicId: userSelectedTopic?.toString(),
  };
  const {
    data: userDailyTrackers,
    isLoading: trackersLoading,
    refetch: refetchTrackers,
  } = trpc.manage.getUserDailyTrackers.useQuery(userDailyTrackersKey, {
    enabled: !!userSelectedTopic?.length && isDailyCheckInCompleted,
  });

  const topics = trpc.manage.getTopics.useQuery(
    // @ts-expect-error
    { topicIds: [userSelectedTopic] },
    {
      enabled: !!userSelectedTopic,
    }
  );

  const [showDailyCheckInProcessedScreen, setShowDailyCheckInProcessedScreen] =
    useState(false);

  useEffect(() => {
    if (
      isDailyCheckInCompleted &&
      _.size(streak) &&
      _.size(userDailyTrackers) &&
      !trackersLoading
    ) {
      setShowDailyCheckInProcessedScreen(true);
    }
  }, [isDailyCheckInCompleted, trackersLoading, streak]);

  if (showDailyCheckInProcessedScreen) {
    return (
      <OnboardingDailyCheckInProcessed
        handleNext={() => {
          navigation.replace("ManageStreakScreen", {
            streakCount: streak?.data?.currentStreak,
            dailyTrackers: userDailyTrackers,
            isDailyCheckInCompleted,
          });
        }}
      />
    );
  }

  return (
    <Screen className="flex-1">
      <AppScrollView>
        <View className="flex-row items-center mt-8">
          <X
            color={"#004987"}
            size={22}
            className="ml-4"
            onPress={() => navigation.goBack()}
          />
          <ProgressBar
            total={6}
            current={5}
            // @ts-expect-error
            className="flex-1 ml-4 mr-4"
          />
        </View>
        <View>
          <Image
            className="w-[178px] h-[40px]"
            source={require("../../assets/img/logo_myRabble.png")}
          />
        </View>
        <View className="flex-1 mt-[58px]">
          <ManageDailyTrackersQuestionsSection
            selectedWeekDay={startOfToday()}
            isCarePartner={isCarePartner}
            topics={topics.data ?? []}
            setDailyCheckInCompleted={setDailyCheckInCompleted}
            isOnboardingCheckIn
            lat={location?.lat || locationIpData?.latitude || 0}
            lng={location?.lng || locationIpData?.longitude || 0}
          />
        </View>
      </AppScrollView>
      <View className="flex flex-row items-center justify-between mx-4 mb-2">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1"
          title="CONTINUE"
          variant={isDailyCheckInCompleted ? "new-primary" : "disabled"}
          disabled={!isDailyCheckInCompleted}
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          style={
            isDailyCheckInCompleted
              ? {
                  shadowColor: "#003366",
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 1,
                  shadowRadius: 1,
                  elevation: 5,
                }
              : {}
          }
          onPress={handleNext}
        />
      </View>
    </Screen>
  );
}
