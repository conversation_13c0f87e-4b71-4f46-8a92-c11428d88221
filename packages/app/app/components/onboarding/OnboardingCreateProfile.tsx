import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import AppButton from "@components/common/AppButton";
import Screen from "@components/common/Screen";
import { RootNavigationProp } from "@navigation/root-navigator";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { ChevronLeft } from "lucide-react-native";
import { Platform, View } from "react-native";
import React, { useEffect } from "react";
import EllipseIcon from "@assets/svg/EllipseIcon";
import OnboardingW2 from "@assets/svg/OnboardingW2";
import * as Application from "expo-application";
import withPressAnimation from "@components/common/AnimateButton";
import useScreenTracking from "@hooks/useScreenTracking";
import mixpanel from "@utils/mixpanel";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";

const AnimatedAppButton = withPressAnimation(AppButton);

const getDeviceId = async () => {
  let deviceId = "";
  if (Platform.OS === "android") {
    deviceId = await Application.getAndroidId();
    console.log("Android Device ID:", deviceId);
  } else if (Platform.OS === "ios") {
    deviceId = (await Application.getIosIdForVendorAsync()) || "";
    console.log("iOS Device ID:", deviceId);
  }
  return deviceId;
};

export default function OnBoardingCreateProfile() {
  const navigation = useNavigation<RootNavigationProp>();
  const { user } =
    useRoute<RouteProp<RootNavParams, "OnBoardingCreateProfile">>().params;
  const { setOnboardingProgressScreen } = useScreenTracking();
  const { mutateAsync: saveProgressScreen } = setOnboardingProgressScreen;

  const handleNext = async () => {
    await mixpanel.trackEvent(
      "Continue clicked (Step 6)(Account Creation)",
      {
        email_or_phone: user?.email || user?.contact?.phone,
      },
      user?._id?.toString(),
      "v2"
    );
    await mixpanel.trackEvent(
      "User signed up",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        opt_in_tracking: user?.marketingConsent ||  false
      },
      String(user?._id),
      "v2"
    );
    navigation.navigate("OnboardingTopics")
  };

  return (
    <Screen className="flex-1">
      <ChevronLeft
        color={"#004987"}
        size={22}
        className="ml-4 mt-8"
        onPress={() => navigation.goBack()}
      />

      <View className="flex-1">
        <View className="flex-1 justify-center item-center">
          <View className="ml-[84px]">
            <OnboardingW2
              message="Your myRabble profile has been successfully created, Just two more questions!"
              // xMargin={15}
              // yMargin={30}
              dy={10}
              style={{ position: "absolute", bottom: -16 }}
              dxMarginPercent="2%"
              dx={10}
            />
          </View>
          <View className="flex justify-center item-center">
            <ButterFlyIcon
              className="self-center min-h-[250px]"
              style={{ transform: [{ scale: 0.68 }] }}
            />
            <View className="self-center mt-[-32px] mr-8">
              <EllipseIcon />
            </View>
          </View>
        </View>
      </View>

      <View className="flex flex-row items-center justify-between mx-4 mb-1">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1 mb-2"
          title="CONTINUE"
          variant="new-primary"
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          style={{
            shadowColor: "#003366",
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 1,
            shadowRadius: 1,
            elevation: 5,
          }}
          onPress={handleNext}
        />
      </View>
    </Screen>
  );
}
