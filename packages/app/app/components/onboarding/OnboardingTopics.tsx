import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import Accordion from "@components/common/AccordionNew";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useMemo, useState, useEffect } from "react";
import { Dimensions, Image, ScrollView, Text } from "react-native";
import { View } from "react-native";
import AppButton from "@components/common/AppButton";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import { trpc } from "@providers/RootProvider";
import LungHealth from "@assets/svg/LungHealth";
import _ from "lodash";
import withPressAnimation from "@components/common/AnimateButton";
import BoneHealth from "@assets/svg/BoneHealth";
import BrainHealth from "@assets/svg/BrainHealth";
import HeartHealth from "@assets/svg/HeartHealth";
import LoadingScreen from "@components/common/LoadingScreen";
import RibbonIcon from "@assets/svg/RibbonIcon";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import useScreenTracking from "@hooks/useScreenTracking";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";
import { useAppProvider } from "@providers/AppProvider";
import { Image as CacheImage } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import constants from "@constants/index";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9; //

const upcomingSections = [
  {
    title: "Bone Health",
    icon: <BoneHealth />,
    extraSpacing: true,
  },
  {
    title: "Head & Neck Health",
    icon: <BrainHealth />,
  },
  {
    title: "Heart Health",
    icon: <HeartHealth />,
  },
];

const CATEGORY_ICONS = {
  Respiratory: <LungHealth color="#5351E8" />,
  Cancer: <RibbonIcon />,
  "Heart Health": <HeartHealth />,
};

const TopicIcons = {
  Asthma: <LungHealth color="#5351E8" />,
  "Asthma - 2": <LungHealth color="#5351E8" />,
  "Breast Cancer": <RibbonIcon />,
  Pediatric: <RibbonIcon color="#e9e94eed" />,
};

export default function OnboardingTopics() {
  const navigation = useNavigation<RootNavigationProp>();
  const route = useRoute<RouteProp<ManageNavParams, "OnboardingTopics">>();
  const fromManageScreen = route?.params?.fromManageScreen ?? false;

  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);

  let { topics } = useAppProvider();
  topics = trpc.manage.getTopics.useQuery(
    { status: "Published" },
    { enabled: !topics?.length }
  );
  const topicIds = _.map(topics?.data, "_id");
  const { setOnboardingProgressScreen } = useScreenTracking();
  const [loadingContext, setLoadingContext] = useState(true);
  const { user } = useSession();
  const { mutateAsync: saveProgressScreen } = setOnboardingProgressScreen;

  useEffect(() => {
    if (topics?.data) {
      setLoadingContext(false);
    }
    setTimeout(() => {
      setLoadingContext(false);
    }, 3000);
  }, [topics?.data]);

  useEffect(() => {
    saveProgressScreen("OnboardingTopics");
  }, []);

  const sections = useMemo(() => {
    if (!topics.data) return [];

    const groupedTopics = _.groupBy(topics.data, (topic) => {
      return topic.category?.[0]?.name || "Respiratory";
    });
    return _.map(groupedTopics, (items, category) => ({
      title: category || "Uncategorized",
      icon: (
        // <Image
        //   source={{ uri: items?.[0]?.category?.[0]?.logo }}
        //   style={{ width: 24, height: 24 }}
        // />
        <CacheImage
          uri={imageKit({
            imagePath:
              _.split(
                items?.[0]?.category?.[0]?.logo,
                constants.bucketBaseUrl
              )[1] || "",
            transform: ["w-500"],
          })}
          transitionDuration={300}
          style={{
            width: 24,
            height: 24,
          }}
        />
      ) || <HeartHealth />,
      items: _.flatMap(items, (topic) => {
        const mappedItem = {
          label: topic.name,
          _id: topic._id,
          icon: (
            // <Image
            //   source={{ uri: topic.logo }}
            //   style={{ width: 24, height: 24 }}
            // />
            <CacheImage
              uri={imageKit({
                imagePath:
                  _.split(topic.logo, constants.bucketBaseUrl)[1] || "",
                transform: ["w-500"],
              })}
              transitionDuration={300}
              style={{
                width: 24,
                height: 24,
              }}
            />
          ),
        };

        if (topic.category === "Cancer") {
          return [
            mappedItem,
            {
              label: "Pediatric",
              icon: TopicIcons["Pediatric"],
              note: "(coming soon!)",
              _id: "pediatric-coming-soon",
            },
          ];
        }

        return mappedItem;
      }),
    }));
  }, [topics.data]);

  if (loadingContext) return <LoadingScreen />;

  if (topics?.isLoading) return <LoadingScreen />;
  return (
    <Screen>
      <View className="flex-row items-center mt-8">
        <ChevronLeft
          color={"#004987"}
          size={22}
          className="ml-4"
          onPress={() => navigation.goBack()}
        />
        <ProgressBar
          total={6}
          current={1}
          style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
        />
      </View>
      <View className="flex-1 items-center">
        <View className="flex-row items-center gap-x-2">
          <ButterFlyIcon height={140} width={90} />
          <QuestionPrompt dx={5} message="What should we focus on?" />
        </View>

        <View className="flex-1" style={{ width: cardWidth }}>
          <Text className="mb-4 text-[#004987] font-Montserrat font-weight-[500] text-[18px]">
            For English speakers
          </Text>
          <ScrollView>
            <Accordion
              sections={[...sections, ...upcomingSections]}
              setActiveItem={setSelectedTopic}
              activeItem={selectedTopic}
            />
          </ScrollView>
        </View>
      </View>
      <View className="flex flex-row items-center justify-between mx-4 mb-1">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1 mb-2"
          title="CONTINUE"
          variant={
            !_.includes(topicIds, selectedTopic) ? "disabled" : "new-primary"
          }
          className="flex-1 rounded-full"
          disabled={!_.includes(topicIds, selectedTopic)}
          textClassName="text-[21px]"
          style={
            _.includes(topicIds, selectedTopic)
              ? {
                  shadowColor: "#003366",
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 1,
                  shadowRadius: 1,
                  elevation: 5,
                }
              : {}
          }
          onPress={() => {
            mixpanel.trackEvent(
              "Health focus selected (Step 7)(Account Creation)",
              {
                email_or_phone: user?.email || user?.contact?.phone || "",
                category_name:
                  _.find(topics?.data, { _id: selectedTopic })?.category || "",
                sub_category:
                  _.find(topics?.data, { _id: selectedTopic })?.name || "",
              },
              user?._id?.toString(),
              "v2"
            );
            navigation.navigate("OnboardingQuestion", {
              topicId: selectedTopic,
              fromManageScreen,
            });
          }}
        />
      </View>
    </Screen>
  );
}
