import ButterFlyIcon from "@assets/svg/ButterFlyIcon";

import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft, X } from "lucide-react-native";
import React, { useMemo, useState, useEffect } from "react";
import { Dimensions, View, Image } from "react-native";
import AppButton from "@components/common/AppButton";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import AppText from "@components/common/AppText";
import BookIcon from "@assets/svg/BookIcon";
import { trpc } from "@providers/RootProvider";
import _ from "lodash";
import RiskMeter from "@assets/svg/RiskMeter";
import clsx from "clsx";
import { ScrollView } from "react-native-gesture-handler";
import CorrectQuestionIcon from "@assets/svg/CorrectIcon";
import { QuestionTypes } from "../../../../shared/types/manage";
import useScreenTracking from "@hooks/useScreenTracking";
import mixpanel from "@utils/mixpanel";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.95;
const asthmaZoneCorrectAnswers = ["Asthma Zones"];
const asthmaSeverityCorrectAnswers = [
  "symptoms throughout the day",
  "frequent nighttime awakenings",
  "unable to perform daily activities",
  "2 or more exacerbations per year",
];

export default function OnboardingLearningQuestion() {
  const navigation = useNavigation<RootNavigationProp>();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [questionAnswer, setQuestionAnswer] = useState<any[]>([]);

  const subscribeTopic = trpc.manage.upsertUserTopic.useMutation();
  const userTopics = trpc.manage.getUserTopics.useQuery({});
  const topics = trpc.manage.getTopics.useQuery({
    ...{ topicIds: ["6703f2a9ab6dd1cba828a588"] },
  });

  const { setOnboardingProgressScreen } = useScreenTracking();
  const { mutateAsync: saveProgressScreen } = setOnboardingProgressScreen;

  useEffect(() => {
    userTopics.refetch();
  }, [selectedOptions]);

  useEffect(() => {
    saveProgressScreen("OnboardingLearningQuestion");
  }, []);

  const learningQuestions = useMemo(() => {
    return topics?.data?.[0]?.learningQuestions || [];
  }, [topics?.data]);

  const handleContinue = async () => {
    if (
      selectedOptions.length &&
      !asthmaZoneCorrectAnswers?.some((p) => _.includes(selectedOptions, p)) &&
      !asthmaSeverityCorrectAnswers?.every((p) =>
        _.includes(selectedOptions, p)
      )
    ) {
      navigation.navigate("OnboardingMissedQuestion");
      return;
    }
    // mixpanel.trackEvent(
    //   "ONBOARDING_ACTION",
    //   {
    //     action: "Asthma education (Step 1)",
    //   },
    //   "",
    //   "v2"
    // );
    if (currentIndex) {
      // mixpanel.trackEvent(
      //   "ONBOARDING_ACTION",
      //   {
      //     action: "Asthma education finished (Step 2)",
      //   },
      //   "",
      //   "v2"
      // );
      navigation.replace("OnboardingDailyCheckIn");
    }
    setCurrentIndex(currentIndex + 1);
    // @ts-expect-error
    const existingAnswers = userTopics?.data[0]?.questionAnswers || [];

    const updatedQuestionAnswers = [
      ...existingAnswers,
      {
        question: currentQuestion?._id,
        selectedOption: questionAnswer.map((answer) => answer.selectedOption),
        answerText: _.last(questionAnswer.map((answer) => answer.answerText)),
      },
    ];
    const res = await subscribeTopic.mutateAsync({
      topic: "6703f2a9ab6dd1cba828a588",
      // @ts-expect-error
      questionAnswers: updatedQuestionAnswers,
    });
    setSelectedOptions([]);
  };

  const currentQuestion = learningQuestions[currentIndex];

  const handleOptionSelect = (optionValue: string, option: any) => {
    if (currentQuestion?.type === QuestionTypes.MultiSelect) {
      setQuestionAnswer((prev) => [
        ...prev,
        {
          quesion: currentQuestion?._id,
          selectedOption: option._id,
          answerText: option.value,
        },
      ]);
      setSelectedOptions((prevSelected) => {
        if (prevSelected.includes(optionValue)) {
          return prevSelected.filter((value) => value !== optionValue);
        } else {
          return [...prevSelected, optionValue];
        }
      });
    } else {
      setQuestionAnswer([
        {
          question: currentQuestion?._id,
          selectedOption: option._id,
          answerText: option.value,
        },
      ]);
      setSelectedOptions([optionValue]);
    }
  };

  const isCorrectAnswer =
    selectedOptions.length &&
    (currentQuestion?.type === QuestionTypes.MultiSelect
      ? asthmaSeverityCorrectAnswers.every((p) =>
          _.includes(selectedOptions, p)
        )
      : asthmaZoneCorrectAnswers.some((p) => _.includes(selectedOptions, p)));

  return (
    <Screen className="bg-gray-100 px-[6px] py-6">
      <ScrollView>
        {/* Top Navigation */}
        <View className="flex-row items-center mb-4 mt-8">
          <X
            color={"#004987"}
            size={20}
            className="ml-4"
            onPress={() => navigation.replace("OnboardingLastQuestion")}
          />
          <ProgressBar
            total={learningQuestions.length}
            current={currentIndex + 1}
            // @ts-expect-error
            className="flex-1 ml-4 mr-4"
          />
        </View>

        <View>
          <Image
            className="w-[178px] h-[40px]"
            source={require("../../assets/img/logo_myRabble.png")}
          />
        </View>

        <View className="bg-[#ffffff] shadow-lg p-[12px] m-[10px] rounded-2xl mt-8">
          <View className="flex-column justify-center">
            <View className="flex-row justify-center">
              <BookIcon />
            </View>
            <AppText className="text-orange-500 font-montserratBold text-sm text-center mt-2">
              ASTHMA ZONES & SEVERITY
            </AppText>
          </View>

          <AppText className="text-[#004987] text-lg font-montserratBold mt-8 text-center font-montserrat text-[26px] font-montserratSemiBold leading6normal">
            {currentQuestion?.question}
          </AppText>

          {currentQuestion?.type === QuestionTypes.MultiSelect && (
            <AppText className="text-[14px] font-montserratRegular text-[#00284A] px-[24px] text-center mb-[8px]">
              (Select all that apply)
            </AppText>
          )}

          <View className="mt-6">
            {currentQuestion?.question !== "How do we track today’s symptoms?"
              ? _.map(currentQuestion?.options, (option) => {
                  return (
                    <View
                      key={option?.value}
                      className=""
                      // style={{ width: cardWidth }}
                    >
                      <Pressable
                        className={clsx(
                          "rounded-lg border border-[#B4DDFF] border-b-[5px] bg-white py-3 px-4 mb-3 shadow-md",
                          selectedOptions.includes(option?.value) &&
                            "bg-[#E1F1FF]"
                        )}
                        style={{
                          elevation: 4,
                          shadowColor: "#000",
                          shadowOffset: { width: 0, height: 2 },
                          shadowOpacity: 0.2,
                          shadowRadius: 5,
                        }}
                        onPress={() =>
                          handleOptionSelect(option?.value, option)
                        }
                      >
                        <AppText className="text-[#004987] font-montserrat text-[16px] font-medium leading-normal">
                          {option?.value}
                        </AppText>
                      </Pressable>
                    </View>
                  );
                })
              : currentQuestion?.options?.length && (
                  <View className="flex-row justify-between w-full ml-[4px] space-x-[4px]">
                    <Pressable
                      className={clsx(
                        "border border-blue-300 flex-1 rounded-xl py-2 border-b-[5px] border-[#B4DDFF] gap-2",
                        selectedOptions.includes(
                          currentQuestion?.options[0]?.value
                        ) && "bg-[#E1F1FF]"
                      )}
                      onPress={() =>
                        handleOptionSelect(
                          currentQuestion?.options[0]?.value || "",
                          currentQuestion?.options[0] || {}
                        )
                      }
                    >
                      <AppText className="text-green-500 font-montserratSemiBold text-[16px] text-center">
                        {currentQuestion?.options[0]?.description[0]?.label ||
                          ""}
                      </AppText>
                      <AppText className="text-yellow-500 font-montserratSemiBold text-[16px] text-center">
                        {currentQuestion?.options[0]?.description[1]?.label ||
                          ""}
                      </AppText>
                      <AppText className="text-red-500 font-montserratSemiBold text-[16px] text-center">
                        {currentQuestion?.options[0]?.description[2]?.label ||
                          ""}
                      </AppText>
                      <AppText className="py-[16px] mt-12 text-[#004987] text-center font-montserrat text-[16px] font-montserratSemiBold leading-normal mr-3">
                        {currentQuestion?.options[0]?.value}
                      </AppText>
                    </Pressable>
                    <Pressable
                      className={clsx(
                        "border flex-1  rounded-xl py-2 items-center border-b-[5px] border-[#B4DDFF] gap-2 font-montserratSemiBold",
                        selectedOptions.includes(
                          currentQuestion?.options[1]?.value
                        ) && "bg-[#E1F1FF]"
                      )}
                      onPress={() =>
                        handleOptionSelect(
                          currentQuestion?.options[1]?.value || "",
                          currentQuestion?.options[0] || {}
                        )
                      }
                    >
                      <RiskMeter />
                      <AppText className="text-[#004987] text-[13px] font-montserratMedium text-center">
                        {currentQuestion?.options[1]?.description}
                      </AppText>
                      <AppText className="py-[16px] mr-1 mt-12 text-[#004987] text-center font-montserrat text-[16px]  font-montserratSemiBold leading-normal">
                        {currentQuestion?.options[1]?.value}
                      </AppText>
                    </Pressable>
                  </View>
                )}
          </View>
        </View>
        {/* Continue Button */}
        <View
          className={`flex justify-between ${
            isCorrectAnswer ? "bg-[#E1F1FF] border-t border-t-[#0B79D3]" : ""
          } ${
            currentQuestion?.question === "How do we track today’s symptoms?"
              ? `pb-32 ${isCorrectAnswer ? "mt-24" : "mt-36"}`
              : `pb-16 ${isCorrectAnswer ? "" : "mt-6"}`
          } `}
        >
          {isCorrectAnswer ? (
            <View className="flex flex-row items-center ml-[37px] mt-4">
              <CorrectQuestionIcon />
              <AppText className="ml-2 text-[13px] text-[#004987] font-montserratBold">
                CORRECT!
              </AppText>
            </View>
          ) : null}
          <View
            className={`flex-1 justify-end items-center mx-4 ${
              currentQuestion?.question === "How do we track today’s symptoms?"
                ? "mt-8"
                : "mt-8"
            } `}
          >
            <AnimatedAppButton
              btnContainer="flex flex-row p-1"
              title="CONTINUE"
              variant={selectedOptions.length ? "new-primary" : "disabled"}
              className="flex-1 rounded-full"
              textClassName="text-[21px]"
              onPress={handleContinue}
              style={
                selectedOptions.length
                  ? {
                      shadowColor: "#003366",
                      shadowOffset: { width: 0, height: 3 },
                      shadowOpacity: 1,
                      shadowRadius: 1,
                      elevation: 6,
                    }
                  : {}
              }
            />
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
}
