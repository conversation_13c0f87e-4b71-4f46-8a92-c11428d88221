import { Dimensions, Image } from 'react-native';
import { Image as CacheImage } from 'react-native-expo-image-cache';
import { imageKit } from '@utils/index';
import { Fragment } from 'react';

type Props = {
    url: string | undefined;
}

const { width } = Dimensions.get('screen');
const MARGIN = 0;
const postImageWidth = width - 2 * MARGIN;
const postImageHeight = (postImageWidth / 5) * 3;

export default function Banner({ url }: Props) {
    return url ? (
        <CacheImage
            uri={url}
            preview={{
                uri: url
            }}
            tint="dark"
            transitionDuration={300}
            style={{
                height: postImageHeight,
                width: '100%',
            }}
        />
    ) : <Fragment />;
}
