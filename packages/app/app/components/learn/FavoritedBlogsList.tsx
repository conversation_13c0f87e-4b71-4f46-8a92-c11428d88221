import AppText from "@components/common/AppText";
import LoadingScreen from "@components/common/LoadingScreen";
import { trpc } from "@providers/RootProvider";
import { View, FlatList } from "react-native";
import BlogListItem from "./BlogListItem";
import { Blog } from "../../../../shared/types/Blog";

export default function FavoritedBlogsList() {
  const { data: favoritedBlogs, isLoading, error } = trpc.blog.getFavoritedBlogs.useQuery();

  if (isLoading) {
    return (
      <View className="mt-8">
        <LoadingScreen />
      </View>
    );
  }

  if (error) {
    return (
      <View
        className="mt-8 bg-white rounded-xl p-6"
        style={{
          elevation: 2,
          shadowColor: "#004987",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }}
      >
        <AppText className="text-center text-lg font-montserratSemiBold text-primary mb-2">
          Error Loading Favorites
        </AppText>
        <AppText className="text-center text-gray-500">
          Unable to load your favorite blogs. Please try again.
        </AppText>
      </View>
    );
  }

  if (!favoritedBlogs || favoritedBlogs.length === 0) {
    return (
      <View
        className="mt-8 bg-white rounded-xl p-6"
        style={{
          elevation: 2,
          shadowColor: "#004987",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }}
      >
        <AppText className="text-center text-lg font-montserratSemiBold text-primary mb-2">
          No Favorites Yet
        </AppText>
        <AppText className="text-center text-gray-500">
          Your favorite blogs will appear here. Start exploring and tap the heart icon to save blogs you love!
        </AppText>
      </View>
    );
  }

  return (
    <View className="mt-8">
      <FlatList
        data={favoritedBlogs}
        keyExtractor={(item) => item._id?.toString() || ""}
        renderItem={({ item }) => {
          // Extract the blog from the favorited blog item
          const blog = item.blog as Blog;
          return <BlogListItem blog={blog} />;
        }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
      />
    </View>
  );
}
