import AppText from "@components/common/AppText";
import { View } from "react-native";

type Props = {
  value: string | undefined;
  classNames: string | undefined;
};

export default function Title({ value, classNames }: Props) {
  return (
    <View>
      <AppText
        className={[
          "mt-3 text-xl text-primary font-montserratBold",
          classNames,
        ].join(" ")}
      >
        {value}
      </AppText>
    </View>
  );
}
