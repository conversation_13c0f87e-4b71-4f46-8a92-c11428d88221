import { <PERSON><PERSON><PERSON>, Modal, View } from "react-native";
import BlogListItem from "./BlogListItem";
import { Blog, BlogStatus } from "../../../../shared/types/Blog";
import Pressable from "@components/common/Pressable";
import FilterIcon from "@assets/svg/services/FilterIcon";
import { useMemo, useState, useEffect } from "react";
import AppButton from "@components/common/AppButton";
import { trpc } from "@providers/RootProvider";
import _ from "lodash";
import AppText from "@components/common/AppText";
import AppCheckBox from "@components/common/AppCheckbox";

type Props = {
  data: Blog[] | undefined;
  setFilterTags?: (v: string[]) => void;
  filterTags?: string[];
  userSelectedTopic?: string;
};

export default function BlogList(props: Props) {
  const [showModal, setShowModal] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // Fetch all tags directly from the backend
  const allTagsQuery = trpc.blog.tags.useQuery({ scope: ["learn"] });

  const manageTopics = trpc.manage.getTopics.useQuery({
    status: "Published",
    ...(props?.userSelectedTopic
      ? { topicIds: [props.userSelectedTopic] }
      : {}),
  });

  trpc.manage.getUserTopics.useQuery(
    {},
    {
      initialData: [],
      onSuccess(data) {
        if (!data.length) return;

        // Extract tag IDs from user topics
        const tagIds = _.flatMap(
          data,
          (userTopic) =>
            userTopic?.topic.tags?.map((tag) =>
              typeof tag === "string" ? tag : tag._id?.toString()
            ) || []
        );

        // Set the tag IDs for filtering
        if (tagIds.length > 0) {
          props?.setFilterTags?.(tagIds);
        }
      },
    }
  );

  const blogs = trpc.blog.getBlogs.useQuery({
    status: BlogStatus.PUBLISHED,
  });

  // Create a mapping of tag IDs to tag objects
  const tagIdToTagMap = useMemo(() => {
    const map = new Map();

    // Add tags from the direct tags query
    if (allTagsQuery.data) {
      allTagsQuery.data.forEach((tag) => {
        map.set(tag._id.toString(), tag);
      });
    }

    // Add tags from topics
    if (manageTopics?.data) {
      manageTopics.data.forEach((topic) => {
        (topic.tags || []).forEach((tag) => {
          const tagId = typeof tag === "string" ? tag : tag._id?.toString();
          const tagName = typeof tag === "string" ? tag : tag.tag;
          if (tagId) {
            map.set(tagId, { _id: tagId, tag: tagName });
          }
        });
      });
    }

    // Add tags from blogs
    if (blogs?.data) {
      blogs.data.forEach((blog) => {
        (blog.tags || []).forEach((tag) => {
          if (tag?._id) {
            map.set(tag._id.toString(), tag);
          }
        });
      });
    }

    return map;
  }, [allTagsQuery.data, manageTopics?.data, blogs?.data]);

  // Get all available tags for the filter modal
  const tags = useMemo(() => {
    return Array.from(tagIdToTagMap.values());
  }, [tagIdToTagMap]);

  useEffect(() => {
    if (props?.filterTags) {
      setSelectedTags(props?.filterTags);
    }
  }, [props?.filterTags]);

  // Helper function to get tag name from ID for debugging
  const getTagName = (tagId: string) => {
    const tag = tagIdToTagMap.get(tagId);
    return tag ? tag.tag : tagId;
  };

  return (
    <View>
      <View className="mt-1">
        {/* Title and Filter Icon */}
        <View className="flex flex-row justify-between items-center">
          <AppText className="text-lg font-bold">
            {/* Display selected tag names instead of IDs */}
            {selectedTags
              .map((tagId) => {
                const tag = tagIdToTagMap.get(tagId);
                return tag ? tag.tag : "";
              })
              .filter(Boolean)
              .join(", ")}
          </AppText>
          <Pressable onPress={() => setShowModal(true)}>
            <FilterIcon width={24} height={24} />
          </Pressable>
        </View>

        {/* Divider Below */}
        <View
          className="h-[1px] bg-[#AFAFAF] mt-2"
          style={{ marginRight: 50 }}
        />
      </View>
      <Modal
        visible={showModal}
        onRequestClose={() => setShowModal(false)}
        animationType="fade"
        transparent
      >
        <View className="flex-1  bg-black/30">
          <Pressable className="flex-1" onPress={() => setShowModal(false)} />
          <View className=" bg-[#f5f7f9] p-4 rounded-t-xl">
            <View className="bg-[#b0c7da] w-10 h-1 self-center" />

            {/* Filter */}
            <View className="my-4">
              <AppText className="text-lg font-bold mb-2">Select Tags</AppText>
              <FlatList
                data={tags}
                keyExtractor={(item) => item._id?.toString() || ""}
                renderItem={({ item }) => {
                  const tagId = item._id?.toString() || "";
                  const isSelected = selectedTags.includes(tagId);

                  return (
                    <Pressable
                      onPress={() => {
                        setSelectedTags((prev) =>
                          isSelected
                            ? prev.filter((id) => id !== tagId)
                            : [...prev, tagId]
                        );
                      }}
                    >
                      <View className="flex-row mb-4">
                        <AppCheckBox active={isSelected} pointerEvents="none" />
                        <View className="ml-2">
                          <AppText className="text-base">{item.tag}</AppText>
                        </View>
                      </View>
                    </Pressable>
                  );
                }}
              />
            </View>

            <View className="flex-row justify-between my-4 mb-8">
              <AppButton
                variant="outline"
                className="rounded-lg px-4 py-2 m-[4px]"
                onPress={() => {
                  setSelectedTags([]);
                  props?.setFilterTags?.([]);
                  setShowModal(false);
                }}
              >
                Reset
              </AppButton>
              <AppButton
                className="rounded-lg px-4 py-2 m-[4px]"
                onPress={() => {
                  // setFilter(selectedFilter);
                  props?.setFilterTags?.(selectedTags);
                  setShowModal(false);
                }}
              >
                Apply Filter
              </AppButton>
            </View>
          </View>
        </View>
      </Modal>
      {props.data?.map((blog, index) => (
        <BlogListItem key={blog._id?.toString() || index} blog={blog} />
        // <FlatList
        //     data={props.data}
        //     renderItem={({ item }) => (
        //         <BlogListItem blog={item} />
        //     )}
        //     keyExtractor={(item, index) => item._id ? item._id.toString() : index.toString()}
        // />
      ))}
    </View>
  );
}
