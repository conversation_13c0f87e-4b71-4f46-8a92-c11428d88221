import React, { useEffect } from "react";
import { View, StyleSheet } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";

interface AnimatedNumberProps {
  value: number;
  style?: any;
}

export const AnimatedNumber: React.FC<AnimatedNumberProps> = ({
  value,
  style,
}) => {
  const translateY = useSharedValue(0);
  const displayValue = useSharedValue(value);

  useEffect(() => {
    if (value !== displayValue.value) {
      // Start from below
      translateY.value = 30;

      // Animate up
      translateY.value = withSpring(0, {
        damping: 15,
        stiffness: 100,
        mass: 0.5,
      });

      // Update the display value (delayed just slightly for animation sync)
      displayValue.value = value;
    }
  }, [value]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  return (
    <View style={[styles.container, style]}>
      <Animated.Text
        className="text-[#0B79D3] text-center font-montserrat text-[18px] leading-normal mt-1.5"
        style={animatedStyle}
      >
        {displayValue.value}
      </Animated.Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 24,
    overflow: "hidden",
  },
});
