import Building from "@assets/svg/services/Building";
import Time from "@assets/svg/services/Time";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import { useSession } from "@hooks/persistUser";
import useAuthGuard from "@hooks/useAuthGuard";
import { ServiceListing } from "@models/Service";
import { ServicesNavParams } from "@navigation/services-navigator/profile-navigator/ServicesNavParams";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import mixpanel from "@utils/mixpanel";
import { format, parseISO } from "date-fns";
import { useCallback, useState } from "react";
import {
  Image,
  NativeSyntheticEvent,
  TextLayoutEventData,
  View,
} from "react-native";

const numOflineToShow = 2;

interface Props {
  item: ServiceListing;
  categoryName?: any;
}

export default function ServiceCard({ item, categoryName }: Props) {
  const { _id, title, organization, logo, description, date } = item;
  const [numLinesExceeded, setNumLinesExceeded] = useState(false);
  const { user } = useSession();

  const navigation = useNavigation<StackNavigationProp<ServicesNavParams>>();

  const onTextLayout = useCallback(
    (e: NativeSyntheticEvent<TextLayoutEventData>) => {
      setNumLinesExceeded(e.nativeEvent.lines.length >= numOflineToShow);
    },
    []
  );

  const authGuard = useAuthGuard();

  const goToServiceDetails = (id: string) => {
    mixpanel.trackEvent(
      "Services post card clicked(Services Screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        category_name: categoryName || "",
        service_post_name: title || "",
        service_post_desc: description || "",
      },
      String(user?._id),
      "v2"
    );
    mixpanel.trackEvent(
      "Services post details view (Services Screen)",
      {
        email: user?.email || "",
        category_name: categoryName || "",
        service_post_name: title || "",
        service_post_desc: description || "",
      },
      String(user?._id),
      "v2"
    );
    authGuard(() => {
      navigation.navigate("ServicesDetailScreen", {
        serviceId: id,
      });
    });
  }

  return (
    <Pressable
      actionTag="service card item"
      className="bg-white rounded-lg p-4 mt-2 mx-2"
      style={{
        elevation: 4,
        shadowColor: "#004987",
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      }}
      onPress={() => goToServiceDetails(_id)}
    >
      <View className="flex flex-row">
        <Image
          className="w-[60px] rounded-md mr-6"
          source={{ uri: logo }}
          resizeMode="contain"
        />
        <View className="flex flex-col justify-center max-w-[80%]">
          <AppText
            numberOfLines={2}
            ellipsizeMode="tail"
            className="text-base text-primary font-montserratSemiBold overflow-hidden whitespace-nowrap"
          >
            {title}
          </AppText>
          <View className="flex flex-row items-center">
            <Building />
            <AppText
              numberOfLines={1}
              ellipsizeMode="tail"
              className="max-w-[90%] mx-1 text-sm text-neutral-500 overflow-hidden"
            >
              {organization}
            </AppText>
          </View>
          {date && (
            <View className="flex flex-row items-center">
              <Time />
              <AppText className="text-sm text-neutral-500">
                {format(parseISO(date), "hh:mm a")}
              </AppText>
            </View>
          )}
        </View>
      </View>

      <View className="mt-2 text-[#333941]">
        <AppText numberOfLines={numOflineToShow} onTextLayout={onTextLayout}>
          {description}
        </AppText>
        {numLinesExceeded && (
          <AppText className="text-accent font-montserratBold self-end">
            Read More
          </AppText>
        )}
      </View>
    </Pressable>
  );
}
