import CloseIcon from '@assets/svg/services/CloseIcon';
import FilterIcon from '@assets/svg/services/FilterIcon';
import AppText from '@components/common/AppText';
import ModalBottomSheet from '@components/common/ModalBottomSheet';
import Pressable from '@components/common/Pressable';
import _ from 'lodash';
import React, { useState } from 'react';
import { PressableProps } from 'react-native';
interface Props extends PressableProps {
  name: string;
  isVisible?: boolean;
  state: boolean;
  onRequestClose?: () => void;
  onBottomSheetOpen?: () => void;
  onSheetStatusChange?: (status: boolean) => void;
  onDeleteState?: () => void;
}

export default function Filter(props: React.PropsWithChildren<Props>) {
  const [bottomSheetVisible, setBottomSheetVisible] = useState(false);
  const isControlledComponent = props.isVisible !== undefined;

  const onOpenBottomSheet = () => {
    props.onRequestClose?.();
    props.onSheetStatusChange?.(true);
    setBottomSheetVisible(true);
  };
  const onCloseBottomSheet = () => {
    props.onBottomSheetOpen?.();
    props.onSheetStatusChange?.(false);
    setBottomSheetVisible(false);
  };

  const onPressDeleteState = () => {
    props.onDeleteState?.();
  };

  return (
    <>
      <Pressable
        actionTag='filter bottom sheet'
        onPress={onOpenBottomSheet}
        className='rounded-full h-9 w-30 items-center border-[1px] border-[#C9D6EA] mt-2 mx-2 px-0.5 py-1 block flex-row'
        {...props}
      >
        <AppText className='mx-1' numberOfLines={1} ellipsizeMode='tail'>{props.name}</AppText>
        {props.state && props.name !== 'state' ? (
          <Pressable onPress={onPressDeleteState} actionTag='filter close'>
            <CloseIcon />
          </Pressable>
        ) : (
          <FilterIcon />
        )}
      </Pressable>
      <ModalBottomSheet
        visible={!isControlledComponent ? bottomSheetVisible : props.isVisible}
        onRequestClose={onCloseBottomSheet}
        onClose={onCloseBottomSheet}
      >
        {props.children}
      </ModalBottomSheet>
    </>
  );
}
