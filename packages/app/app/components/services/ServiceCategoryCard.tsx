import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import { Category } from "@models/Service";
import React, { memo } from "react";
import { View, StyleSheet, Dimensions, Image } from "react-native";

const { width } = Dimensions.get("screen");
const MARGIN = 10;
const cardWidth = width / 2 - MARGIN * 2;

interface ServiceCategoryCardProps {
  item: Category;
  onPress: (categoryId: string, categoryName: string) => void;
}

function ServiceCategoryCard(props: ServiceCategoryCardProps) {
  const { item, onPress } = props;

  return (
    <View
      className="flex-row justify-between rounded-xl bg-white"
      style={styles.row}
    >
      <Pressable
        actionTag="service category card item"
        onPress={() => onPress(item._id!, item.title)}
        className="flex flex-1 bg-white px-[4%] py-4 rounded-lg border-[0.3px] border-[#C9D6EA] h-48 items-center"
      >
        <Image
          source={{
            uri: item.logo,
          }}
          resizeMode="contain"
          className="h-16 w-16"
        />
        <AppText
          numberOfLines={1}
          className="mt-3 text-lg text-center text-[#191D23]  font-montserratSemiBold"
        >
          {item.title}
        </AppText>
        <AppText
          numberOfLines={2}
          className="mt-1 leading-4 text-center text-[#313131] font-montserratMedium text-[12px]"
        >
          {item.description}
        </AppText>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  row: {
    width: cardWidth,
    shadowColor: "#004987",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default ServiceCategoryCard;
