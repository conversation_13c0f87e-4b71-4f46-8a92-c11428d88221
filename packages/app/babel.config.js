module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        require.resolve('babel-plugin-module-resolver'),
        {
          alias: {
            app: './app',
            '@components': './app/components',
            '@providers': './app/providers',
            '@services': './app/services',
            '@assets': './app/assets',
            '@config': './app/config',
            '@theme': './app/theme',
            '@constants': './app/constants',
            '@hooks': './app/hooks',
            '@models': './app/models',
            '@navigation': './app/navigation',
            '@screens': './app/screens',
            '@utils': './app/utils',
          },
          extensions: ['.ts', '.tsx'],
        },
      ],
      'nativewind/babel',
      'react-native-reanimated/plugin',
    ],
  };
};
