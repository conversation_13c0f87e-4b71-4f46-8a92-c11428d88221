# The manifest for the "prod" environment.
# Read the full specification for the "Environment" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/environment/

# Your environment name will be used in naming your resources like VPC, cluster, etc.
name: prod
type: Environment

# Import your own VPC and subnets or configure how they should be created.
network:
  vpc:
    id: vpc-0fb06e48a078ea64d
    subnets:
      public:
        - id: subnet-014202eeb2cb3605d
        - id: subnet-07acde3231fa92a73
      private:
        - id: subnet-00e9fe236ca7ea641
        - id: subnet-0d339630a5cb0e319

# Configure the load balancers in your environment, once created.
# http:
#   public:
#   private:

# Configure observability for your environment resources.
observability:
  container_insights: false
